<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->
    <title><%= webpackConfig.name %></title>
    <script>
       /**
         * 屏幕尺寸适配
         */
         function fnResize(){
            var nWinWidth = window.innerWidth,
                nWinHeight =  window.innerHeight,

                STANDARD_WIDTH = 1920,
                STANDARD_HEIGHT = 1080
            ;
            // if(nWinWidth / nWinHeight > STANDARD_WIDTH / STANDARD_HEIGHT){//胖子，按高度适配
            //     document.getElementsByTagName("html")[0].style["fontSize"] = ((nWinHeight / STANDARD_HEIGHT) * 16) + "px";
            // } else {//瘦子，按宽度适配
                document.getElementsByTagName("html")[0].style["fontSize"] = ((nWinWidth / STANDARD_WIDTH) * 16) + "px";
            // }
        }
        fnResize();
        window.onresize = fnResize;
    </script>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
