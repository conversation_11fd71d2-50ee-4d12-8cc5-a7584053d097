// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:#303149;
$menuActiveText:#005EF8;
$subMenuActiveText:#303149; // https://github.com/ElemeFE/element/issues/12951

$menuBg:#EFF5FA;
$menuHover:#dce4ec;

$subMenuBg:#EFF5FA;
$subMenuHover:#dce4ec;

$sideBarWidth: 210px;
$topBarHeight: 58px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
