@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-variables.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './topbar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 15px;
  .el-pagination {
    margin-top: 10px;
  }
  // 搜索表单
  .search-form {
    .el-form-item--small.el-form-item {
      margin-bottom: 10px !important;
    }
    .el-date-editor.el-input {
      width: 100%;
    }
  }
  // 列表页的表格
  .list-table {
    margin-top: 10px;
    th.el-table__cell {
      background-color: #f5f7fa !important;
    }
  }
  .oprate-dialog {
    .el-dialog__body {
      padding: 10px 20px 10px 20px;
    }
  }
}
.dialog-footer {
  text-align: right;
}
// 操作表单
.oprate-form {
  &.oprate-page {
    padding: 15px;
    background-color: #f9fafc;
  }
  .el-form-item__label {
    font-size: 13px;
    font-weight: normal;
  }
  // padding-right: 30px;
  .el-select {
    // width: 100%;
    display: block;
  }
  .el-form-item__content {
    line-height: 31px;
    // .el-input, .el-date-editor, .el-textarea {
    //   width: auto;
    // }
    .el-input-group--append {
      .el-input__inner {
        padding: 0 10px;
      }
      .el-input-group__append {
        padding: 0 10px;
      }
    }
  }

  .big-textarea.el-textarea {
    width: 400px;
  }
}
.flex-content {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  button {
    height: 31.5px;
    box-sizing: border-box;
  }
}

// 去掉上下箭头
input[type="number"] {
  -moz-appearance: textfield;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }

  .search-view {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    // margin-left: -20px;
    .el-form-item__label {
      font-weight: 400;
      margin-left: 10px;
      font-size: 13px;
    }
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.t-border {
  border-top: 1px solid #DCDFE6;
}
.b-border {
  border-bottom: 1px solid #DCDFE6;
}
.l-border {
  border-left: 1px solid #DCDFE6;
}
.r-border {
  border-right: 1px solid #DCDFE6;
}
.mt5 {
  margin-top: 5px;
}
.mr5 {
  margin-right: 5px;
}
.mb5 {
  margin-bottom: 5px;
}
.ml5 {
  margin-left: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mt15 {
  margin-top: 15px;
}
.mr15 {
  margin-right: 15px;
}
.mb15 {
  margin-bottom: 15px;
}
.ml15 {
  margin-left: 15px;
}
.mt20 {
  margin-top: 20px;
}
.mr20 {
  margin-right: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mt30 {
  margin-top: 30px;
}
.mr30 {
  margin-right: 30px;
}
.mb30 {
  margin-bottom: 30px;
}
.ml30 {
  margin-left: 30px;
}
.c-success {
  color: $--color-success !important;
}
.c-primary {
  color: $--color-primary !important;
}
.c-warning {
  color: $--color-warning !important;
}
.c-danger {
  color: $--color-danger !important;
}
.c-gray {
  color: #999 !important;
}
.collapse-module {
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
  .el-collapse-item__header {
    position: relative;
    .collapse-title-pre {
      padding-left: 10px;
      &::before {
        content: '';
        position: relative;
        top: -1px;
        background-color: $--color-primary;
        width: 4px;
        height: 15px;
        vertical-align: middle;
        // line-height: 22px;
        border-radius: 3px;
        display: inline-block;
        margin-left: 8px;
        margin-right: 10px;
      }
    }
    .collapse-title-right {
      position: absolute;
      right: 30px;
    }
  }
  .el-collapse-item__header.is-active {
    // border-bottom-color: transparent;
    border-bottom-color: #e6ebf5;
  }
  .el-collapse-item__content {
    padding: 10px;
  }
}

.el-textarea.is-disabled .el-textarea__inner,
.el-input.is-disabled,
.el-input.is-disabled .el-input__inner {
  color: #333;
}
.no-text-radio {
  // color: transparent !important;
  .el-radio__label {
    display: none;
  }
}
