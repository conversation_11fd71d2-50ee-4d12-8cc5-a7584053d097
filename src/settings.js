
let isStaging = location.origin.indexOf('acar168') > -1
if (window.localStorage && window.localStorage.isStaging) {
  isStaging = parseInt(window.localStorage.isStaging)
}
module.exports = {
  title: isStaging ? '直播平台 商家端' : '涂划算 商家端',
  logo: isStaging ? '' : require('@/assets/login-badge.png'),

  /**
   * @type {boolean} true | false
   * @description Whether show the settings right-panel
   */
  showSettings: true,

  /**
   * @type {boolean} true | false
   * @description Whether need tagsView
   */
  tagsView: false,

  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the logo in sidebar
   */
  sidebarLogo: true,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production',
  // 应用类型：enterprise商家端 | manager管理端
  siteType: 'enterprise',

  // 是否测试环境
  isStaging
}
window.document.title = module.exports.title
