<template>
  <div class="log-menu">
    <img v-if="$settings.logo" :src="$settings.logo" class="topbar-logo">
    <span class="log">{{ $settings.title }}</span>
  </div>
</template>

<script>
export default {
  name: 'Topbar<PERSON><PERSON>',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
    }
  }
}
</script>

<style lang="scss" scoped>
.topbarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.topbarLogoFade-enter,
.topbarLogoFade-leave-to {
  opacity: 0;
}

.topbar-logo-container {
  position: relative;
  float:left;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .topbar-logo-link {
    height: 100%;
    width: 100%;

    & .topbar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .topbar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .topbar-logo {
      margin-right: 0px;
    }
  }
}
</style>
