<template>
  <div class="top-nav">
    <logo :collapse="false" />
    <!-- <el-menu
      :active-text-color="variables.menuActiveText"
      :default-active="activeMenu"
      mode="horizontal"
      @select="handleSelect"
    >
      <div v-for="item in permission_routes" :key="item.path" class="nav-item">
        <app-link :to="resolvePath(item)">
          <el-menu-item
            v-if="!item.hidden"
            :index="item.path"
          >{{ item.meta ? item.meta.title : item.children[0].meta.title }}</el-menu-item>
        </app-link>
      </div>
    </el-menu> -->

    <div class="right-menu">
      <!-- <template>

        <search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="Global Size" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

      </template> -->
      <template>
        <el-badge :value="unReadCount" class="notice-badge" :hidden="unReadCount === 0" :max="99">
          <router-link to="/mine/notice">
            <i class="el-icon-message" />
          </router-link>
        </el-badge>
        <div class="right-menu-item" style="font-size: 15px;">
          你好，{{ name }}
        </div>
      </template>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="defaultImg" :onerror="errorAvatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <router-link to="/profile/index">
            <el-dropdown-item>Profile</el-dropdown-item>
          </router-link>
          <router-link to="/notice/log/mine/index">
            <el-dropdown-item>Message</el-dropdown-item>
          </router-link>
          <router-link to="/">
            <el-dropdown-item>Dashboard</el-dropdown-item>
          </router-link>
          <a target="_blank" href="https://github.com/PanJiaChen/vue-element-admin/">
            <el-dropdown-item>Github</el-dropdown-item>
          </a>
          <a target="_blank" href="https://panjiachen.github.io/vue-element-admin-site/#/">
            <el-dropdown-item>Docs</el-dropdown-item>
          </a> -->
          <!-- <el-dropdown-item @click.native="$router.push('/mine/notice')">
            <span style="display:block;">我的消息</span>
          </el-dropdown-item> -->
          <el-dropdown-item @click.native="updatePwdDialog.isShow=true">
            <span style="display:block;">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog :title="updatePwdDialog.title" :visible.sync="updatePwdDialog.isShow" :close-on-click-modal="false" append-to-body width="500px">
        <el-form ref="updatePwdForm" :model="updatePwdInfo" :rules="updatePwdRules" label-width="100px">
          <el-form-item label="原密码" prop="oldPwd">
            <el-input v-model="updatePwdInfo.oldPwd" type="password" clearable placeholder="请输入原密码" style="width:300px" />
          </el-form-item>
          <el-form-item label="新密码" prop="newPwd">
            <el-input v-model="updatePwdInfo.newPwd" type="password" clearable placeholder="请输入新密码" style="width:300px" />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPwd">
            <el-input v-model="updatePwdInfo.confirmPwd" type="password" clearable placeholder="请确认密码" style="width:300px" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button plain type="primary" @click="updatePwdDialog.isShow = false">取 消</el-button>
          <el-button type="primary" @click="updatePwdDetailClick">保 存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import AppLink from '../Sidebar/Link'
import { constantRoutes } from '@/router'
import variables from '@/styles/variables.scss'
import { isExternal } from '@/utils/validate'
import Logo from './Logo'
import md5 from '@/utils/md5'
// import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
// import Search from '@/components/HeaderSearch'
import { updatePwd } from '@/api/user'
import { mapState } from 'vuex'
import defaultImg from '@/assets/defualt_avatar.jpeg'

export default {
  name: 'Topbar',
  components: {
    // AppLink,
    // Screenfull,
    // SizeSelect,
    // Search,
    Logo
  },
  data() {
    return {
      routes: constantRoutes,
      defaultImg,
      errorAvatar: 'this.src="' + defaultImg + '"',
      updatePwdDialog: {
        title: '修改密码',
        isShow: false
      },
      updatePwdInfo: {
        oldPwd: null,
        newPwd: null,
        confirmPwd: null
      },
      updatePwdRules: {
        oldPwd: [{
          required: true, message: '请输入原密码', trigger: 'blur'
        }],
        newPwd: [{
          required: true, message: '请输入新密码', trigger: 'blur'
        }],
        confirmPwd: [{
          required: true, message: '请确认密码', trigger: 'blur'
        }]
      }
    }
  },
  computed: {
    ...mapState({
      unReadCount: state => state.user.unReadCount,
    }),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      // 如果是首页，首页高亮
      if (path === '/dashboard') {
        return '/'
      }
      // 如果不是首页，高亮一级菜单
      const activeMenu = '/' + path.split('/')[1]
      return activeMenu
    },
    variables() {
      return variables
    },
    sidebar() {
      return this.$store.state.app.sidebar
    },
    ...mapGetters(['avatar', 'permission_routes', 'name', 'device'])
  },
  watch: {
    // $route: {
    //   handler: function() {
    //     this.initCurrentRoutes()
    //   },
    //   immediate: true
    // }
  },
  activated() {
    // this.initCurrentRoutes()
  },
  mounted() {
    this.$store.dispatch('user/getUnReadNoticeCount')
    // this.initCurrentRoutes()
  },
  methods: {
    // 通过当前路径找到二级菜单对应项，存到store，用来渲染左侧菜单
    initCurrentRoutes() {
      const { path } = this.$route
      console.log(this.$route)
      let route = this.permission_routes.find(
        item => item.path === '/' + path.split('/')[1]
      )
      // 如果找不到这个路由，说明是首页
      if (!route) {
        route = this.permission_routes.find(item => item.path === '/')
      }
      console.log('permission_routes', this.permission_routes)
      this.$store.commit('permission/SET_CURRENT_ROUTES', route)
      this.setSidebarHide(route)
    },
    // 判断该路由是否只有一个子项或者没有子项，如果是，则在一级菜单添加跳转路由
    isOnlyOneChild(item) {
      if (item.children && item.children.length === 1) {
        return true
      }
      return false
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          this.onlyOneChild = item
          return true
        }
      })

      if (showingChildren.length === 0 || showingChildren.length === 1) {
        return true
      }
      return false
    },
    resolvePath(item) {
      // 如果是个完成的url直接返回
      if (isExternal(item.path)) {
        return item.path
      }
      // 如果是首页，就返回重定向路由
      if (item.path === '/') {
        const path = item.redirect
        return path
      }

      // 如果有子项，默认跳转第一个子项路由
      let path = ''
      /**
       * item 路由子项
       * parent 路由父项
       */
      const getDefaultPath = (item, parent) => {
        // 如果path是个外部链接（不建议），直接返回链接，存在个问题：如果是外部链接点击跳转后当前页内容还是上一个路由内容
        if (isExternal(item.path)) {
          path = item.path
          return
        }
        // 第一次需要父项路由拼接，所以只是第一个传parent
        if (parent) {
          path += (parent.path + '/' + item.path)
        } else {
          path += ('/' + item.path)
        }
        // 如果还有子项，继续递归
        if (item.children) {
          getDefaultPath(item.children[0])
        }
      }

      if (item.children) {
        getDefaultPath(item.children[0], item)
        return path
      }

      return item.path
    },
    handleSelect(key, keyPath) {
      // 把选中路由的子路由保存store
      const route = this.permission_routes.find(item => item.path === key)
      this.$store.commit('permission/SET_CURRENT_ROUTES', route)
      this.setSidebarHide(route)
    },
    // 设置侧边栏的显示和隐藏
    setSidebarHide(route) {
      if (this.hasOneShowingChild(route.children)) {
        this.$store.dispatch('app/toggleSideBarHide', true)
      } else {
        this.$store.dispatch('app/toggleSideBarHide', false)
      }
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      this.$router.push(`/login`)
    },
    updatePwdDetailClick() {
      this.$refs.updatePwdForm.validate((valid) => {
        if (valid) {
          if (this.updatePwdInfo.newPwd !== this.updatePwdInfo.confirmPwd) {
            this.$message.error('新密码与确认密码不一致')
            return
          }
          updatePwd({
            oldPwd: md5(this.updatePwdInfo.oldPwd),
            newPwd: md5(this.updatePwdInfo.newPwd)
          }).then(response => {
            this.updatePwdDialog.isShow = false
            this.$message.success('修改密码成功,请重新登陆')
            setTimeout(() => {
              this.logout()
            }, 3000)
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.notice-badge {
  height: 100%;
  margin-right: 15px;
  top: -5px;
  ::v-deep .el-badge__content {
    line-height: 15px;
  }
  .el-icon-message {
    color: #fff;
    font-size: 21px;
  }
}
</style>

