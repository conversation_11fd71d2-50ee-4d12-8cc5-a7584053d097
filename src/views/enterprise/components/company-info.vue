<template>
  <div>
    <el-table
      class="list-table"
      :data="formData.companies"
      :stripe="true"
      border
      style="width: 100%;"
    >
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        prop="companyName"
        label="公司名称"
        min-width="200"
      />
      <el-table-column
        prop="unifiedSocialCreditCode"
        label="统一社会信用代码"
      />
      <el-table-column
        label="操作"
        width="80"
      >
        <template slot-scope="scope">
          <el-button class="pd0" size="mini" type="text" @click="editHandel(scope.$index)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="dialog-footer mt10">
      <el-button type="primary" @click="editHandel()">新增公司</el-button>
      <!-- <el-button plain type="primary" @click="$router.go(-1)">返回</el-button> -->
    </div>

    <CompanyAdd ref="companyAddAdd" @success="onSuccess" />
  </div>
</template>
<script>
import CompanyAdd from '@/components/Company/CompanyAdd'

export default {
  components: {
    CompanyAdd
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    editHandel(index) {
      this.$refs.companyAddAdd.show(this.formData.companies, index)
    },
    onSuccess(data) {
      this.$emit('success', data)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
