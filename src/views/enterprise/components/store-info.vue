<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-27 09:57:37
-->
<template>
  <div>
    <el-table
      class="list-table"
      :data="formData.stores"
      :stripe="true"
      border
      style="width: 100%;"
    >
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        prop="dyStoreId"
        label="店铺ID"
        min-width="50"
      />
      <el-table-column
        prop="dyStoreUid"
        label="店铺UID"
        min-width="50"
      />
      <el-table-column
        prop="storeName"
        label="店铺名称"
        min-width="50"
      />
      <el-table-column
        prop="storeLink"
        label="店铺链接"
      />
      <el-table-column
        prop="storeRating"
        label="店铺评分"
      />
      <el-table-column
        label="操作"
        width="80"
      >
        <template slot-scope="scope">
          <el-button class="pd0" size="mini" type="text" @click="editHandel(scope.$index)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="dialog-footer mt10">
      <el-button type="primary" @click="editHandel()">新增店铺</el-button>
      <!-- <el-button plain type="primary" @click="$router.go(-1)">返回</el-button> -->
    </div>

    <StoreAdd ref="storeAdd" @success="onSuccess" />
  </div>
</template>
<script>
import StoreAdd from '@/components/Store/StoreAdd'

export default {
  components: {
    StoreAdd
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    editHandel(index) {
      this.$refs.storeAdd.show(this.formData.stores, index)
    },
    onSuccess(data) {
      this.$emit('success', data)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
