<template>
  <div>
    <template slot="title">
      <div class="collapse-title-pre">商户信息</div>
    </template>
    <el-form v-if="formData.companies.length && formData.stores.length" ref="form" class="app-container oprate-form" :model="formData" :rules="rules" label-width="130px">
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="商户编号"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              clearable
              disabled
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="公司名称"
            prop="companies[0].companyName"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.companies[0].companyName"
                clearable
                placeholder="请输入"
              />
              <CompanyChoose disabled>
                <el-button type="primary" plain class="ml5" style="width: 55px;">更多</el-button>
              </CompanyChoose>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="统一社会信用代码"
            prop="companies[0].unifiedSocialCreditCode"
          >
            <el-input
              v-model="formData.companies[0].unifiedSocialCreditCode"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="店铺ID"
            prop="stores[0].dyStoreId"
          >
            <el-input
              v-model="formData.stores[0].dyStoreId"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="店铺UID"
            prop="stores[0].dyStoreUid"
          >
            <el-input
              v-model="formData.stores[0].dyStoreUid"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="店铺名称"
            prop="stores[0].storeName"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.stores[0].storeName"
                clearable
                placeholder="请输入"
              />
              <StoreChoose disabled>
                <el-button type="primary" plain class="ml5" style="width: 55px;">更多</el-button>
              </StoreChoose>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="店铺链接"
            prop="stores[0].storeLink"
            style="width: 100%"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.stores[0].storeLink"
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.store.storeLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="品牌"
            prop="brand"
          >
            <el-input
              v-model="formData.brand"
              clearable
              placeholder=""
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="品牌联系人姓名"
            prop="contactName"
          >
            <el-input
              v-model="formData.contactName"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="品牌联系人手机号"
            prop="contactPhoneNo"
          >
            <el-input
              v-model="formData.contactPhoneNo"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { validPhone } from '@/utils/validate'
import StoreChoose from '@/components/Store/StoreChoose'
import CompanyChoose from '@/components/Company/CompanyChoose'

export default {
  components: { CompanyChoose, StoreChoose },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      rules: {
        // contactName: [
        //   { required: true, message: '请输入联系人姓名', trigger: 'change' },
        // ],
        contactPhoneNo: [
          { required: false, message: '请输入联系手机号', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPhone(this.formData.contactPhoneNo)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ]
      }
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
