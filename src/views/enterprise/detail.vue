<!--
 * @Author: dong<PERSON>och<PERSON>@163.com
 * @Date: 2023-11-27 09:57:37
-->
<template>
  <div class="app-container">
    <el-tabs type="border-card" class="mt15">
      <el-tab-pane label="商户信息">
        <EnterpriseInfo ref="enterpriseInfoRef" :form-data="formData" />
        <div class="dialog-footer mt10">
          <el-button :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane label="店铺信息">
        <StoreInfo :form-data="formData" @success="pullData" />
      </el-tab-pane>
      <el-tab-pane label="公司信息">
        <CompanyInfo :form-data="formData" @success="pullData" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EnterpriseInfo from './components/enterprise-info'
import StoreInfo from './components/store-info'
import CompanyInfo from './components/company-info'
import enterpriseApis from '@/api/enterprise'
import detailMixin from '@/mixins/detailMixin'

const defaultFormData = {
  company: {},
  store: {},
  companies: [],
  stores: []
}
export default {
  components: { EnterpriseInfo, StoreInfo, CompanyInfo },
  mixins: [detailMixin],
  data() {
    return {
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      apis: {
        ...enterpriseApis,
      },
      rules: {
        monitorStationTypeNo: [
          { required: true, message: '请选择站点分类', trigger: 'change' }
        ],
        no: [
          { required: true, message: '请输入编号', trigger: 'change' }
        ],
        name: [{
          required: true, message: '请输入名称', trigger: 'change'
        }],
        enableFlag: [{ required: true, message: '请选择是否启用', trigger: 'change' }]
      },
    }
  },
  computed: {
  },
  mounted() {
    this.pullData(false)
  },
  methods: {
    pullData() {
      this.isLoading = true
      this.apis.getDetail().then(response => {
        this.formData = { ...defaultFormData, ...response.data }
      }).finally(() => {
        this.isLoading = false
      })
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.isSubmiting = true
      this.apis.update(this.formData).then(async res => {
        await this.$store.dispatch('user/getInfo')
        this.$message.success('保存成功')
        this.$emit('success', res.data)
        // this.close()
      }).finally(() => {
        this.isSubmiting = false
      })
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.enterpriseInfoRef.validate()
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
