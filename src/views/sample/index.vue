<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item label="快递单号" prop="trackingNo">
            <el-input v-model="searchForm.trackingNo" clearable placeholder="请输入快递单号" />
          </el-form-item>
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="searchForm.companyName" clearable placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="商品名称" prop="skuName">
            <el-input v-model="searchForm.skuName" clearable placeholder="请输入商品名称" />
          </el-form-item>
          <!-- <el-form-item label="品类" prop="productCategoryCode">
            <ProductCategorySelect v-model="searchForm.productCategoryCode" />
          </el-form-item> -->
          <el-form-item
            label="抖音商品类目"
            prop="dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="searchForm.dyProductCategories" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
            <el-button
              v-if="!$isAdmin && !canChoose"
              type="primary"
              icon="el-icon-plus"
              plain
              @click="$router.push({
                name: 'SampleAdd'
              })"
            >新增</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- <el-button type="primary" @click="addHandle">添加</el-button> -->
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <template v-if="canChoose">
        <el-table-column
          v-if="multiple"
          fixed
          type="selection"
          label="选择"
          align="center"
          min-width="45"
        />
        <el-table-column
          v-else
          fixed
          label="选择"
          align="center"
          min-width="45"
        >
          <template slot-scope="scope">
            <el-radio :value="selectItems.length ? selectItems[0].id : ''" :label="scope.row.id" class="no-text-radio" />
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="单据编号"
        prop="code"
        min-width="130"
      />
      <el-table-column
        label="商户编号"
        prop="merchantCode"
        min-width="120"
      />
      <el-table-column
        label="公司名称"
        prop="companyName"
        min-width="200"
      />
      <el-table-column
        label="对接商务"
        prop="counterPartBusinessName"
        min-width="90"
      />
      <el-table-column
        label="品牌"
        prop="brand"
        min-width="110"
      />
      <el-table-column
        label="店铺名称"
        prop="storeName"
        min-width="110"
      />
      <el-table-column
        label="商品名称"
        prop="skuName"
        min-width="250"
      />
      <!-- <el-table-column
        label="商品类目"
        prop="productCategoryName"
        min-width="150"
      /> -->
      <el-table-column
        label="抖音商品类目"
        prop="dyProductCategories"
        min-width="130"
      >
        <template slot-scope="scope">
          {{ (scope.row.dyProductCategories || []).map(item => item.name).join('、') }}
        </template>
      </el-table-column>
      <el-table-column
        label="快递单号"
        prop="trackingNo"
        min-width="140"
      />
      <el-table-column
        label="寄出时间"
        prop="shippingTime"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.shippingTime | parseDate }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        prop="status"
        min-width="90"
        fixed="right"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('sampleStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        label="补寄状态"
        prop="status"
        min-width="90"
        fixed="right"
      >
        <template slot-scope="scope">
          {{ {
          1: '待补寄',
          2: '已补寄'
        }[scope.row.resendStatus] }}
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push({
              name: `SampleDetail`,
              query: {
                id: scope.row.id
              }
            })"
          >查看</el-button>
          <el-button
            v-if="!$isAdmin && scope.row.resendStatus===1"
            type="text"
            size="small"
            @click="$router.push({
              name: `SampleResend`,
              query: {
                id: scope.row.id
              }
            })"
          >补寄</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <!-- <el-dialog :title="oprateDialog.title" :visible.sync="oprateDialog.isShow" :close-on-click-modal="false" class="oprate-dialog" width="850px">
      <detail v-if="oprateDialog.isShow" v-model="oprateDialog.isShow" :dialog-data="oprateDialog.data" :dialog-type="oprateDialog.type" @success="pullData" />
    </el-dialog> -->
  </div>
</template>

<script>
import ProductCategorySelect from '@/components/ProductCategorySelect'
import listMixin from '@/mixins/listMixin'
import sampleApi from '@/api/sample'

export default {
  name: 'Sample',
  components: { ProductCategorySelect },
  mixins: [listMixin],
  props: {},
  data() {
    return {
      searchForm: {
        trackingNo: null,
        companyName: null,
        skuName: null,
        productCategoryCode: null,
        dyProductCategories: [],
      },
      // 增删改查集合
      apis: {
        ...sampleApi
      }
    }
  },
  computed: {
  },
  mounted() {
    this.pullData()
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
