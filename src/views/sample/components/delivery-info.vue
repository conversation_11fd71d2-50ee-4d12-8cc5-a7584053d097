<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">快递信息</div>
    </template>
    <el-form ref="form" :model="formData" :rules="rules" :disabled="!canEdit" label-position="right" label-width="100px">
      <el-row>
        <el-col :span="8">
          <el-form-item
            label="快递公司"
            prop="courierCompany"
          >
            <el-input
              v-model="formData.courierCompany"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="快递单号"
            prop="trackingNo"
          >
            <el-input
              v-model="formData.trackingNo"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="包裹数"
            prop="quantity"
          >
            <el-input
              v-model="formData.quantity"
              clearable
              type="number"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item
            label="寄出时间"
            prop="shippingTime"
          >
            <el-date-picker
              v-model="formData.shippingTime"
              type="date"
              placeholder="选择日期"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="需退回"
            prop="needToReturn"
          >
            <el-checkbox v-model="formData.needToReturn" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="文字说明"
            prop="illustrate"
          >
            <el-input
              v-model="formData.illustrate"
              clearable
              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-if="formData.needToReturn" :span="12">
          <el-form-item
            label="快递信息"
            prop="information"
          >
            <el-input
              v-model="formData.information"
              clearable
              type="textarea"
              placeholder="如需退回，请填写快递信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="files"
          >
            <div v-if="!$isAdmin" slot="label">
              相关附件
              <!-- <el-button size="small" type="primary" @click="$refs.formAttachmentRef.choosePics()">
                <div>历史</div>
                <div class="mt5">附件</div>
              </el-button> -->
            </div>
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :files="formData.files"
                :product-category-code-list="['YP']"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import FormAttachment from '@/components/FormAttachment'
export default {
  components: { FormAttachment },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      rules: {
        information: [{ required: true, message: '请填写快递信息', trigger: 'change' }],
        quantity: [{ required: true, message: '请填写数量', trigger: 'change' }],
        shippingTime: [{ required: true, message: '请选择寄出时间', trigger: 'change' }],
        trackingNo: [{ required: true, message: '请填写快递单号', trigger: 'change' }],
        courierCompany: [{ required: true, message: '快递公司', trigger: 'change' }],
      },
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
