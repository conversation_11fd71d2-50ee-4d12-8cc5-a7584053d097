<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :rules="rules" :disabled="!canEdit" label-position="right" label-width="100px">
      <el-row>
        <el-col v-show="!canEdit" :span="6">
          <el-form-item
            label="单据编号"
            prop="baseInfo.code"
          >
            <el-input
              v-model="formData.baseInfo.code"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="样品名称"
            prop="name"
          >
            <el-input
              v-model="formData.name"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="公司名称"
            prop="baseInfo.companyName"
          >
            <el-input
              v-model="formData.baseInfo.companyName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="对接商务"
            prop="baseInfo.counterPartBusinessName"
          >
            <el-input
              v-model="formData.baseInfo.counterPartBusinessName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌"
            prop="baseInfo.brand"
          >
            <el-input
              v-model="formData.baseInfo.brand"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="店铺名称"
            prop="baseInfo.storeName"
          >
            <el-input
              v-model="formData.baseInfo.storeName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.baseInfo && formData.baseInfo.code">
        <el-col :span="8">
          <el-form-item
            label="样品单号"
            prop="baseInfo.code"
          >
            <el-input
              v-model="formData.baseInfo.code"
              disabled
              clearable
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="样品状态"
            prop="baseInfo.status"
          >
            {{ formData.baseInfo.status | toEnumName('sampleStatus') }}
          </el-form-item>
        </el-col>
        <el-col v-if="formData.baseInfo.resendStatus > 0" :span="8">
          <el-form-item
            label="补寄状态"
            prop="baseInfo.resendStatus"
          >
            {{ {
              1: '待补寄',
              2: '已补寄'
            }[formData.baseInfo.resendStatus] }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import ProductChooseDialog from '@/components/ChooseDialogs/ProductChooseDialog'
import ProductCategorySelect from '@/components/ProductCategorySelect'
import { toEnumName } from '@/filters'
// import Enterprise from '@/views/enterprise/index.vue'
export default {
  components: { ProductChooseDialog, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    baseData: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // Enterprise,
      rules: {
        'name': [{ required: true, message: '请填写样品名称', trigger: 'change' }],
      },
    }
  },
  computed: {
  },
  watch: {
    // 监听SKU数据变化，自动同步到基础信息对象
    'formData.sku': {
      handler(newSku, oldSku) {
        if (newSku && Object.keys(newSku).length > 0) {
          // 确保baseInfo对象存在
          if (!this.formData.baseInfo) {
            this.$set(this.formData, 'baseInfo', {})
          }

          // 从SKU数据中提取基础信息，统一更新到baseInfo对象
          const baseInfoUpdates = {}

          if (newSku.companyName) {
            baseInfoUpdates.companyName = newSku.companyName
          }
          if (newSku.merchantCode) {
            baseInfoUpdates.merchantCode = newSku.merchantCode
          }
          if (newSku.brand) {
            baseInfoUpdates.brand = newSku.brand
          }
          if (newSku.storeName) {
            baseInfoUpdates.storeName = newSku.storeName
          }
          if (newSku.counterPartBusinessName) {
            baseInfoUpdates.counterPartBusinessName = newSku.counterPartBusinessName
          }

          // 批量更新baseInfo对象
          Object.keys(baseInfoUpdates).forEach(key => {
            this.$set(this.formData.baseInfo, key, baseInfoUpdates[key])
          })

          // 如果需要查询更多信息，可以在这里调用API
          this.fetchAdditionalInfo(newSku)
        }
      },
      deep: true,
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    toEnumName,
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
    // 根据SKU信息查询补充的基础信息
    async fetchAdditionalInfo(skuData) {
      try {
        // 如果需要根据SKU查询更多基础信息，可以在这里调用API
        // 例如：根据merchantCode查询更详细的商户信息
        if (skuData.merchantCode) {
          // const merchantInfo = await merchantApi.getDetail(skuData.merchantCode)
          // 更新更多基础信息字段
          // this.$set(this.formData.baseInfo, 'createTime', merchantInfo.createTime)
          // this.$set(this.formData.baseInfo, 'status', merchantInfo.status)
        }
      } catch (error) {
        console.error('获取补充信息失败:', error)
      }
    },
    chooseProduct() {
      this.$refs.productChooseDialogRef.show()
    },
    onChosedProduct(items) {
      if (items.length > 0) {
        this.$set(this.formData, 'sku', items[0])
        this.$set(this.formData, 'skuId', items[0].id)
      } else {
        this.$set(this.formData, 'sku', {})
        this.$set(this.formData, 'skuId', '')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
