<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :rules="rules" :disabled="!canEdit" label-position="right" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="公司名称"
            prop="companyName"
          >
            <el-input
              :value="formData.merchantCompany ? formData.merchantCompany.companyName : ''"
              clearable
              disabled
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="对接商务"
            prop="merchant.counterPartBusinessName"
          >
            <el-input
              v-model="formData.merchant.counterPartBusinessName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌"
            prop="sku.brand"
          >
            <el-input
              v-model="formData.sku.brand"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="店铺名称"
            prop="storeName"
          >
            <el-input
              v-model="formData.storeName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="商户编号"
            prop="merchantCode"
          >
            <el-input
              v-model="formData.merchantCode"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="创建时间"
            prop="createTime"
          >
            <el-input
              v-model="formData.createTime"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import ProductChooseDialog from '@/components/ChooseDialogs/ProductChooseDialog'
import ProductCategorySelect from '@/components/ProductCategorySelect'
import { toEnumName } from '@/filters'
// import Enterprise from '@/views/enterprise/index.vue'
export default {
  components: { ProductChooseDialog, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    baseData: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // Enterprise,
      rules: {
        'sku.code': [{ required: true, message: '请选择sku', trigger: 'change' }],
      },
    }
  },
  computed: {
  },
  watch: {
    'baseData.merchant': {
      handler(newValue, oldValue) {
        this.$set(this.formData, 'merchantCompanyName', newValue?.company?.companyName)
      },
      deep: true,
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    toEnumName,
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
    chooseProduct() {
      this.$refs.productChooseDialogRef.show()
    },
    onChosedProduct(items) {
      if (items.length > 0) {
        this.$set(this.formData, 'sku', items[0])
        this.$set(this.formData, 'skuId', items[0].id)
      } else {
        this.$set(this.formData, 'sku', {})
        this.$set(this.formData, 'skuId', '')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
