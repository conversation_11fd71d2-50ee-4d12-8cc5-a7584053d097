<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :rules="rules" :disabled="!canEdit" label-position="right" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="单据编号"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="公司名称"
            prop="merchant.company.companyName"
          >
            <el-input
              v-model="formData.merchant.company.companyName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="对接商务"
            prop="merchant.counterPartBusinessName"
          >
            <el-input
              v-model="formData.merchant.counterPartBusinessName"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌"
            prop="sku.brand"
          >
            <el-input
              v-model="formData.sku.brand"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="样品名称"
            prop="name"
          >
            <el-input
              v-model="formData.name"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="样品状态"
            prop="status"
          >
            {{ formData.status | toEnumName('sampleStatus') }}
          </el-form-item>
        </el-col>
        <el-col v-if="formData.resendStatus > 0" :span="6">
          <el-form-item
            label="补寄状态"
            prop="status"
          >
            {{ {
              1: '待补寄',
              2: '已补寄'
            }[formData.resendStatus] }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import ProductChooseDialog from '@/components/ChooseDialogs/ProductChooseDialog'
import ProductCategorySelect from '@/components/ProductCategorySelect'
import { toEnumName } from '@/filters'
// import Enterprise from '@/views/enterprise/index.vue'
export default {
  components: { ProductChooseDialog, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    baseData: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // Enterprise,
      rules: {
        'sku.code': [{ required: true, message: '请选择sku', trigger: 'change' }],
      },
    }
  },
  computed: {
  },
  watch: {
    'baseData.merchant': {
      handler(newValue, oldValue) {
        this.$set(this.formData, 'merchantCompanyName', newValue?.company?.companyName)
      },
      deep: true,
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    toEnumName,
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
    chooseProduct() {
      this.$refs.productChooseDialogRef.show()
    },
    onChosedProduct(items) {
      if (items.length > 0) {
        this.$set(this.formData, 'sku', items[0])
        this.$set(this.formData, 'skuId', items[0].id)
      } else {
        this.$set(this.formData, 'sku', {})
        this.$set(this.formData, 'skuId', '')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
