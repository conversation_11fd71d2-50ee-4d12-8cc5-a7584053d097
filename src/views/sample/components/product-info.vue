<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">sku信息</div>
      <!-- <div class="collapse-title-right">
        <el-button type="text" size="mini" @click.stop="handleAddMain">新增主品</el-button>
        <el-button type="text" size="mini" @click.stop="handleAddGift">新增赠品</el-button>
      </div> -->
    </template>
    <el-form ref="form" :model="formData" :rules="rules" :disabled="!canEdit" label-position="right" label-width="100px">
      <!-- <el-row v-if="$isAdmin">
        <el-col :span="6">
          <el-form-item
            label="公司名称"
            prop="merchantCompanyName"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.merchantCompanyName"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-button type="primary" style="width: 55px;" plain class="ml5" @click="chooseEnterprise">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="抖音商品ID"
            prop="sku.code"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.sku.code"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-button type="primary" plain class="ml5" :disabled="!canEdit" @click="chooseProduct">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="sku名称"
            prop="sku.name"
          >
            <el-input
              v-model="formData.sku.name"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品类"
            prop="sku.productCategoryCode"
          >
            <ProductCategorySelect v-model="formData.sku.productCategoryCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="抖音商品类目"
            prop="dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="formData.dyProductCategories" :limit="1" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌名称"
            prop="sku.brand"
          >
            <el-input
              v-model="formData.sku.brand"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            label="商品标签"
            prop="sku.label"
          >
            <el-select v-model="formData.sku.label" disabled placeholder="请选择" clearable>
              <el-option
                v-for="item in $enums['goodLabels'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="接受补贴"
            prop="sku.acceptSubsidies"
          >
            <el-checkbox v-model="formData.sku.acceptSubsidies" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="商品链接"
            prop="sku.skuLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.sku.skuLink"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" plain class="ml5" @click="$copy(formData.sku.skuLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ProductChooseDialog ref="productChooseDialogRef" type="product" :on-chosed="onChosedProduct" :limit="1" />
    <!-- <CommonChooseDialog ref="enterpriseChooseDialogRef" title="选择商家" :component="Enterprise" :on-chosed="onChosedEnterprise" :multiple="false" :init-search-params="{status: 2}" /> -->
  </el-collapse-item>
</template>

<script>
import ProductChooseDialog from '@/components/ChooseDialogs/ProductChooseDialog'
import ProductCategorySelect from '@/components/ProductCategorySelect'
// import Enterprise from '@/views/enterprise/index.vue'
export default {
  components: { ProductChooseDialog, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    baseData: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // Enterprise,
      rules: {
        'sku.code': [{ required: true, message: '请选择sku', trigger: 'change' }],
      },
    }
  },
  computed: {
  },
  watch: {
    'baseData.merchant': {
      handler(newValue, oldValue) {
        this.$set(this.formData, 'merchantCompanyName', newValue?.company?.companyName)
      },
      deep: true,
      immediate: true
    },
  },
  mounted() {
  },
  methods: {
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
    // chooseEnterprise() {
    //   this.$refs.enterpriseChooseDialogRef.show()
    // },
    // onChosedEnterprise(items) {
    //   if (items.length > 0) {
    //     this.$set(this.formData, 'merchantId', items[0].id)
    //     this.$set(this.formData, 'merchantCompanyId', items[0].companyId)
    //     this.$set(this.formData, 'merchantCompanyName', items[0].companyName)
    //   } else {
    //     this.$set(this.formData, 'merchantId', '')
    //     this.$set(this.formData, 'merchantCompanyId', '')
    //     this.$set(this.formData, 'merchantCompanyName', '')
    //   }
    // },
    chooseProduct() {
      this.$refs.productChooseDialogRef.show()
    },
    onChosedProduct(items) {
      if (items.length > 0) {
        this.$set(this.formData, 'sku', items[0])
        this.$set(this.formData, 'skuId', items[0].id)
      } else {
        this.$set(this.formData, 'sku', {})
        this.$set(this.formData, 'skuId', '')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
