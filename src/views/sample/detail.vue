<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="样品详情">
        <el-collapse v-model="activeNames">
          <BaseInfo ref="baseInfoRef" :form-data="formData" name="0" :can-edit="canEdit" />
          <ProductInfo ref="productInfoRef" :form-data="formData" name="1" :can-edit="canEdit" />
          <DeliveryInfo ref="deliveryInfoRef" :form-data="formData" name="2" :can-edit="canEdit" class="mt15" />
        </el-collapse>
        <div class="dialog-footer mt10">
          <template v-if="$isAdmin">
            <!-- 仓库管理员 -->
            <el-button v-if="$isAdmin && formData.status === 1" v-permission="'button:sample:transferredToBusiness'" :loading="isSubmiting" type="primary" @click="transferredToBusinessHandle">转商务</el-button>
            <el-button v-if="$isAdmin && formData.status === 1" v-permission="'button:sample:receipt'" :loading="isSubmiting" type="primary" @click="receiptHandle">签 收</el-button>
            <!-- 商务 -->
            <el-button v-if="$isAdmin && formData.status === 5 && formData.resendStatus < 1" v-permission="'button:sample:resend'" :loading="isSubmiting" type="primary" @click="reissueHandle">通知补寄</el-button>
            <template v-if="formData.status === 2 || formData.status === 5">
              <AuditBtn v-permission="'button:sample:businessAudit'" level="businessAudit" :audit-api="apis.businessAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
              <AuditBtn v-permission="'button:sample:operationsAudit'" level="operationsAudit" :audit-api="apis.operationsAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
            </template>
          </template>
          <template v-else>
            <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
            <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
            <el-button
              v-if="canEdit && formData.resendStatus===1"
              :loading="isSubmiting"
              type="primary"
              @click="$router.push({
                name: `ResendDetail`,
                query: {
                  id: scope.row.id
                }
              })"
            >补 寄</el-button>
          </template>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
        <AuditRecord :data="formData.auditRecords" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import sampleApi from '@/api/sample'
import ProductInfo from './components/product-info'
import DeliveryInfo from './components/delivery-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import detailMixin from '@/mixins/detailMixin'
import BaseInfo from "@/views/sample/components/base-info.vue";
const defaultFormData = {
  // 基础信息对象
  baseInfo: {
    code: null,
    status: null,
    name: null,
    resendStatus: null,
    merchantCode: null,
    storeName: null,
    createTime: null,
    companyName: null,
    counterPartBusinessName: null,
    brand: null
  },
  // 商品信息相关
  sku: {
    productCompanyName: null,
    brand: null,
    name: null,
    productCategoryCode: null,
    label: null,
    acceptSubsidies: null,
    skuLink: null,
  },
  // 快递信息相关
  courierCompany: null,
  trackingNo: null,
  quantity: null,
  shippingTime: null,
  illustrate: null,
  needToReturn: false,
  files: [],
  auditRecords: [],
}

export default {
  components: {BaseInfo, ProductInfo, DeliveryInfo, AuditRecord },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['0', '1', '2'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      auditLevel: 1,
      // 增删改查集合
      apis: {
        ...sampleApi
      },
      auditApi: null,
      auditTitle: '',
    }
  },
  computed: {
    // 状态：0保存；1已提交；2已签收；3审核通过；4审核不通过
    canEdit() {
      return (this.isAdd || [0, 4].indexOf(this.formData.status) > -1) && !this.$isAdmin
    },
  },
  mounted() {
    this.pullData()
  },
  methods: {
    // 重写detailMixin中的dataUpdateCallback
    dataUpdateCallback() {
      debugger
      this.processFormData()
    },
    // 数据处理：将旧数据结构迁移到新的baseInfo结构
    processFormData() {
      debugger
      // 确保baseInfo对象存在
      if (!this.formData.baseInfo) {
        this.$set(this.formData, 'baseInfo', {})
      }

      // 迁移基础字段到baseInfo对象
      const fieldsToMigrate = ['code', 'status', 'name', 'resendStatus', 'merchantCode', 'storeName', 'createTime']
      fieldsToMigrate.forEach(field => {
        if (this.formData[field] !== undefined && this.formData.baseInfo[field] === undefined) {
          this.$set(this.formData.baseInfo, field, this.formData[field])
        }
      })

      // 迁移merchant相关字段
      if (this.formData.merchant) {
        if (this.formData.merchant.company && this.formData.merchant.company.companyName) {
          this.$set(this.formData.baseInfo, 'companyName', this.formData.merchant.company.companyName)
        }
        if (this.formData.merchant.counterPartBusinessName) {
          this.$set(this.formData.baseInfo, 'counterPartBusinessName', this.formData.merchant.counterPartBusinessName)
        }
      }

      // 迁移SKU中的品牌信息
      if (this.formData.sku && this.formData.sku.brand) {
        this.$set(this.formData.baseInfo, 'brand', this.formData.sku.brand)
      }
    },

    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.baseInfoRef.validate(),
        this.$refs.productInfoRef.validate(),
        this.$refs.deliveryInfoRef.validate()
      ])
      return !validList.includes(false)
    },
    // 通知补寄
    reissueHandle() {
      this.$confirm('确定通知补寄?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        this.apis.resend(this.formData.id).then(res => {
          this.$message.success('已通知补寄')
          this.pullData()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    // 签收
    receiptHandle() {
      this.$confirm('确定签收?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        this.apis.receipt(this.formData.id).then(res => {
          this.$message.success('签收成功')
          this.pullData()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
