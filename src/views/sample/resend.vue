<template>
  <div v-if="isDataInit" class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="样品详情">
        <el-collapse v-model="activeNames">
          <BaseInfo ref="baseInfoRef" :form-data="formData" name="0" :can-edit="false" />
          <ProductInfo ref="productInfoRef" :form-data="formData" name="1" :can-edit="false" />
          <DeliveryInfo ref="deliveryInfoRef" :form-data="formData" name="2" :can-edit="canEdit" class="mt15" />
        </el-collapse>
        <div class="dialog-footer mt10">
          <!-- <el-button v-if="isNotChecked" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button> -->
          <el-button :loading="isSubmiting" type="primary" @click="resendHandle">提交补寄</el-button>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import sampleApi from '@/api/sample'
import ProductInfo from './components/product-info'
import DeliveryInfo from './components/delivery-info'
import detailMixin from '@/mixins/detailMixin'
import BaseInfo from "@/views/sample/components/base-info.vue";
const defaultFormData = {
  // 基础信息对象
  baseInfo: {
    code: null,
    status: null,
    name: null,
    resendStatus: null,
    merchantCode: null,
    storeName: null,
    createTime: null,
    companyName: null,
    counterPartBusinessName: null,
    brand: null
  },
  // 商品信息相关
  sku: {
    productCompanyName: null,
    brand: null,
    name: null,
    productCategoryCode: null,
    label: null,
    acceptSubsidies: null,
    skuLink: null,
  },
  // 快递信息相关
  courierCompany: null,
  trackingNo: null,
  quantity: null,
  shippingTime: null,
  illustrate: null,
  needToReturn: null,
  information: '',
  pictureIds: '',
  files: [],
  auditRecords: [],
}

export default {
  components: {BaseInfo, ProductInfo, DeliveryInfo },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['0', '1', '2'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      auditLevel: 1,
      isDataInit: false,
      // 增删改查集合
      apis: {
        ...sampleApi
      },
    }
  },
  computed: {
    canEdit() {
      return true
    },
  },
  async mounted() {
    await this.pullData()
    this.processFormData() // 处理数据结构
    this.formData = {
      ...this.formData,
      originalSampleId: this.$route.query.id,
      id: '',
      baseInfo: {
        ...this.formData.baseInfo,
        code: `${(this.formData.baseInfo.code || '').split('-')[0]}-${this.formData.frequency + 1}`,
        status: null,
        resendStatus: null,
      },
      courierCompany: null,
      trackingNo: null,
      quantity: null,
      illustrate: null,
      needToReturn: null,
      pictureIds: '',
      information: '',
      files: [],
    }
    this.isDataInit = true
  },
  methods: {
    // 数据处理：将旧数据结构迁移到新的baseInfo结构
    processFormData() {
      // 确保baseInfo对象存在
      if (!this.formData.baseInfo) {
        this.$set(this.formData, 'baseInfo', {})
      }

      // 迁移基础字段到baseInfo对象
      const fieldsToMigrate = ['code', 'status', 'name', 'resendStatus', 'merchantCode', 'storeName', 'createTime']
      fieldsToMigrate.forEach(field => {
        if (this.formData[field] !== undefined && this.formData.baseInfo[field] === undefined) {
          this.$set(this.formData.baseInfo, field, this.formData[field])
        }
      })

      // 迁移merchant相关字段
      if (this.formData.merchant) {
        if (this.formData.merchant.company && this.formData.merchant.company.companyName) {
          this.$set(this.formData.baseInfo, 'companyName', this.formData.merchant.company.companyName)
        }
        if (this.formData.merchant.counterPartBusinessName) {
          this.$set(this.formData.baseInfo, 'counterPartBusinessName', this.formData.merchant.counterPartBusinessName)
        }
      }

      // 迁移SKU中的品牌信息
      if (this.formData.sku && this.formData.sku.brand) {
        this.$set(this.formData.baseInfo, 'brand', this.formData.sku.brand)
      }
    },

    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.baseInfoRef.validate(),
        this.$refs.productInfoRef.validate(),
        this.$refs.deliveryInfoRef.validate()
      ])
      return !validList.includes(false)
    },
    // 提交补寄
    async resendHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.isSubmiting = true
      this.apis.saveResend(this.formData).then(res => {
        this.$message.success('提交成功')
        this.$router.replace({
          name: 'SampleDetail',
          query: {
            id: res.data.id
          }
        })
      }).finally(() => {
        this.isSubmiting = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
