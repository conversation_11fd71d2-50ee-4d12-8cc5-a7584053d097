<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="付款详情">
        <el-collapse v-model="activeNames">
          <BasicInfo ref="basicInfoRef" :form-data="formData" name="1" :can-edit="canEdit" :is-add="isAdd" />
        </el-collapse>
        <div class="dialog-footer mt10">
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
          <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="danger" @click="invalidHandle">作 废</el-button> -->
          <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="danger" @click="deleteHandle">删 除</el-button>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
        <AuditRecord :data="formData.auditRecords" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import collectionApi from '@/api/collection'
import BasicInfo from './components/basic-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import detailMixin from '@/mixins/detailMixin'
const defaultFormData = {
  // 基本信息
  code: null,
  liveBroadcastPlanId: null,
  salesContractId: null,
  merchantId: null,
  merchantCompanyId: null,
  merchantCompanyName: null,
  merchantPaymentDate: null,
  merchantPaymentAmount: null,
  status: null,
  detail: [],
  files: [],
  //   {
  //     index: null,
  //     type: null,
  //     amount: null,
  //   }
  // ],
  pictureIds: null,
}

export default {
  name: 'EnterprisePaymentDetail',
  components: { BasicInfo, AuditRecord },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2', '3'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      apis: {
        ...collectionApi
      },
    }
  },
  computed: {
    // status 0保存；1待审核；2通过；3不通过；4作废
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    },
  },
  mounted() {
    this.pullData()
  },
  methods: {
    // pullData() {
    //   const { id } = this.$route.query
    //   if (!id) {
    //     return
    //   }
    //   this.isLoading = true
    //   this.apis.getDetail(id).then(response => {
    //     this.originFormData = { ...response.data }
    //     this.formData = { ...response.data }
    //     // const contract = response.data?.contract
    //     // const merchant = response.data?.merchant
    //     // this.$set(this.formData, 'contractCode', contract.code)
    //     // this.$set(this.formData, 'contractId', contract.id)
    //     // this.$set(this.formData, 'contractCompanyName', merchant.company.companyName)
    //     // this.$set(this.formData, 'contractDate', contract.contractDate)
    //     // this.$set(this.formData, 'contractAmount', contract.amount)
    //     // console.log(this.formData)
    //     // const paymentNodeIds = response.data?.paymentNodes.map(ele => ele.id)
    //     // console.log(paymentNodeIds)
    //     // this.$set(this.formData, 'paymentNodeIds', paymentNodeIds)
    //   }).finally(() => {
    //     this.isLoading = false
    //   })
    // },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate()
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
