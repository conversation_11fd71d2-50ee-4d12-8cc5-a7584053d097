<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" label-width="90px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="单据编号"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              clearable
              disabled
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款单位"
            prop="merchantCompanyId"
          >
            <div class="flex-content">
              <el-input
                :value="formData.merchantCompany ? formData.merchantCompany.companyName : ''"
                clearable
                disabled
                placeholder="请选择"
              />
              <CompanyChoose v-model="formData.merchantCompanyId" :merchant-id="formData.merchantId" :set-default="isAdd" :editable="true" @change="(val, obj) => formData.merchantCompany = obj || {}">
                <el-button style="width:55px" type="primary" plain class="ml5">选择</el-button>
              </CompanyChoose>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款日期"
            prop="merchantPaymentDate"
          >
            <el-date-picker
              v-model="formData.merchantPaymentDate"
              type="date"
              style="width: 100%;"
              placeholder="选择日期"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款金额"
            prop="merchantPaymentAmount"
          >
            <el-input
              v-model="formData.merchantPaymentAmount"
              type="number"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item
            label="合同日期"
            prop="contractDate"
          >
            <el-date-picker
              v-model="formData.contractDate"
              type="date"
              disabled
              placeholder="选择日期"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item
            label="合同金额"
            prop="contractAmount"
          >
            <el-input
              v-model="formData.contractAmount"
              type="number"
              clearable
              disabled
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            label="付款节点"
            prop="paymentNodeIds"
          >
            <el-select v-model="formData.paymentNodeIds" multiple placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in nodeList"
                :key="item.type"
                :label="item.lable"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item
            label="直播日期"
            prop="直播日期"
          >
            <el-date-picker
              v-model="formData.直播日期"
              type="date"
              placeholder="选择日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item
            label="付款单"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              clearable
              disabled
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="files"
          >
            <div slot="label">
              相关附件
            </div>
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :disabled="!canEdit"
                :files="formData.files"
                :product-category-code-list="['SK']"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!isAdd">
        <el-col :span="6">
          <el-form-item
            label="状态"
            prop="status"
          >
            {{ formData.status | toEnumName('commonAuditStatus') }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <CommonChooseDialog ref="commonChooseDialogRef" title="选择销售合同" :component="SaleContract" :on-chosed="onChosed" :multiple="false" :init-search-params="{status: 2}" /> -->
  </el-collapse-item>
</template>

<script>
import CompanyChoose from '@/components/Company/CompanyChoose'
// import contractSaleApi from '@/api/contractSale'
import FormAttachment from '@/components/FormAttachment'
import CommonChooseDialog from '@/components/ChooseDialogs/CommonChooseDialog'
import { mapGetters } from 'vuex'
// import SaleContract from '@/views/contract/sale/index.vue'
// import PurchaseContract from '@/views/contract/purchase/index.vue'
export default {
  components: { CompanyChoose, FormAttachment, CommonChooseDialog },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    isAdd: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      // SaleContract,
      // PurchaseContract,
      nodeList: [],
      rules: {
        // contractCode: [{ required: true, message: '请选择合同号', trigger: 'change' }],
        merchantCompanyId: [{ required: true, message: '请选择付款单位', trigger: 'change' }],
        merchantPaymentDate: [{ required: true, message: '请选择付款日期', trigger: 'change' }],
        merchantPaymentAmount: [{ required: true, message: '请输入付款金额', trigger: 'change' }],
      },
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ])
  },
  watch: {
    // 'formData.contractId'(newVal, oldVal) {
    //   newVal !== oldVal && this.getNodeList(newVal)
    // }
  },
  mounted() {
    if (!this.$isAdmin && this.isAdd) {
      this.formData.merchantId = this.userInfo.id
      // this.formData.merchant.brand = this.userInfo.brand
    }
  },
  methods: {
    // chooseContract() {
    //   this.$refs.commonChooseDialogRef.show()
    // },
    // // 获取付款节点列表
    // getNodeList(id) {
    //   if (!id) {
    //     return
    //   }
    //   this.isLoading = true
    //   contractSaleApi.getDetail(id).then(response => {
    //     this.nodeList = response.data.paymentNodes || []
    //   }).finally(() => {
    //     this.isLoading = false
    //   })
    // },
    // onChosed(items) {
    //   // console.log(items)
    //   if (items.length > 0) {
    //     this.$set(this.formData, 'contractCode', items[0].code)
    //     this.$set(this.formData, 'contractId', items[0].id)
    //     this.$set(this.formData, 'contractCompanyName', items[0].companyName)
    //     this.$set(this.formData, 'contractDate', items[0].contractDate)
    //     this.$set(this.formData, 'contractAmount', items[0].amount)
    //     this.getNodeList(items[0].id)
    //   } else {
    //     // this.$set(this.formData, 'contractCode', '')
    //   }
    // },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
