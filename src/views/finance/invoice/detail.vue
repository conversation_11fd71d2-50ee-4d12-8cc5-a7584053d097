<!--
 * @Author: <EMAIL>
 * @Date: 2024-02-01 10:58:38
-->
<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="发票详情">
        <el-collapse v-model="activeNames">
          <BasicInfo ref="basicInfoRef" :form-data="formData" name="1" :can-edit="canEdit" />
        </el-collapse>
        <div class="dialog-footer mt10">
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
          <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button>
          <el-button v-if="!isAdd" :loading="isSubmiting" type="danger" @click="invalidHandle">作 废</el-button> -->
          <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="danger" @click="deleteHandle">删 除</el-button>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
        <AuditRecord :data="formData.auditRecords" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import invoiceApi from '@/api/invoice'
import BasicInfo from './components/basic-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import AuditDialog from '@/components/Audit/AuditDialog'
import detailMixin from '@/mixins/detailMixin'
const defaultFormData = {
  salesReceiptsId: null,
  information: null,
  email: null,
  invoiceNo: null,
  invoiceDate: new Date().getTime(),
  invoiceAmount: null,
  pictureIds: null,
  type: 1,
  remark: null
}

export default {
  name: 'FinanceInvoiceDetail',
  components: { BasicInfo, AuditRecord, AuditDialog },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2', '3'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      apis: {
        ...invoiceApi
      },
    }
  },
  computed: {
    // status 0保存；1待审核；2通过；3不通过；4作废
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    },
  },
  mounted() {
    this.pullData()
  },
  methods: {
    pullData() {
      const { id } = this.$route.query
      if (!id) {
        return
      }
      this.isLoading = true
      this.apis.getDetail(id).then(response => {
        this.originFormData = { ...response.data }
        this.formData = { ...response.data }
        // const contractPaymentReceived = response.data?.contractPaymentReceived
        // const merchant = response.data?.merchant
        // this.$set(this.formData, 'paymentReceivedCode', contractPaymentReceived.code)
        // this.$set(this.formData, 'paymentReceivedId', contractPaymentReceived.id)
        // this.$set(this.formData, 'paymentReceivedCompanyName', merchant.company?.companyName)
        // this.$set(this.formData, 'paymentReceivedDate', contractPaymentReceived.paymentDate)
        // this.$set(this.formData, 'paymentReceivedAmount', contractPaymentReceived.amount)
        // this.$set(this.formData, 'paymentReceivedEmail', merchant.company?.email)
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate()
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
