<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" label-width="90px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="付款单号"
            prop="salesReceiptsId"
          >
            <div class="flex-content">
              <el-input
                :value="formData.salesReceipts ? formData.salesReceipts.code : ''"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-button v-if="canEdit" type="primary" style="width: 55px;" plain class="ml5" @click="chooseEnterprisePayment">选择</el-button>
              <el-form v-else :model="formData">
                <el-button
                  type="primary"
                  style="width: 55px;"
                  plain
                  class="ml5"
                  :disabled="false"
                  @click="$router.push({
                    name: 'EnterprisePaymentDetail',
                    query: {
                      id: formData.salesReceipts.id
                    }
                  })"
                >查看</el-button>
              </el-form>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款单位"
          >
            <el-input
              :value="formData.salesReceipts ? formData.salesReceipts.merchantCompanyName : ''"
              clearable
              disabled
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款日期"
          >
            <el-date-picker
              :value="formData.salesReceipts ? formData.salesReceipts.merchantPaymentDate : ''"
              type="date"
              disabled
              placeholder="选择日期"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="付款金额"
          >
            <el-input
              :value="formData.salesReceipts ? formData.salesReceipts.merchantPaymentAmount : ''"
              type="number"
              clearable
              disabled
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="发票类型"
            prop="type"
          >
            <el-select v-model="formData.type" placeholder="请选择" clearable>
              <el-option
                v-for="item in $enums['invoiceType'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="电子邮箱"
            prop="email"
          >
            <el-input
              v-model="formData.email"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item
            label="开票日期"
            prop="invoiceDate"
          >
            <el-date-picker
              v-model="formData.invoiceDate"
              type="date"
              style="width: 100%;"
              placeholder="选择日期"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="开票信息"
            prop="information"
          >
            <el-input
              v-model="formData.information"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="备注"
            prop="remark"
          >
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="files"
          >
            <div class="flex-content">
              <!-- 一般纳税人证明 -->
              <FormAttachment
                v-if="canEdit"
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :disabled="!canEdit"
                :files="formData.files"
                :product-category-code-list="['FP1']"
              />
              <!-- 发票 -->
              <FormAttachment
                v-else
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :disabled="!canEdit"
                :files="formData.files"
                :product-category-code-list="['FP1', 'FP2']"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <CommonChooseDialog ref="enterprisePaymentChooseDialogRef" title="选择付款单" :component="EnterprisePayment" :multiple="false" :init-search-params="{status: 2}" />
  </el-collapse-item>
</template>

<script>
import CommonChooseDialog from '@/components/ChooseDialogs/CommonChooseDialog'
import EnterprisePayment from '@/views/finance/enterprise-payment/index.vue'
import { validEmail } from '@/utils/validate'
// import Collection from '@/views/finance/collection/index.vue'
export default {
  components: { CommonChooseDialog },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      EnterprisePayment,
      rules: {
        salesReceiptsId: [
          { required: true, message: '请选择付款单号', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择发票类型', trigger: 'change' }
        ],
        email: [
          { required: true, message: '请输入电子邮箱', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validEmail(value)) {
              callback(new Error('请输入正确的邮箱地址'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        // information: [{
        //   required: true, message: '请输入开票信息', trigger: 'change'
        // }],
        invoiceDate: [{
          required: true, message: '请选择开票日期', trigger: 'change'
        }],
        information: [{
          required: true, message: '请输入开票信息', trigger: 'change'
        }],
      },
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    chooseEnterprisePayment() {
      this.$refs.enterprisePaymentChooseDialogRef.show(null, items => {
        if (items.length > 0) {
          this.$set(this.formData, 'salesReceipts', items[0])
          this.$set(this.formData, 'salesReceiptsId', items[0].id)
          this.$set(this.formData, 'invoiceAmount', items[0].merchantPaymentAmount)
        } else {
          this.$set(this.formData, 'salesReceipts', null)
          this.$set(this.formData, 'salesReceiptsId', '')
          this.$set(this.formData, 'invoiceAmount', '')
        }
      })
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
