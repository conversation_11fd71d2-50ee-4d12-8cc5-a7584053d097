<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item label="单据编号" prop="code">
            <el-input v-model="searchForm.code" clearable placeholder="请输入单据编号" />
          </el-form-item>
          <el-form-item label="付款单号" prop="code">
            <el-input v-model="searchForm.salesReceiptsCode" clearable placeholder="请输入付款单号" />
          </el-form-item>
          <el-form-item label="发票号" prop="invoiceNo">
            <el-input v-model="searchForm.invoiceNo" clearable placeholder="请输入发票号" />
          </el-form-item>
          <el-form-item label="商户名称" prop="merchantCompanyName">
            <el-input v-model="searchForm.merchantCompanyName" clearable placeholder="请输入商户名称" />
          </el-form-item>
          <!-- <el-form-item label="开票日期" prop="开票日期">
            <el-date-picker
              v-model="dateArr"
              type="daterange"
              style="width: 230px;"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="timestamp"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
            <el-button
              v-if="!canChoose"
              type="primary"
              icon="el-icon-plus"
              plain
              @click="$router.push({
                name: 'FinanceInvoiceAdd'
              })"
            >新增</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- <el-button type="primary" @click="addHandle">添加</el-button> -->
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <template v-if="canChoose">
        <el-table-column
          v-if="multiple"
          fixed
          type="selection"
          label="选择"
          align="center"
          min-width="45"
        />
        <el-table-column
          v-else
          fixed
          label="选择"
          align="center"
          min-width="45"
        >
          <template slot-scope="scope">
            <el-radio :value="selectItems.length ? selectItems[0].id : ''" :label="scope.row.id" class="no-text-radio" />
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="单据编号"
        prop="code"
        min-width="130"
      />
      <el-table-column
        label="付款单号"
        prop="salesReceiptsCode"
        min-width="130"
      />
      <el-table-column
        label="发票号"
        prop="invoiceNo"
        min-width="130"
      />
      <el-table-column
        label="电子邮箱"
        prop="email"
        min-width="150"
      />
      <el-table-column
        label="开票金额"
        prop="invoiceAmount"
        min-width="100"
      />
      <el-table-column
        label="申请日期"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | parseDate }}
        </template>
      </el-table-column>
      <el-table-column
        label="开票日期"
        prop="invoiceDate"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.invoiceDate | parseDate }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        min-width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('commonAuditStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="!canChoose"
        fixed="right"
        label="操作"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push({
              path: `/finance/invoice-detail`,
              query: {
                id: scope.row.id
              }
            })"
          >查看</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <!-- <el-dialog :title="oprateDialog.title" :visible.sync="oprateDialog.isShow" :close-on-click-modal="false" class="oprate-dialog" width="850px">
      <detail v-if="oprateDialog.isShow" v-model="oprateDialog.isShow" :dialog-data="oprateDialog.data" :dialog-type="oprateDialog.type" @success="pullData" />
    </el-dialog> -->
  </div>
</template>

<script>
import listMixin from '@/mixins/listMixin'
import invoiceApi from '@/api/invoice'
// import detail from './detail'

export default {
  name: 'FinanceInvoice',
  // components: { detail },
  mixins: [listMixin],
  props: {},
  data() {
    return {
      searchForm: {
        code: null,
        invoiceDateStart: null,
        invoiceDateEnd: null,
        salesReceiptsCode: null,
        invoiceNo: null,
        merchantCompanyName: null,
      },
      // 增删改查集合
      apis: {
        ...invoiceApi
      }
    }
  },
  computed: {
    dateArr: {
      get() {
        return this.searchForm.invoiceDateStart ? [this.searchForm.invoiceDateStart, this.searchForm.invoiceDateEnd] : []
      },
      set(val) {
        this.$set(this.searchForm, 'invoiceDateStart', val ? val[0] : null)
        this.$set(this.searchForm, 'invoiceDateEnd', val ? val[1] : null)
      }
    }
  },
  mounted() {
    this.pullData()
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
