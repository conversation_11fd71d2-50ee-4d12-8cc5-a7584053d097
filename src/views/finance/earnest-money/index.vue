<template>
  <div class="app-container oprate-form">
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1" class="collapse-module">
        <template slot="title">
          <div class="collapse-title-pre">保证金详情</div>
        </template>
        <el-form ref="form" :model="formData" :disabled="!canEdit" label-position="right" label-width="120px">
          <el-row>
            <el-col :span="6">
              <el-form-item
                label="商家名称"
              >
                <el-input
                  :value="formData.merchant ? formData.merchant.company.companyName : ''"
                  disabled
                  clearable
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="保证金总额"
              >
                <el-input
                  :value="formData.margin || 0"
                  clearable
                  disabled
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="冻结保证金"
              >
                <el-input
                  :value="formData.freezeMargin || 0"
                  clearable
                  disabled
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="可用保证金"
              >
                <el-input
                  :value="formData.usableMargin || 0"
                  clearable
                  disabled
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item
                label="最后一场直播场次"
              >
                <el-tooltip class="item" effect="dark" :content="(formData.liveBroadcastPlan && formData.liveBroadcastPlan.status === 4) ? `${$utils.parseTime(formData.liveBroadcastPlan.realTime, '{y}-{m}-{d}')}【${formData.liveBroadcastPlan.theme}】` : ''" placement="top-end">
                  <el-input
                    :value="(formData.liveBroadcastPlan && formData.liveBroadcastPlan.status === 4) ? `${$utils.parseTime(formData.liveBroadcastPlan.realTime, '{y}-{m}-{d}')}【${formData.liveBroadcastPlan.theme}】` : ''"
                    disabled
                    clearable
                    placeholder=""
                  />
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="距今天数"
              >
                <el-input
                  :value="daysFromNow"
                  type="number"
                  clearable
                  disabled
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-radio-group v-model="tabIndex" class="mb10">
            <el-radio-button :label="0">保证金往来记录</el-radio-button>
            <el-radio-button :label="1">保证金冻结记录</el-radio-button>
            <el-radio-button :label="2">在冻保证金合同</el-radio-button>
          </el-radio-group>
          <RecordList v-if="tabIndex === 0" :merchant-id="formData.merchantId" :show-apply="showApply" />
          <FreezeList v-if="tabIndex === 1" :merchant-id="formData.merchantId" :show-apply="showApply" />
          <ContractList v-if="tabIndex === 2" :merchant-id="formData.merchantId" />
        </el-form>
        <!-- <CommonChooseDialog ref="enterprisePaymentChooseDialogRef" title="选择付款单" :component="EnterprisePayment" :multiple="false" :init-search-params="{status: 2}" /> -->
      </el-collapse-item>
    </el-collapse>
    <div v-if="!$isAdmin" class="dialog-footer mt10">
      <!-- && formData.balance > 0 -->
      <el-button
        v-if="canEdit"
        :loading="isSubmiting"
        type="primary"
        @click="showApply()"
      >申请退还</el-button>
      <!-- <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
      <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button> -->
      <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button>
      <el-button v-if="!isAdd" :loading="isSubmiting" type="danger" @click="invalidHandle">作 废</el-button> -->
      <!-- <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="danger" @click="deleteHandle">删 除</el-button>
      <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button> -->
    </div>
    <div v-if="$isAdmin" class="dialog-footer mt10">
      <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
    </div>
    <!-- <Detail ref="detailRef" /> -->
  </div>
</template>

<script>
import earnestMoneyApi from '@/api/earnestMoney'
import BasicInfo from './components/basic-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import AuditDialog from '@/components/Audit/AuditDialog'
import detailMixin from '@/mixins/detailMixin'
import Detail from './detail'
import { mapGetters } from 'vuex'
import RecordList from './components/record-list'
import FreezeList from './components/freeze-list'
import ContractList from './components/contract-list'
const defaultFormData = {
  merchantId: '',
  liveBroadcastPlan: null
}

export default {
  name: 'EarnestMoney',
  components: { BasicInfo, AuditRecord, AuditDialog, Detail, RecordList, FreezeList, ContractList },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2', '3'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      tabIndex: 0,
      // 增删改查集合
      apis: {
        ...earnestMoneyApi,
        getDetail: earnestMoneyApi.getEnterprisEarnestMoneyeData,
      },
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
    // status 0保存；1待审核；2通过；3不通过；4作废
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    },
    // 距今天数
    daysFromNow() {
      if (this.formData.liveBroadcastPlan && this.formData.liveBroadcastPlan.realTime) {
        const time = new Date().getTime() - new Date(this.formData.liveBroadcastPlan.realTime).getTime()
        return (time / 1000 / 60 / 60 / 24).toFixed(0)
      } else {
        return 0
      }
    },
  },
  mounted() {
    if (this.$isAdmin) {
      this.formData.merchantId = this.$route.query.merchantId
    } else {
      this.formData.merchantId = this.userInfo.id
    }
    this.pullData()
  },
  methods: {
    pullData() {
      const { merchantId } = this.formData
      this.isLoading = true
      this.apis.getDetail(merchantId).then(response => {
        this.originFormData = { ...this.originFormData, ...response.data }
        this.formData = { ...response.data }
        this.formData.merchantId = merchantId
      }).finally(() => {
        this.isLoading = false
      })
    },
    showApply(item) {
      if (!item) {
        // if (this.daysFromNow < 90) {
        //   this.$message.error('距今天数小于90天，不可申请退还')
        //   return
        // }
        this.$router.push({
          name: 'EarnestMoneyAdd',
          query: {
            merchantId: this.$isAdmin ? this.formData.merchantId : undefined
          }
        })
      } else {
        this.$router.push({
          name: 'EarnestMoneyDetail',
          query: {
            id: item.documentId,
          }
        })
      }
      // this.$refs.detailRef.show(item, this.pullData)
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate()
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
