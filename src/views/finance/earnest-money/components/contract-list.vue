<template>
  <div>
    <el-table
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
    >
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="合同编号"
        fixed
        prop="code"
        width="150"
      />
      <el-table-column
        label="合同类型"
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.type | toEnumName('contractType') }}
        </template>
      </el-table-column>
      <el-table-column
        :label="searchForm.type === 6 ? '被授权方': '甲方'"
        prop="partyACompanyName"
        min-width="110"
      />
      <el-table-column
        label="乙方"
        prop="partyBCompanyName"
        min-width="110"
      />
      <!-- <el-table-column
        label="乙方"
        prop="businessCompanyName"
        min-width="110"
      /> -->
      <!-- <el-table-column
        label="直播场次"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.planningTime | parseDate('{y}-{m}-{d}') }}【{{ scope.row.theme }}】
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="商务"
        prop="businessManager"
        min-width="100"
      />
      <el-table-column
        label="品牌"
        prop="brand"
        min-width="110"
      />
      <el-table-column
        label="商品"
        prop="product"
        min-width="130"
      /> -->
      <el-table-column
        label="保证金"
        prop="margin"
        min-width="80"
      />
      <el-table-column
        label="合同状态"
        min-width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('commonAuditStatus') }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="合同日期"
        prop="contractDate"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.contractDate | parseDate }}
        </template>
      </el-table-column>
      <el-table-column
        label="合同金额"
        prop="amount"
        min-width="100"
      />
      <el-table-column
        label="合同类型"
        prop="type"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ {
            1: '框架合同'
          }[scope.row.type] }}
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="$isAdmin"
        fixed="right"
        label="操作"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push({
              name: `SaleContractDetail`,
              query: {
                id: scope.row.id,
                t: searchForm.type
              }
            })"
          >查看</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
  </div>
</template>
<script>
import contractSaleApi from '@/api/contractSale'
import listMixin from '@/mixins/listMixin'
export default {
  mixins: [listMixin],
  props: {
    merchantId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      searchForm: {
        partyAId: '',
        hasFreezeMargin: true
      },
      tableData: [],
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      // 增删改查集合
      apis: {
        ...contractSaleApi,
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.searchForm.partyAId = this.merchantId
      this.pullData()
    })
  },
  methods: {
  }
}
</script>
