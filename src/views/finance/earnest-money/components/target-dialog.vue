<template>
  <el-dialog v-if="isShow" :title="title" :visible.sync="isShow" width="350px" :close-on-click-modal="false" append-to-body>
    <el-form ref="form" class="oprate-form" :model="formData" :rules="rules" label-position="right" label-width="90px">
      <el-row>
        <el-col :span="20">
          <el-form-item
            label="品类"
            prop="productCategoryCode"
          >
            <ProductCategorySelect
              v-model="formData.productCategoryCode"
              @change="(val, obj) => {
                formData.productCategoryName = obj.name
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item
            label="目标销量"
            prop="targetSalesVolume"
          >
            <TenThousandInput v-model="formData.targetSalesVolume" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="报名链接"
            prop="报名链接"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.报名链接"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.报名链接, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div class="dialog-footer mt10">
      <el-button :loading="isSubmiting" plain type="primary" @click="close">取 消</el-button>
      <el-button :loading="isSubmiting" type="primary" @click="saveHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import detailMixin from '@/mixins/detailMixin'
import ProductCategorySelect from '@/components/ProductCategorySelect'
const defaultFormData = {
  productCategoryCode: '',
  productCategoryName: '',
  targetSalesVolume: '',
}
export default {
  components: { ProductCategorySelect },
  mixins: [detailMixin],
  props: {
    editable: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      isShow: false,
      title: '目标销量详情',
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      rules: {
        productCategoryCode: [{ required: true, message: '请选择商品品类', trigger: 'change' }],
        targetSalesVolume: [{ required: true, message: '请输入目标销量', trigger: 'change' }],
      },
      onSuccess: null
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    close() {
      this.isShow = false
    },
    show(data, onSuccess) {
      this.isShow = true
      this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...data }))
      this.onSuccess = onSuccess
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          resolve(valid)
        })
      })
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.close()
      this.onSuccess && this.onSuccess(this.formData)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
