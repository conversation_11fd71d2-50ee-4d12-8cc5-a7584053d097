<template>
  <div>
    <el-table
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
    >
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="单据编号"
        min-width="140"
      >
        <template slot-scope="scope">
          <el-button v-if="scope.row.documentType === 2" type="text" @click="showApply(scope.row)">{{ scope.row.documentCode }}</el-button>
          <span v-else>{{ scope.row.documentCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="记录日期"
      >
        <template slot-scope="scope">
          {{ scope.row.recordDate | parseDate }}
        </template>
      </el-table-column>
      <el-table-column
        prop="amount"
        label="金额"
      />
      <el-table-column
        label="类型"
      >
        <template slot-scope="scope">
          {{ {
            1: '收取',
            2: '退还',
            3: '沿用'
          }[scope.row.type] }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('commonAuditStatus') }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="操作"
        width="80"
      >
        <template slot-scope="scope">
          <el-button class="pd0" size="mini" type="text" @click="showApply(scope.row)">查看</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
  </div>
</template>
<script>
import earnestMoneyApi from '@/api/earnestMoney'
import listMixin from '@/mixins/listMixin'
export default {
  mixins: [listMixin],
  props: {
    merchantId: {
      type: [Number, String],
      default: ''
    },
    showApply: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      searchForm: {
        merchantId: ''
      },
      tableData: [],
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      // 增删改查集合
      apis: {
        ...earnestMoneyApi,
        getPager: earnestMoneyApi.merchantMarginRecord,
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.searchForm.merchantId = this.merchantId
      this.pullData()
    })
  },
  methods: {
  }
}
</script>
