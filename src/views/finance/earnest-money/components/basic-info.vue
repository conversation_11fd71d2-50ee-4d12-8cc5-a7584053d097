<template>
  <!-- <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">保证金退换单</div>
    </template> -->
  <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" class="mt10" label-width="120px">
    <el-row>
      <el-col :span="8">
        <el-form-item
          label="保证金退换单编号"
          prop="code"
        >
          <el-input
            v-model="formData.code"
            disabled
            clearable
            placeholder="自动生成"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="申请日期"
          prop="createTime"
        >
          <el-date-picker
            v-model="formData.createTime"
            type="date"
            disabled
            style="width:100%"
            placeholder="选择日期"
            value-format="timestamp"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="退还金额"
          prop="amount"
        >
          <!-- <el-input-number v-model="formData.amount" :min="0" :max="maxBalance" label="请输入退还金额" style="width: 100%;" /> -->
          <el-input
            v-model="formData.amount"
            type="number"
            :disabled="$isAdmin"
            clearable
            :placeholder="`最大可输入${maxBalance}`"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="商家账户信息"
          prop="accountInformation"
        >
          <el-input
            v-model="formData.accountInformation"
            clearable
            type="textarea"
            :rows="3"
            :disabled="$isAdmin"
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item
          label="付款日期"
          prop="paymentDate"
        >
          <el-date-picker
            v-model="formData.paymentDate"
            type="date"
            disabled
            placeholder="选择日期"
            style="width:100%"
            value-format="timestamp"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          label="实付金额"
          prop="actualPaymentAmount"
        >
          <!-- <el-input-number v-model="formData.actualPaymentAmount" disabled label="请输入实付金额" style="width: 100%;" /> -->
          <el-input
            v-model="formData.actualPaymentAmount"
            type="number"
            disabled
            clearable
            placeholder=""
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item
          label="相关附件"
          prop="files"
        >
          <div class="flex-content">
            <FormAttachment
              ref="formAttachmentRef"
              v-model="formData.pictureIds"
              :disabled="!canEdit"
              :files="formData.files"
              :product-category-code-list="['BZJTH']"
            />
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!-- </el-collapse-item> -->
</template>

<script>
import CommonChooseDialog from '@/components/ChooseDialogs/CommonChooseDialog'
import { validEmail } from '@/utils/validate'
export default {
  components: { CommonChooseDialog },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    maxBalance: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      rules: {
        amount: [
          { required: true, message: '请输入退还金额', trigger: 'change' },
          // { max: this.maxBalance, message: `最多可申请退还金额为：${this.maxBalance}`, trigger: 'change' }
        ],
        accountInformation: [{ required: true, message: '请输入商家账户信息', trigger: 'change' }],
      }
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            if (parseFloat(this.formData.amount) > this.maxBalance) {
              this.$message.error(`最多可申请退还金额为：${this.maxBalance}`)
              return false
            }
          }
          return resolve(valid)
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
