<template>
  <el-dialog v-if="isShow" :title="title" :visible.sync="isShow" width="350px" :close-on-click-modal="false" append-to-body>
    <el-form ref="form" class="oprate-form mr20" :model="formData" :rules="rules" label-position="right" label-width="90px">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="付款日期"
            prop="paymentDate"
          >
            <el-date-picker
              v-model="formData.paymentDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%;"
              value-format="timestamp"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="实付金额"
            prop="actualPaymentAmount"
          >
            <el-input
              v-model="formData.actualPaymentAmount"
              type="number"
              clearable
              placeholder=""
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item
            label="报名链接"
            prop="报名链接"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.报名链接"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.报名链接, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div class="dialog-footer mt10">
      <el-button :loading="isSubmiting" plain type="primary" @click="close">取 消</el-button>
      <el-button :loading="isSubmiting" type="primary" @click="saveHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import earnestMoneyApi from '@/api/earnestMoney'
import detailMixin from '@/mixins/detailMixin'
const defaultFormData = {
  id: '',
  paymentDate: '',
  actualPaymentAmount: '',
  // accountInformation: '',
  // merchantId: '',
}
export default {
  mixins: [detailMixin],
  props: {
    editable: {
      type: Boolean,
      default: true
    },
    tableData: {
      type: [Array, null],
      default: () => []
    }
  },
  data() {
    return {
      isShow: false,
      title: '付款信息',
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      rules: {
        paymentDate: [{ required: true, message: '请选择付款日期', trigger: 'change' }],
        actualPaymentAmount: [{ required: true, message: '请输入实付金额', trigger: 'change' }],
      },
      onSuccess: null
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    close() {
      this.isShow = false
    },
    show(data, onSuccess) {
      this.isShow = true
      this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...data }))
      this.onSuccess = onSuccess
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          resolve(valid)
        })
      })
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.isSubmiting = true
      earnestMoneyApi.savePayInfo(this.formData).then(res => {
        this.$message.success('保存成功')
        this.close()
        this.onSuccess && this.onSuccess(this.formData)
      }).finally(() => {
        this.isSubmiting = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
