<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="详细信息">
        <el-collapse v-model="activeNames">
          <BasicInfo ref="basicInfoRef" :form-data="formData" name="1" :can-edit="canEdit" :max-balance="maxBalance" />
        </el-collapse>
        <div v-if="$isAdmin" class="dialog-footer mt10">
          <el-button v-if="!isAdd && formData.status !== 2" v-permission="'button:finance:merchantMargin:actualPayment'" :loading="isSubmiting" type="primary" @click="payHandle">付款信息</el-button>
          <template v-if="formData.status === 1">
            <AuditBtn level="businessAudit" :audit-api="apis.businessAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
            <AuditBtn v-permission="'button:finance:merchantMargin:cashierAudit'" level="cashierAudit" :audit-api="apis.cashierAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" :on-before="validateInput" />
            <AuditBtn level="financeManagerAudit" :audit-api="apis.financeManagerAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
          </template>
          <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button> -->
        </div>
        <div v-else class="dialog-footer mt10">
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
          <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
          <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button>
          <el-button v-if="!isAdd" :loading="isSubmiting" type="danger" @click="invalidHandle">作 废</el-button> -->
          <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="danger" @click="deleteHandle">删 除</el-button>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
        <AuditRecord :data="formData.auditRecords" />
      </el-tab-pane>
    </el-tabs>
    <PayDialog ref="payDialogRef" />
  </div>
</template>

<script>
import earnestMoneyApi from '@/api/earnestMoney'
import BasicInfo from './components/basic-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import AuditDialog from '@/components/Audit/AuditDialog'
import PayDialog from './components/pay-dialog'
import detailMixin from '@/mixins/detailMixin'
import { mapGetters } from 'vuex'
const defaultFormData = {
  code: null,
  merchantId: null,
  amount: null,
  accountInformation: null,
  paymentDate: null,
  actualPaymentAmount: null,
  status: null,
  createTime: new Date().getTime()
}

export default {
  name: 'EarnestMoneyDetail',
  components: { BasicInfo, AuditRecord, AuditDialog, PayDialog },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2', '3'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      merchantData: {},
      initAmount: 0, // 初始金额，用于编辑的时候计算
      // 增删改查集合
      apis: {
        ...earnestMoneyApi
      },
    }
  },
  computed: {
    // 最大可输入保证金余额
    maxBalance() {
      return this.merchantData.usableMargin
      // return this.canEdit ?
      // return (this.merchantData.balance || 0) - (this.initAmount || 0)
    },
    ...mapGetters([
      'userInfo'
    ]),
    // status 0保存；1待审核；2通过；3不通过；4作废
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    },
  },
  async mounted() {
    if (this.isAdd) {
      if (this.$isAdmin) {
        this.formData.merchantId = this.$route.query.merchantId
      } else {
        this.formData.merchantId = this.userInfo.id
      }
    }
    await this.pullData()
    this.pullMerchantData()
  },
  methods: {
    pullMerchantData() {
      console.log(this.formData)
      const { merchantId } = this.formData
      this.isLoading = true
      this.apis.getEnterprisEarnestMoneyeData(merchantId).then(response => {
        this.merchantData = { ...response.data }
      }).finally(() => {
        this.isLoading = false
      })
    },
    pullData() {
      const { id } = this.$route.query
      if (!id) {
        return
      }
      this.isLoading = true
      return this.apis.getDetail(id).then(response => {
        this.originFormData = { ...response.data }
        this.formData = { ...response.data }
        this.initAmount = this.formData.amount
        // const contractPaymentReceived = response.data?.contractPaymentReceived
        // const merchant = response.data?.merchant
        // this.$set(this.formData, 'paymentReceivedCode', contractPaymentReceived.code)
        // this.$set(this.formData, 'paymentReceivedId', contractPaymentReceived.id)
        // this.$set(this.formData, 'paymentReceivedCompanyName', merchant.company?.companyName)
        // this.$set(this.formData, 'paymentReceivedDate', contractPaymentReceived.paymentDate)
        // this.$set(this.formData, 'paymentReceivedAmount', contractPaymentReceived.amount)
        // this.$set(this.formData, 'paymentReceivedEmail', merchant.company?.email)
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate()
      ])
      return !validList.includes(false)
    },
    // 出纳审核的时候，校验一下保证金
    async validateInput() {
      if (!this.formData.paymentDate || !this.formData.actualPaymentAmount) {
        this.$message.error('请先填写付款信息')
        return false
      }
    },
    payHandle() {
      this.$refs.payDialogRef.show(this.formData, this.pullData)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
