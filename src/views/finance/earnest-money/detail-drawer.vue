<template>
  <el-drawer
    title="保证金退还单"
    :visible.sync="isShow"
    :with-header="false"
    size="70%"
  >
    <div class="app-container oprate-form">
      <el-tabs type="border-card">
        <el-tab-pane label="保证金退还单">
          <!-- <el-collapse v-model="activeNames"> -->
          <BasicInfo ref="basicInfoRef" :form-data="formData" name="1" :can-edit="canEdit" />
          <!-- </el-collapse> -->
          <div v-if="$isAdmin" class="dialog-footer mt10">
            <template v-if="formData.status === 1">
              <AuditBtn v-permission="''" level="businessAudit" :audit-api="apis.businessAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
            </template>
            <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button> -->
            <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="payHandle">付款信息</el-button>
          </div>
          <div v-else class="dialog-footer mt10">
            <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
            <el-button v-if="canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
            <!-- <el-button v-if="!isAdd" :loading="isSubmiting" type="primary" @click="auditHandle">审 核</el-button>
            <el-button v-if="!isAdd" :loading="isSubmiting" type="danger" @click="invalidHandle">作 废</el-button> -->
            <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="danger" @click="deleteHandle">删 除</el-button>
            <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
          <AuditRecord :data="formData.auditRecords" />
        </el-tab-pane>
      </el-tabs>
      <PayDialog ref="payDialogRef" />
    </div>
  </el-drawer>
</template>

<script>
import earnestMoneyApi from '@/api/earnestMoney'
import BasicInfo from './components/basic-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import AuditDialog from '@/components/Audit/AuditDialog'
import PayDialog from './components/pay-dialog'
import detailMixin from '@/mixins/detailMixin'
import { mapGetters } from 'vuex'
const defaultFormData = {
  code: null,
  merchantId: null,
  amount: null,
  accountInformation: null,
  paymentDate: null,
  actualPaymentAmount: null,
  status: null,
}

export default {
  name: 'EarnestMoneyDetail',
  components: { BasicInfo, AuditRecord, AuditDialog, PayDialog },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2', '3'],
      isShow: false,
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      apis: {
        ...earnestMoneyApi
      },
      onSuccess: null,
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
    isAdd() {
      return !this.formData.id
    },
    // status 0保存；1待审核；2通过；3不通过；4作废
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    },
  },
  mounted() {
  },
  methods: {
    show(data, onSuccess) {
      this.isShow = true
      this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...data }))
      this.onSuccess = onSuccess
      if (this.isAdd) {
        if (this.$isAdmin) {
          this.formData.merchantId = this.$route.query.merchantId
        } else {
          this.formData.merchantId = this.userInfo.id
        }
      }
      this.pullData()
    },
    pullData() {
      const { id } = this.$route.query
      if (!id) {
        return
      }
      this.isLoading = true
      this.apis.getDetail(id).then(response => {
        this.originFormData = { ...response.data }
        this.formData = { ...response.data }
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate()
      ])
      return !validList.includes(false)
    },
    payHandle() {
      this.$refs.payDialogRef.show(this.formData, this.pullData)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
