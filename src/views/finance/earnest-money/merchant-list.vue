<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="searchForm.companyName" clearable placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="店铺名称" prop="storeName">
            <el-input v-model="searchForm.storeName" clearable placeholder="请输入店铺名称" />
          </el-form-item>
          <!-- <el-form-item v-if="!canChoose" label="审核状态" prop="status">
            <el-select v-model="searchForm.status" placeholder="请选择" clearable>
              <el-option
                v-for="item in $enums['auditStatus'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <template v-if="canChoose">
        <el-table-column
          v-if="multiple"
          fixed
          type="selection"
          label="选择"
          align="center"
          min-width="45"
        />
        <el-table-column
          v-else
          fixed
          label="选择"
          align="center"
          min-width="45"
        >
          <template slot-scope="scope">
            <el-radio :value="selectItems.length ? selectItems[0].id : ''" :label="scope.row.id" class="no-text-radio" />
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="商家编号"
        prop="code"
        min-width="130"
      />
      <el-table-column
        label="公司名称"
        prop="companyName"
        min-width="180"
      />
      <el-table-column
        label="店铺名称"
        prop="storeName"
        min-width="150"
      />
      <el-table-column
        label="联系人"
        prop="contactName"
        min-width="100"
      />
      <el-table-column
        label="对口商务"
        prop="counterPartBusinessName"
        min-width="100"
      />
      <el-table-column
        label="推荐人"
        prop="recommendedBy"
        min-width="100"
      />
      <el-table-column
        label="手机号码"
        prop="contactPhoneNo"
        min-width="110"
      />
      <el-table-column
        label="注册时间"
        prop="registrationTime"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.registrationTime | parseDate }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="状态"
        fixed="right"
        prop="status"
        min-width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('auditStatus') }}
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="!canChoose"
        fixed="right"
        label="操作"
        min-width="140"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="$router.push({
              name: `EarnestMoney`,
              query: {
                merchantId: scope.row.id
              }
            })"
          >查看保证金来往记录</el-button>
          <!-- <el-button
            type="text"
            size="small"
            @click="$router.push({
              path: `/enterprise/enterprise-live-broadcast-detail`,
              query: {
                id: scope.row.id
              }
            })"
          >直播信息</el-button> -->
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <!-- <el-dialog :title="oprateDialog.title" :visible.sync="oprateDialog.isShow" :close-on-click-modal="false" class="oprate-dialog" width="850px">
      <detail v-if="oprateDialog.isShow" v-model="oprateDialog.isShow" :dialog-data="oprateDialog.data" :dialog-type="oprateDialog.type" @success="pullData" />
    </el-dialog> -->
  </div>
</template>

<script>
import listMixin from '@/mixins/listMixin'
import earnestMoneyApi from '@/api/earnestMoney'
// import detail from './detail'

export default {
  name: 'EarnestMoneyMerchant',
  // components: { detail },
  mixins: [listMixin],
  props: {},
  data() {
    return {
      searchForm: {
        companyName: null,
        storeName: null,
        status: null
      },
      // 增删改查集合
      apis: {
        ...earnestMoneyApi,
        getPager: earnestMoneyApi.getMerchantPager
      }
    }
  },
  computed: {
    // dateArr: {
    //   get() {
    //     return this.searchForm.invoiceDateStart ? [this.searchForm.invoiceDateStart, this.searchForm.invoiceDateEnd] : []
    //   },
    //   set(val) {
    //     this.$set(this.searchForm, 'invoiceDateStart', val ? val[0] : null)
    //     this.$set(this.searchForm, 'invoiceDateEnd', val ? val[1] : null)
    //   }
    // }
  },
  mounted() {
    this.pullData()
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
