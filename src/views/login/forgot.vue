<template>
  <LoginLayout align="center">
    <div class="center-container">
      <div class="title">
        <span>忘记密码</span>
      </div>
      <div class="oprate-box">
        <el-form ref="form" :model="formData" :rules="rules" label-suffix=":" label-width="100px">
          <el-row>
            <el-col :span="21">
              <el-form-item
                label="手机号"
                prop="account"
              >
                <el-input v-model="formData.account" type="text" maxlength="15" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="21">
              <el-form-item label="短信验证码" prop="verificationCode">
                <!-- <div class="flex-content"> -->
                <el-input v-model="formData.verificationCode" type="number" :maxlength="6" placeholder="请输入短信验证码" autocomplete="off" style="padding-right:100px;" />
                <span class="get-code" :class="countdown > 0 ? 'forbid' : ''" @click="getSmsCode">{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}</span>
                <!-- </div> -->
              </el-form-item>
            </el-col>
            <el-col :span="21">
              <el-form-item label="新密码" prop="password">
                <el-input v-model="formData.password" :type="passwordType" placeholder="请输入新密码" autocomplete="off" />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-tooltip class="item" effect="dark" content="密码规则：8-16位,包含字母、数字、符号至少2种格式" placement="top">
                <div class="flex-content"><i class="el-icon-question" /></div>
              </el-tooltip>
            </el-col>
            <el-col :span="21">
              <el-form-item label="确认新密码" prop="password1">
                <el-input v-model="formData.password1" :type="passwordType" placeholder="请输入确认新密码" autocomplete="off" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="text-center">
            <el-link type="primary">密码规则：8-16位,包含字母、数字、符号至少2种格式</el-link>
          </div> -->
        </el-form>
      </div>
      <div class="btn-footer">
        <el-button :loading="isSubmiting" :disabled="!isFormValid" style="width:300px" size="normal" type="primary" @click="saveHandle">确定</el-button>
        <div class="links mt15">
          <el-link type="primary" href="#/login">返回登录</el-link>
        </div>
      </div>
    </div>
  </LoginLayout>
</template>

<script>
import LoginLayout from './layout.vue'
import { validPhone, validPassword, validURL } from '@/utils/validate'
import { getVerifyCode, verifyAccount, findPwd, sendForgotPwdCode } from '@/api/user'
import md5 from '@/utils/md5'
export default {
  components: { LoginLayout },
  data() {
    return {
      isSubmiting: false,
      countdown: 0,
      codeUrl: '',
      passwordType: 'password',
      formData: {
        verificationCode: '',
        account: '',
        password: '',
        password1: '',
      },
      rules: {
        account: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPhone(value)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPassword(value)) {
              callback(new Error('密码规则：8-16位,包含字母、数字、符号至少2种格式'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        password1: [
          { required: true, message: '请再次输入密码', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value === '') {
              callback(new Error('请再次输入密码'))
            } else if (value !== this.formData.password) {
              callback(new Error('两次输入密码不一致!'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        verificationCode: [
          { required: true, message: '请输入短信验证码', trigger: 'change' },
        ]
      },
    }
  },
  computed: {
    isFormValid() {
      return this.formData.account && this.formData.verificationCode && this.formData.password && this.formData.password1
    }
  },
  watch: {
    // 'formData.account': function(newVal, oldVal) {
    //   if (!validPhone(this.formData.account)) {
    //     this.formData.verificationCode = ''
    //   } else if (!this.codeUrl) {
    //     this.refreshCode()
    //   }
    // }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    // 获取短信校验码
    getSmsCode() {
      if (this.countdown > 0) return
      if (!validPhone(this.formData.account)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      this.countdown = 60
      sendForgotPwdCode(this.formData.account).then(res => {
        if (res.code === 200) {
          this.$message.success('验证码已发送')
          this.timer = setInterval(() => {
            this.countdown--
            if (this.countdown === 0) {
              clearInterval(this.timer)
            }
          }, 1000)
        } else {
          this.countdown = 0
          this.$message.error(res.msg)
          return
        }
      }).catch(ex => {
        this.countdown = 0
      })
    },
    // accountBlur() {
    //   if (!validPhone(this.formData.account)) {
    //     this.codeUrl = ''
    //   } else if (!this.codeUrl) {
    //     this.refreshCode()
    //   }
    // },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.isSubmiting = true
          findPwd({
            account: this.formData.account,
            verificationCode: this.formData.verificationCode,
            newPwd: md5(this.formData.password),
          }).then(res => {
            this.$message.success('密码重置成功')
            this.$router.push('/login')
          }).finally(() => {
            this.isSubmiting = false
          })
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
  .center-container {
    width: 600px;
  }
</style>
