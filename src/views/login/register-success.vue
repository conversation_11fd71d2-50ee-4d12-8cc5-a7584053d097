<!--
 * @Author: dong<PERSON><PERSON><PERSON>@163.com
 * @Date: 2023-12-05 15:39:31
-->
<template>
  <LoginLayout align="center">
    <div class="center-container">
      <!-- <div class="title">
        <span>用户注册</span>
      </div> -->
      <div class="step-box">
        <el-steps :active="3" finish-status="success" simple>
          <el-step title="账号注册" />
          <el-step title="企业认证" />
          <el-step title="注册成功" />
        </el-steps>
      </div>
      <div class="oprate-box">
        <div class="examine"><svg-icon icon-class="success" color="#029B00" class-name="success-icon" />注册成功</div>
        <div class="links mt15">
          <el-link type="primary" href="#/login">去登录</el-link>
        </div>
      </div>
    </div>
  </LoginLayout>
</template>

<script>
import LoginLayout from './layout.vue'
export default {
  components: { LoginLayout },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
  },
}
</script>

<style lang="scss" scoped>
  .examine {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    // line-height: 300px;
  }
  .center-container {
    width: 700px;
    .oprate-box {
      padding: 150px 0 !important;
      .links a {
        margin-top: 15px;
        font-size: 16px;
      }
    }
  }
  .success-icon {
    margin-right: 15px;
    font-size: 40px;
    position: relative;
    top: 5px;
  }
</style>
