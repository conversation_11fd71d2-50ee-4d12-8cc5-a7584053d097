<template>
  <LoginLayout>
    <el-form ref="form" :model="formData" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">
      <div class="top-links">
        <span class="link" @click="toggleLoginType">{{
          formData.loginType===2?'使用密码登录':'使用验证码登录'
        }}</span>
      </div>
      <div class="badge-container">
        <img v-if="$settings.logo" :src="$settings.logo" class="badge">
      </div>
      <div class="title-container">
        <h3 class="title">{{ $settings.title }} </h3>
      </div>
      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="account"
          v-model="formData.account"
          placeholder="登录名"
          name="account"
          type="text"
          tabindex="1"
          autocomplete="on"
          @keyup.enter.native="handleLogin"
        />
      </el-form-item>

      <el-form-item v-if="formData.loginType === 2" prop="verificationCode">
        <span class="svg-container">
          <svg-icon icon-class="mobile-code" style="font-size: 20px;" />
        </span>
        <el-input v-model="formData.verificationCode" type="number" :maxlength="6" placeholder="请输入短信验证码" autocomplete="off" style="padding-right:100px;" />
        <span class="get-code" :class="countdown > 0 ? 'forbid' : ''" @click="getSmsCode">{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}</span>
        <!-- </div> -->
      </el-form-item>
      <el-tooltip v-else v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
          <el-input
            :key="passwordType"
            ref="password"
            v-model="formData.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="on"
            @keyup.native="checkCapslock"
            @blur="capsTooltip = false"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
      </el-tooltip>
      <el-button :loading="loading" type="primary" size="normal" style="width:100%; margin-top:10px; margin-bottom:20px;" @click.native.prevent="handleLogin">登录</el-button>
      <div class="links">
        <el-link class="mr15" type="primary" href="#/forgot">忘记密码</el-link>
        <el-link type="primary" href="#/register">立即注册</el-link>
      </div>
      <div class="beian">
        <a class="c-gray" target="_blank" href="https://beian.miit.gov.cn">浙ICP备**********号</a>
      </div>
    </el-form>
  </LoginLayout>
</template>

<script>
import LoginLayout from './layout.vue'
import { validPhone, validPassword, validURL } from '@/utils/validate'
import { getVerifyCode, verifyAccount, findPwd, sendLoginCode } from '@/api/user'
export default {
  name: 'Login',
  components: { LoginLayout },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value.trim() === '') {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      // var reg = ^(?=.*\\d)(?!^[0-9]+$)(?!^[A-z]+$)(?!^[^A-z0-9]+$)^[^\\s\\u4e00-\\u9fa5]{8,16}
      // value.match(reg)
      if (value.length < 6) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    return {
      countdown: 0,
      formData: {
        loginType: 1, // 1账号密码登录，2短信登录
        account: '',
        password: '',
        verificationCode: '',
      },
      loginRules: {
        account: [{ required: true, trigger: 'change', validator: validateUsername }],
        password: [{ required: true, trigger: 'change', validator: validatePassword }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      checked: localStorage.getItem('rememberPwd') === 'true',
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        console.log('-------------$route handler------------------')
        console.log(route)
        console.log('-------------------------------')
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    if (process.env.NODE_ENV === 'development') {
      this.formData.account = '***********'
      this.formData.password = 'qwer123456'
    }
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.formData.account === '') {
      this.$refs.account.focus()
    } else if (this.formData.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    toggleLoginType() {
      if (this.formData.loginType === 1) {
        this.formData.loginType = 2
      } else {
        this.formData.loginType = 1
      }
    },
    // 获取短信校验码
    getSmsCode() {
      if (this.countdown > 0) return
      if (!validPhone(this.formData.account)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      this.countdown = 60
      sendLoginCode(this.formData.account).then(res => {
        if (res.code === 200) {
          this.$message.success('验证码已发送')
          this.timer = setInterval(() => {
            this.countdown--
            if (this.countdown === 0) {
              clearInterval(this.timer)
            }
          }, 1000)
        } else {
          this.countdown = 0
          this.$message.error(res.msg)
          return
        }
      }).catch(ex => {
        this.countdown = 0
      })
    },
    changeHandel(val) {
      localStorage.setItem('rememberPwd', val)
    },
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('user/login', this.formData)
            .then(() => {
              console.log('-------------handleLogin------------------')
              console.log(this.redirect)
              console.log(this.otherQuery)
              console.log('-------------------------------')
              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              this.loading = false
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>
<style lang="scss" scoped>
// $bg:#2d3a4b;
$bg:#ffff;
$dark_gray:#889aa4;
$light_gray:#eee;
.login-form {
  position: relative;
  width: 400px;
  height: 100%;
  max-width: 100%;
  padding: 25px 35px 0;
  margin: 0 auto;
  overflow: hidden;

  .el-form-item {
    // border: 1px solid rgba(255, 255, 255, 0.1);
    // background: rgba(0, 0, 0, 0.1);
    // border-radius: 5px;
    // color: #454545;
    .el-input {
      display: inline-block;
      // height: 47px;
      width: 85%;
    }

    border: 1px solid #dcdfe6;
    // background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }
  .top-links {
    margin-bottom: 20px;
    margin-right: -10px;
    cursor: pointer;
    // position: relative;
    // top: -5px;
    // top: -5px;
    text-align: right;
    .link {
      // float: right;
      font-size: 14px;
      color: #576B95;
    }
  }

  .badge-container {
    position: relative;
    margin-bottom: 10px;
    .badge {
      width: 70px;
      height: 70px;
      margin: 0 auto 10px auto;
      display: block;
    }
  }

  .title-container {
    position: relative;

    .title {
      font-size: 16px;
      // color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .links {
    text-align: center;
    margin-bottom: 40px;
  }
  .beian {
    position: relative;
    top: -20px;
    text-align: center;
    font-size: 12px;
    color:rgb(102, 102, 102)
  }
}
</style>
