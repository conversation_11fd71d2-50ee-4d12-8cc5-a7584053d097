<template>
  <LoginLayout align="center">
    <div class="center-container">
      <!-- <div class="title">
        <span>用户注册</span>
      </div> -->
      <div class="step-box">
        <el-steps :active="stepActive" finish-status="success" simple>
          <el-step title="账号注册" />
          <el-step title="企业认证" />
          <el-step title="注册成功" />
        </el-steps>
      </div>
      <div v-if="stepActive==1" class="oprate-box">
        <el-form ref="form1" :model="formData" :rules="rules1" label-suffix=":" label-width="100px">
          <el-row>
            <el-col :span="22">
              <el-form-item label="手机号" prop="account">
                <el-input v-model="formData.account" type="text" maxlength="15" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item label="短信验证码" prop="verificationCode">
                <!-- <div class="flex-content"> -->
                <el-input v-model="formData.verificationCode" type="number" :maxlength="6" placeholder="请输入短信验证码" autocomplete="off" style="padding-right:100px;" />
                <span class="get-code" :class="countdown > 0 ? 'forbid' : ''" @click="getSmsCode">{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}</span>
                <!-- </div> -->
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item label="密码 " prop="password">
                <el-input v-model="formData.password" :type="passwordType" placeholder="密码规则：8-16位,包含字母、数字、符号至少2种格式" style="padding-right: 25px" />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-tooltip class="item" effect="dark" content="密码规则：8-16位,包含字母、数字、符号至少2种格式" placement="top">
                <div class="flex-content"><i class="el-icon-question" /></div>
              </el-tooltip>
            </el-col>
            <el-col :span="22">
              <el-form-item label="重复密码" prop="password1">
                <el-input v-model="formData.password1" :type="passwordType" placeholder="请再次输入密码" autocomplete="off" style="padding-right: 25px" />
                <!-- <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span> -->
              </el-form-item>
            </el-col>
            <el-col v-if="isShowCode" :span="22">
              <el-form-item label="验证码" prop="verificationCode">
                <div class="flex-content">
                  <el-input v-model="formData.verificationCode" placeholder="请输入图形验证码" autocomplete="off" />
                  <img :src="codeUrl" class="code-img-width" @click="refreshCode">
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="22" class="mt5 mb15">
              <el-form-item label="推荐人" prop="recommendedBy">
                <el-input v-model="formData.recommendedBy" autocomplete="off" placeholder="请输入推荐人" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="text-center">
            <el-link type="primary">密码规则：8-16位,包含字母、数字、符号至少2种格式</el-link>
          </div> -->
          <div v-if="!$settings.isStaging" class="agreements">
            <el-checkbox v-model="isAgree">
              你已阅读并同意<a href="#/agreement/privacy" class="c-primary" target="_blank">《涂划算供应商管理-平台用户使用服务协议》</a> 、<a href="#/agreement/privacy" class="c-primary" target="_blank">《涂划算-隐私政策》</a>
            </el-checkbox>
          </div>
          <div class="btn-footer mt15">
            <el-button style="width:300px" size="normal" type="primary" :disabled="!isAgree" @click="goStep2">下一步</el-button>
            <div class="links mt15">
              <el-link type="primary" href="#/login">已有账号？返回登录</el-link>
            </div>
          </div>
        </el-form>
      </div>
      <div v-if="stepActive==2" class="oprate-box">
        <el-form ref="form2" :model="formData" :rules="rules2" label-suffix=":" label-width="140px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="formData.companyName" clearable placeholder="请输入公司名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
                <el-input v-model="formData.unifiedSocialCreditCode" clearable placeholder="请输入统一社会信用代码" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="formData.brand" placeholder="请输入品牌" autocomplete="off" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="店铺ID" prop="dyStoreId">
                <div class="flex-content">
                  <el-input v-model="formData.dyStoreId" placeholder="请输入店铺ID" autocomplete="off" />
                  <el-input v-model="formData.dyStoreUid" placeholder="请输入店铺UID" autocomplete="off" class="ml10" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="店铺名称" prop="storeName">
                <div class="flex-content">
                  <el-input v-model="formData.storeName" placeholder="请输入店铺名称" autocomplete="off" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="店铺链接" prop="storeLink">
                <el-input v-model="formData.storeLink" placeholder="格式：https://www.example.com" autocomplete="off" />
              </el-form-item>
            </el-col>
            <el-col :span="24" class="mt5">
              <el-form-item label="品牌联系人" prop="contactName">
                <div class="flex-content">
                  <el-input v-model="formData.contactName" placeholder="请输入品牌联系人姓名" autocomplete="off" />
                  <el-input v-model="formData.contactPhoneNo" placeholder="请输入品牌联系人手机号" autocomplete="off" class="ml10" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <div class="text-center">
            <el-link type="primary">密码规则：8-16位,包含字母、数字、符号至少2种格式</el-link>
          </div> -->
          <div class="btn-footer mt15">
            <div style="text-align: right;">
              <el-button :loading="isSubmiting" style="width:230px" size="normal" type="primary" plain @click="regHandle">跳 过</el-button>
              <el-button :loading="isSubmiting" style="width:230px" size="normal" type="primary" @click="regHandle">下一步</el-button>
            </div>
            <div class="links mt15">
              <el-link type="primary" href="#/login">已有账号？返回登录</el-link>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </LoginLayout>
</template>

<script>
import LoginLayout from './layout.vue'
import { validPhone, validPassword, validURL } from '@/utils/validate'
import { getVerifyCode, verifyAccount, register, sendRegisterCode } from '@/api/user'
import md5 from '@/utils/md5'
export default {
  components: { LoginLayout },
  data() {
    return {
      isSubmiting: false,
      countdown: 0,
      stepActive: 1,
      codeUrl: '',
      isShowCode: false,
      passwordType: 'password',
      isAgree: false,
      formData: {
        id: '',
        code: '',
        account: '',
        password: '',
        password1: '',
        verificationCode: '',
        companyName: '',
        unifiedSocialCreditCode: '',
        brand: '',
        storeName: '',
        storeRating: 0,
        storeLink: '',
        contactName: '',
        contactPhoneNo: '',
        email: '',
        registrationTime: '',
        status: 0,
        reasonForFailure: 0,
        reasonDetails: '',
        reviewId: 0,
        reviewBy: '',
        reviewTime: '',
        pictureIds: '',
        recommendedBy: ''
      },
      rules1: {
        account: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPhone(value)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPassword(value)) {
              callback(new Error('密码规则：8-16位,包含字母、数字、符号至少2种格式'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        password1: [
          { required: true, message: '请再次输入密码', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value === '') {
              callback(new Error('请再次输入密码'))
            } else if (value !== this.formData.password) {
              callback(new Error('两次输入密码不一致!'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        // verificationCode: [
        //   { required: true, message: '请输入图形校验码', trigger: 'change' },
        // ],
        verificationCode: [
          { required: true, message: '请输入短信验证码', trigger: 'change' },
        ]
      },
      rules2: {
        // companyName: [
        //   { required: true, message: '请输入公司名称', trigger: 'change' },
        // ],
        // unifiedSocialCreditCode: [
        //   { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        // ],
        // brand: [
        //   { required: true, message: '请输入品牌', trigger: 'change' },
        // ],
        // dyStoreId: [
        //   { required: true, message: '请输入店铺ID', trigger: 'change' },
        // ],
        // storeName: [
        //   { required: true, message: '请输入店铺名称', trigger: 'change' },
        // ],
        storeLink: [
          // { required: false, message: '请输入店铺链接', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value && !validURL(value)) {
              callback(new Error('请输入正确的链接地址（格式：https://www.example.com）'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        contactName: [
          { required: false, message: '请输入品牌联系人姓名', trigger: 'change' },
          { validator: (rule, value, callback) => {
            // if (!this.formData.contactPhoneNo) {
            //   callback(new Error('请输入联系手机号'))
            // } else
            if (this.formData.contactPhoneNo && !validPhone(this.formData.contactPhoneNo)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ]
      }
    }
  },
  // watch: {
  //   'formData.account'() {
  //     this.accountBlur()
  //   }
  // },
  mounted() {
    if (this.$settings.isStaging) {
      this.isAgree = true
    }
    // this.accountBlur()
  },
  methods: {
    // 获取短信校验码
    getSmsCode() {
      if (this.countdown > 0) return
      if (!validPhone(this.formData.account)) {
        this.$message.error('请输入正确的手机号')
        return
      }
      this.countdown = 60
      sendRegisterCode(this.formData.account).then(res => {
        if (res.code === 200) {
          this.$message.success('验证码已发送')
          this.timer = setInterval(() => {
            this.countdown--
            if (this.countdown === 0) {
              clearInterval(this.timer)
            }
          }, 1000)
        } else {
          this.countdown = 0
          this.$message.error(res.msg)
          return
        }
      }).catch(ex => {
        this.countdown = 0
      })
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    // accountBlur() {
    //   this.isShowCode = validPhone(this.formData.account)
    //   if (!this.isShowCode) {
    //     this.codeUrl = ''
    //   } else if (!this.codeUrl) {
    //     this.refreshCode()
    //   }
    // },
    // refreshCode() {
    //   console.log('refreshcolde')
    //   this.codeUrl = getVerifyCode(this.formData.account)
    //   // this.codeUrl = getVerifyCode(this.formData.account).then(response => {
    //   //   this.codeUrl = response
    //   // }).finally(() => {
    //   // })
    // },
    goStep2() {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          // if (!this.isAgree) {
          //   return this.$message.error('请先同意用户协议')
          // }
          verifyAccount({
            account: this.formData.account,
            verificationCode: this.formData.verificationCode,
          }).then(res => {
            this.stepActive += 1
          }, () => {
            this.refreshCode()
          })
        }
      })
    },
    regHandle() {
      this.$refs.form2.validate((valid) => {
        if (valid) {
          this.isSubmiting = true
          register({
            ...this.formData,
            password: md5(this.formData.password),
          }).then(res => {
            this.$router.push('/register-success')
          }).finally(() => {
            this.isSubmiting = false
          })
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
  $dark_gray:#889aa4;
  .code-img-width {
    width: 40%;
    height: 47px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .center-container {
    width: 700px;
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 4px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .agreements {
    ::v-deep .el-checkbox__label {
      font-size: 12px;
      color: #666;
    }
  }
</style>
