<template>
  <div class="login-container" :class="[align, $settings.isStaging ? 'bt' : 'ths']">
    <div v-if="$settings.isStaging" class="bruce">
      <ul class="bubble-bgwall">
        <li v-for="(item, index) in 15" :key="index">Hi</li>
      </ul>
    </div>
    <div class="form-wrapper">
      <slot />
    </div>
  </div>
</template>

<script>

export default {
  name: 'LoginLayout',
  props: {
    /**
     * 布局对齐方式
     * right/center
    */
    align: {
      type: String,
      default: 'right'
    }
  }
}
</script>
<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#000;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-form-item__label {
    line-height: 37px;
  }
  .el-input {
    display: inline-block;
    // height: 37px;
    // width: 85%;

    input {
      // background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 37px;
      // caret-color: $cursor;
      caret-color: #999;

      // &:-webkit-autofill {
      //   box-shadow: 0 0 0px 1000px $bg inset !important;
      //   -webkit-text-fill-color: $cursor !important;
      // }
      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #ffff inset !important;
        -webkit-text-fill-color: #000 !important;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
@import "~@/styles/element-variables.scss";
// $bg:#2d3a4b;
$bg:#ffff;
$dark_gray:#889aa4;
$light_gray:#eee;

.login-container {
  position: relative;
	min-width: 550px;
	height: 100%;
	min-height: 500px;
	// background-color: #eeeeee;
	// background-position: 50%;
	// background-size: 100% 100%;
  // background: url('../../assets/login-bg.jpg') no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  &.ths {
    background: url('../../assets/login-bg.jpg') no-repeat;
    background-size: 100% auto;
  }
  &.center{
    justify-content: center;
  }
  &.right .form-wrapper{
    margin-right: 10%;
  }
  .bruce {
		overflow: hidden;
		position: absolute;
    // top: 0;
    // left: 0;
		margin: 0 auto;
		width: 100%;
		height: 100%;
		background-image: linear-gradient(270deg, #8146b4, #6990f6);
		.bubble-bgwall {
			li {
				display: flex;
				position: absolute;
				bottom: -200px;
				justify-content: center;
				align-items: center;
				border-radius: 10px;
				width: 50px;
				height: 50px;
				background-color: rgba(#fff, 0.15);
				color: #ccc;
				animation: bubble 15s infinite;
				&:nth-child(1) {
					left: 10%;
				}
				&:nth-child(2) {
					left: 20%;
					width: 90px;
					height: 90px;
					animation-duration: 7s;
					animation-delay: 2s;
				}
				&:nth-child(3) {
					left: 25%;
					animation-delay: 4s;
				}
				&:nth-child(4) {
					left: 40%;
					width: 60px;
					height: 60px;
					background-color: rgba(#fff, 0.3);
					animation-duration: 8s;
				}
				&:nth-child(5) {
					left: 70%;
				}
				&:nth-child(6) {
					left: 80%;
					width: 120px;
					height: 120px;
					background-color: rgba(#fff, 0.2);
					animation-delay: 3s;
				}
				&:nth-child(7) {
					left: 32%;
					width: 160px;
					height: 160px;
					animation-delay: 2s;
				}
				&:nth-child(8) {
					left: 55%;
					width: 40px;
					height: 40px;
					font-size: 12px;
					animation-duration: 15s;
					animation-delay: 4s;
				}
				&:nth-child(9) {
					left: 25%;
					width: 40px;
					height: 40px;
					background-color: rgba(#fff, 0.3);
					font-size: 12px;
					animation-duration: 12s;
					animation-delay: 2s;
				}
				&:nth-child(10) {
					left: 85%;
					width: 160px;
					height: 160px;
					animation-delay: 5s;
				}
			}
		}

		@keyframes bubble {
			0% {
				opacity: 0.5;
				transform: translateY(0) rotate(45deg);
			}
			25% {
				opacity: 0.75;
				transform: translateY(-400px) rotate(90deg);
			}
			50% {
				opacity: 1;
				transform: translateY(-600px) rotate(135deg);
			}
			100% {
				opacity: 0;
				transform: translateY(-1000px) rotate(180deg);
			}
		}
	}

  background-size: cover;
  // background-image: url("../../assets/login-bg.png");

  .form-wrapper {
    z-index: 10;
    border-radius: 8px;
    background: #fff;
    overflow: hidden;
    // .right-form {
    //   // width: 850px;
    //   // height: 490px;
    //   // position: absolute;
    //   display: inline-block;
    //   background-color: #ffff;
    //   border-radius: 5px;
    // }
    ::v-deep .center-container {
      border: 1px solid #eee;
      padding-bottom: 30px;
      .flex-content {
        position: relative;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-left: 5px;
        i {
          font-size: 18px;
          color: #337ef9;
        }
      }
      .title {
        width: 100%;
        height: 50px;
        background: #F5F7FA;
        font-size: 16px;
        font-weight: bold;
        line-height: 50px;
        padding-left: 20px;
        box-sizing: border-box;
      }
      .oprate-box {
        width: 550px;
        margin: 0 auto;
        padding: 35px 25px 20px 25px;
        .el-form-item {
          margin-bottom: 15px;
          .el-form-item__content {
            height: 40px;
          }
        }
        .el-input {
          border: 1px solid #dcdfe6;
          // background: rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          color: #454545;
          overflow: hidden;
        }
        .el-icon-question {
          margin-top: 11px;
          margin-left: 5px;
        }
      }
      .btn-footer {
        text-align: center;
      }
    }
    ::v-deep {
      .links {
        text-align: center;
      }
      .get-code {
        position: absolute;
        right: 13px;
        top: 3px;
        white-space: nowrap;
        margin-left: 10px;
        white-space: nowrap;
        color: $--color-primary;
        cursor: pointer;
        &.forbid {
          cursor: none;
          color: #999;
        }
      }
    }
  }
}
</style>
