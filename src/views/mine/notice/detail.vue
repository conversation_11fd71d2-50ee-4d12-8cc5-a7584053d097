<template>
  <div>
    <el-form ref="form" class="oprate-form111 mr20" :model="formData" :rules="rules" disabled label-position="right" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="发送用户">
            <el-input v-model="formData.sentUserName" readonly />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="接收用户">
            <el-input v-model="formData.receiveUserName" readonly />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="发送时间">
            <el-date-picker
              v-model="formData.sendTime"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%;"
              readonly
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标题">
            <el-input v-model="formData.title" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否已读">
            <!-- {{ formData.isRead ? '已读' : '未读' }} -->
            <!-- <el-switch v-model="formData.isRead" disabled /> -->
            <svg-icon v-if="formData.isRead" class-name="email-icon c-gray" icon-class="email_open" title="已读" />
            <svg-icon v-else class-name="email-icon c-warning not-read" icon-class="email-fill" title="未读" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容">
            <el-input v-model="formData.content" type="textarea" readonly />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="发送结果">
            <el-input v-model="formData.result" readonly />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="接收地址">
            <el-input v-model="formData.receiveAddress" readonly />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <div class="dialog-footer mt10 mb10">
      <el-button :loading="isSubmiting" plain type="primary" @click="close">取 消</el-button>
      <!-- <el-button :loading="isSubmiting" type="primary" @click="saveHandle">确 定</el-button> -->
    </div>
  </div>
</template>

<script>
import noticeLogApi from '@/api/notice/log'
import detailMixin from '@/mixins/detailMixin'
const defaultFormData = {
  id: null,
  code: null,
  noticeType: null,
  name: null,
  title: null,
  content: null,
  jumpUrl: null,
  isEnabled: true,
}
export default {
  mixins: [detailMixin],
  props: {
  },
  data() {
    return {
      // 增删改查集合
      apis: {
        ...noticeLogApi
      },
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      rules: {
        // code: [
        //   { required: true, message: '请输入编码', trigger: 'change' }
        // ],
        name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'change' }
        ]
      },
      onSuccess: null
    }
  },
  computed: {
  },
  mounted() {
    if (this.dialogData) {
      this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...this.dialogData }))
      this.pullData()
      console.log(this.formData)
    }
  },
  methods: {
    // close() {
    //   this.isShow = false
    // },
    // show(data, onSuccess) {
    //   this.isShow = true
    //   this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...data }))
    //   this.onSuccess = onSuccess
    // },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          resolve(valid)
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.email-icon {
  font-size: 19px;
}
</style>
