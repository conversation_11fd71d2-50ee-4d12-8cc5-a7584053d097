<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item label="标题">
            <el-input v-model.trim="searchForm.title" placeholder="标题" clearable />
          </el-form-item>
          <el-form-item label="是否已读">
            <el-select
              v-model="searchForm.isRead"
              clearable
              placeholder="请选择"
              @clear="searchForm.isRead=undefined"
            >
              <el-option
                label="已读"
                :value="true"
              />
              <el-option
                label="未读"
                :value="false"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="接收人">
            <el-input v-model.trim="searchForm.receiveUserName" placeholder="接收人" />
          </el-form-item> -->
          <!-- <el-form-item label="发送人">
            <el-autocomplete
              v-model="searchForm.sentUserName"
              class="inline-input"
              :fetch-suggestions="querySearch"
              placeholder="请输入发送人姓名"
              value-key="name"
              value="id"
              :trigger-on-focus="false"
              @select="handleSendUserSelect"
            />
          </el-form-item>
          <el-form-item label="接收人">
            <el-autocomplete
              v-model="searchForm.receiveUserName"
              class="inline-input"
              :fetch-suggestions="querySearch"
              placeholder="请输入接收人姓名"
              value-key="name"
              value="id"
              :trigger-on-focus="false"
              @select="handleReciveUserSelect"
            />
          </el-form-item> -->
          <!--
          <el-form-item label="消息状态">
            <el-select
              v-model="searchForm.status"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in $enums['noticeStatus'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <!-- <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button> -->
          </el-form-item>
          <div>
            <el-button type="warning" @click="batchRead()">设置已读</el-button>
          </div>
        </el-form>
      </div>
    </div>
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <template>
        <el-table-column
          fixed
          type="selection"
          label="选择"
          align="center"
          min-width="45"
        />
      </template>
      <el-table-column
        label="是否已读"
        align="center"
        width="70"
      >
        <template slot-scope="scope">
          <svg-icon v-if="scope.row.isRead" class-name="email-icon c-gray" icon-class="email_open" title="已读" />
          <svg-icon v-else class-name="email-icon c-warning not-read" icon-class="email-fill" title="点击设置已读" @click="batchRead(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column
        prop="sentUserName"
        label="发送用户"
        min-width="80"
      />
      <el-table-column label="标题" prop="title" min-width="100" />
      <el-table-column label="内容" min-width="300">
        <template slot-scope="scope">
          <span>{{ scope.row.content }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发送时间"
        width="140"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.sendTime | parseTime }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="result"
        label="发送结果"
        min-width="100"
      /> -->
      <!-- <el-table-column label="状态" min-width="80">
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('noticeStatus') }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        prop="receiveAddress"
        label="接收地址"
        min-width="100"
      /> -->
      <!-- <el-table-column
        label="操作"
        width="80"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="() => {
              batchRead(scope.row)
              editHandle(scope.row)
            }"
          >查看</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <el-dialog title="详细详情" :visible.sync="oprateDialog.isShow" :close-on-click-modal="false" class="oprate-dialog" width="700px">
      <Detail v-if="oprateDialog.isShow" v-model="oprateDialog.isShow" :dialog-data="oprateDialog.data" :dialog-type="oprateDialog.type" @success="pullData" />
    </el-dialog>
  </div>
</template>

<script>
import { userList } from '@/api/user'
import listMixin from '@/mixins/listMixin'
import noticeLogApi from '@/api/notice/log'
import { parseTime } from '@/utils'
import Detail from './detail'
export default {
  name: 'MyNoticeLog',
  components: {
    Detail
  },
  mixins: [listMixin],
  data() {
    return {
      searchForm: {
        sentUserId: undefined,
        sentUserName: undefined,
        receiveUserId: undefined,
        receiveUserName: undefined,
        noticeType: undefined,
        status: undefined,
        isRead: undefined,
        title: undefined,
      },
      // 增删改查集合
      apis: {
        ...noticeLogApi,
        getPager: noticeLogApi.getMyPager
      },
      userQuery: {
        loginName: null,
        name: null,
        currentPage: 1,
        pageSize: 20
      },
    }
  },
  mounted() {
    this.pullData()
  },
  methods: {
    // querySearch(queryString, cb) {
    //   userList(this.userQuery).then(response => {
    //     var restaurants = response.data.list
    //     var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
    //     // 调用 callback 返回建议列表的数据
    //     console.info(results)
    //     cb(results)
    //   })
    // },
    // createFilter(queryString) {
    //   return (restaurant) => {
    //     return (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
    //   }
    // },
    // handleSendUserSelect(item) {
    //   this.searchForm.sentUserId = item.id
    // },
    // handleReciveUserSelect(item) {
    //   this.searchForm.receiveUserId = item.id
    // },
    // 批量设置已读
    batchRead(row) {
      if (!this.selectItems.length && !row) {
        this.$message.warning('请选择要操作的数据')
        return
      }
      if (row && row.isRead) {
        return
      }
      let params
      if (row) {
        params = [row.id]
      } else {
        params = this.selectItems.map(item => item.id)
      }
      noticeLogApi.batchRead(params).then(res => {
        if (res.code === 200) {
          this.$store.dispatch('user/getUnReadNoticeCount')
          if (!row) {
            this.$message.success('操作成功')
          }
          this.pullData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (!row.isRead) {
        return 'not-read-row'
      }
      return ''
    },
    rowClick(row) {
      this.batchRead(row)
      row.jumpUrl = row.jumpUrl || ''
      // row.jumpUrl = 'http://www.baidu.com'
      // row.jumpUrl = '/enterprise/detail?id=37'
      if (row.jumpUrl.startsWith('http')) {
        window.open(row.jumpUrl, '_blank')
      } else {
        this.$router.push(row.jumpUrl)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.email-icon {
  font-size: 19px;
  &.not-read {
    cursor: pointer;
  }
}
::v-deep .not-read-row {
  font-weight: bolder;
}
</style>
