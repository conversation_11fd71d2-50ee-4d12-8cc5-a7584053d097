<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item v-if="$isAdmin" label="公司名称" prop="companyName">
            <el-input v-model="searchForm.companyName" clearable placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="sku名称" prop="skuName">
            <el-input v-model="searchForm.skuName" clearable placeholder="请输入sku名称" />
          </el-form-item>
          <el-form-item label="商品品牌" prop="brand">
            <el-input v-model="searchForm.brand" clearable placeholder="请输入商品品牌" />
          </el-form-item>
          <el-form-item label="商品类目" prop="商品类目">
            <ProductCategorySelect v-model="searchForm.productCategoryCode" />
          </el-form-item>
          <el-form-item
            label="抖音商品类目"
            prop="dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="searchForm.dyProductCategories" />
          </el-form-item>
          <template v-if="$isAdmin">
            <el-form-item
              label="直播场次"
              prop="liveBroadcastPlanId"
            >
              <LiveBroadcastPlanChoose v-model="searchForm.liveBroadcastPlanId" :show-btn="false" />
            </el-form-item>
            <el-form-item label="完善状态" prop="perfectStatus">
              <el-select v-model="searchForm.perfectStatus" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['liveBroadcastPerfectStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商务" prop="businessAudit">
              <el-select v-model="searchForm.businessAudit" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['auditStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="运营" prop="operationsAudit">
              <el-select v-model="searchForm.operationsAudit" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['auditStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="合规" prop="complianceAudit">
              <el-select v-model="searchForm.complianceAudit" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['auditStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="客服" prop="customerServiceAudit">
              <el-select v-model="searchForm.customerServiceAudit" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['auditStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="法务" prop="legalAffairsAudit">
              <el-select v-model="searchForm.legalAffairsAudit" placeholder="请选择" clearable>
                <el-option
                  v-for="item in $enums['auditStatus'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
            <!-- <el-button
              v-if="$isAdmin && !canChoose"
              type="primary"
              icon="el-icon-plus"
              plain
              @click="$router.push({
                name: 'InvitationAdd'
              })"
            >新增</el-button> -->
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- <el-button type="primary" @click="addHandle">添加</el-button> -->
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      border
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <template v-if="canChoose">
        <el-table-column
          v-if="multiple"
          fixed
          type="selection"
          label="选择"
          align="center"
          min-width="45"
        />
        <el-table-column
          v-else
          fixed
          label="选择"
          align="center"
          min-width="45"
        >
          <template slot-scope="scope">
            <el-radio :value="selectItems.length ? selectItems[0].id : ''" :label="scope.row.id" class="no-text-radio" />
          </template>
        </el-table-column>
      </template>
      <el-table-column
        fixed
        label="序号"
        type="index"
        min-width="80"
      />
      <el-table-column
        label="单据编码"
        prop="code"
        min-width="140"
      />
      <el-table-column
        label="公司名称"
        prop="companyName"
        min-width="200"
      />
      <el-table-column
        label="sku名称"
        prop="skuName"
        min-width="200"
      />
      <el-table-column
        label="商品类目"
        prop="productCategoryName"
        min-width="120"
      />
      <el-table-column
        label="抖音商品类目"
        prop="dyProductCategories"
        min-width="130"
      >
        <template slot-scope="scope">
          {{ (scope.row.dyProductCategories || []).map(item => item.name).join('、') }}
        </template>
      </el-table-column>
      <el-table-column
        label="商品品牌"
        prop="brand"
        min-width="120"
      />
      <el-table-column
        label="提报时间"
        prop="registrationTime"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.registrationTime | parseDate }}
        </template>
      </el-table-column>
      <template v-if="$isAdmin">
        <el-table-column
          label="商务"
          prop="businessAudit"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.businessAudit | toEnumName('auditStatus') }}
          </template>
        </el-table-column>
        <el-table-column
          label="运营"
          prop="operationsAudit"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.operationsAudit | toEnumName('auditStatus') }}
          </template>
        </el-table-column>
        <el-table-column
          label="合规"
          prop="complianceAudit"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.complianceAudit | toEnumName('auditStatus') }}
          </template>
        </el-table-column>
        <el-table-column
          label="客服"
          prop="customerServiceAudit"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.customerServiceAudit | toEnumName('auditStatus') }}
          </template>
        </el-table-column>
        <el-table-column
          label="法务"
          prop="legalAffairsAudit"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.legalAffairsAudit | toEnumName('auditStatus') }}
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="完善状态"
        fixed="right"
        prop="perfectStatus"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.perfectStatus | toEnumName('liveBroadcastPerfectStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="!canChoose"
        fixed="right"
        label="操作"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-button
            v-if="$isAdmin && !scope.row.hasPushed"
            v-permission="'button:inviteRegistration:pushBatch'"
            type="text"
            size="small"
            @click="pushBatch([scope.row.id])"
          >邀请</el-button>
          <el-button
            type="text"
            size="small"
            @click="$router.push({
              name: 'InvitationDetail',
              query: {
                id: scope.row.id
              }
            })"
          >查看</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <!-- <el-dialog :title="oprateDialog.title" :visible.sync="oprateDialog.isShow" :close-on-click-modal="false" class="oprate-dialog" width="850px">
      <detail v-if="oprateDialog.isShow" v-model="oprateDialog.isShow" :dialog-data="oprateDialog.data" :dialog-type="oprateDialog.type" @success="pullData" />
    </el-dialog> -->
  </div>
</template>

<script>
import listMixin from '@/mixins/listMixin'
import invitationRegisterApi from '@/api/invitationRegister'
import ProductCategorySelect from '@/components/ProductCategorySelect'
// import detail from './detail'

export default {
  name: 'Invitation',
  components: { ProductCategorySelect },
  mixins: [listMixin],
  props: {},
  data() {
    return {
      searchForm: {
        companyName: '',
        skuName: '',
        brand: '',
        productCategoryCode: '',
        dyProductCategories: [],
        status: '',
        businessAudit: '',
        operationsAudit: '',
        complianceAudit: '',
        customerServiceAudit: '',
        liveBroadcastPlanId: '',
      },
      // 增删改查集合
      apis: {
        ...invitationRegisterApi
      }
    }
  },
  created() {},
  mounted() {
    this.pullData()
    // this.initDict()
  },
  methods: {
    // 推送
    pushBatch(ids) {
      this.$confirm('你确定向商家发送邀请?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.apis.pushBatch(ids).then(reponse => {
          if (reponse.code === 200) {
            this.$message({
              showClose: true,
              type: 'success',
              message: '邀请成功!'
            })
            this.pullData()
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
        })
      }).catch(() => {
        // this.$message({
        //   type: 'info',
        //   message: '已取消删除'
        // })
      })
    },
    // initDict() {
    //   enterpriseApi.monitorStationType.getDictList({}).then(
    //     response => {
    //       this.siteClassList = response.data
    //     }
    //   )
    // },
  }
}
</script>

<style lang="scss" scoped>

</style>
