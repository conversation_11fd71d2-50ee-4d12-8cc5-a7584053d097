<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="报名信息">
        <el-collapse v-model="activeNames">
          <BasicInfo ref="basicInfoRef" :form-data="formData" name="1" :can-edit="!$isAdmin && canEdit" :show-deleted="showDeleted" :can-edit-rating-files="canEditRatingFiles" :is-add="isAdd" type="perfect" />
          <ProductInfo
            v-for="(item, index) in formData.skus || []"
            :key="index"
            ref="productInfoRef"
            :can-edit="canEdit"
            :form-data="item"
            :can-edit-files="canEditFiles"
            :name="`sku信息${index + 1}`"
            :title="`sku信息${index + 1}`"
            :can-remove="formData.skus.length > 1"
            class="mt15"
            :show-deleted="showDeleted"
            @removeSku="removeSku"
          />
        </el-collapse>
        <div class="dialog-footer mt10">
          <template v-if="$isAdmin">
            <el-button v-if="!showDeleted" v-permission="'button:inviteRegistration:showDeletedPics'" size="small" type="primary" plain @click.stop="showDeleted=!showDeleted">
              <div>显示已删除附件</div>
            </el-button>
            <el-button v-else v-permission="'button:inviteRegistration:showDeletedPics'" size="small" type="primary" plain @click.stop="showDeleted=!showDeleted">
              <div>隐藏已删除附件</div>
            </el-button>
            <el-button v-if="formData.perfectStatus !== 4 && !formData.salesContractId" v-permission="'button:inviteRegistration:updateSalesContract'" :loading="isSubmiting" type="primary" @click="chooseSkuContract()">匹配合同</el-button>
            <el-button v-if="formData.perfectStatus !== 4 && !formData.salesCollectionId" v-permission="'button:inviteRegistration:updateCollection'" :loading="isSubmiting" type="primary" @click="chooseCollection()">匹配收款单</el-button>
            <template v-if="formData.perfectStatus === 3">
              <AuditBtn v-permission="'button:inviteRegistration:businessAudit'" level="businessAudit" :audit-api="apis.businessAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
              <AuditBtn v-permission="'button:inviteRegistration:complianceAudit'" level="complianceAudit" :audit-api="apis.complianceAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" :status-arr="[1, 2, 3]" />
              <AuditBtn v-permission="'button:inviteRegistration:operationsAudit'" level="operationsAudit" :audit-api="apis.operationsAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
              <AuditBtn v-permission="'button:inviteRegistration:customerServiceAudit'" level="customerServiceAudit" :audit-api="apis.customerServiceAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" :status-arr="[1, 2, 3]" />
              <AuditBtn v-permission="'button:inviteRegistration:legalAffairsAudit'" level="legalAffairsAudit" :audit-api="apis.legalAffairsAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" :status-arr="[1, 2, 4]" />
              <AuditBtn v-permission="'button:inviteRegistration:mechanismLeaderAudit'" level="mechanismLeaderAudit" :audit-api="apis.mechanismLeaderAudit" :is-enterprise-data="true" :audit-records="formData.auditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
            </template>
            <el-dropdown v-if="$isAdmin && formData.perfectStatus === 4" v-permission="'button:inviteRegistration:addSkuBatch'" :loading="isSubmiting" class="mr10" @command="addToMyPalletHandle">
              <el-button type="primary" @click="getHasPallets()">
                加入我的货盘表<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in myPalletList" :key="index" :command="item.id">{{ item.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button v-if="formData.perfectStatus === 4" v-permission="'button:liveBroadcast:blindBuying:save'" :loading="isSubmiting" type="primary" @click="createBlindBuyingHandle()">发起盲拍</el-button>
            <!-- <el-button v-if="!isAdd && canEdit" :loading="isSubmiting" type="primary" @click="invalidHandle(5)">作 废</el-button> -->
          </template>
          <template v-else>
            <!-- <el-button v-if="canEdit" :loading="isSubmiting" type="warning" plain @click="addSku">新增sku</el-button> -->
            <el-button v-if="canEdit || canEditRatingFiles" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
            <el-button v-if="canEdit && !(formData.needUploadStoreRatingImages || formData.needSupplementQualificationDocuments)" :loading="isSubmiting" type="primary" @click="submitHandle()">提交</el-button>
          </template>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.auditRecords && formData.auditRecords.length" label="审核记录">
        <AuditRecord :data="formData.auditRecords" />
      </el-tab-pane>
      <!-- <el-tab-pane v-if="formData.history && formData.history.length" label="历史直播记录">
        <BroadcastHistory :data="formData.history" />
      </el-tab-pane> -->
    </el-tabs>
    <CommonChooseDialog ref="saleContractChooseDialogRef" title="选择销售合同" :component="SaleContract" :multiple="false" :init-search-params="{status: 2, merchantId: formData.merchantId }" />
    <CommonChooseDialog ref="collectionChooseDialogRef" title="选择收款单" :component="Collection" :multiple="false" :init-search-params="{status: 2, merchantId: formData.merchantId }" />
  </div>
</template>

<script>
import palletApi from '@/api/pallet'
import invitationRegisterApi from '@/api/invitationRegister'
import BasicInfo from '../components/basic-info'
import ProductInfo from '../components/product-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import BroadcastHistory from '../components/broadcast-history.vue'
import detailMixin from '@/mixins/detailMixin'
import CommonChooseDialog from '@/components/ChooseDialogs/CommonChooseDialog'
import SaleContract from '@/views/contract/sale/index.vue'
import Collection from '@/views/finance/collection/index.vue'
import { mapGetters } from 'vuex'
const skuDefault = {
  registrationId: '',
  registrationType: 1, // 报名类型：1邀请报名，2商品提报
  merchantId: '',
  merchantStoreId: '',
  code: '',
  name: '',
  productCategoryCode: '',
  productCategoryName: '',
  label: '',
  acceptSubsidies: false,
  skuLink: '',
  liveBroadcastPrice: '',
  commission: '',
  commissionRatio: '',
  inventory: '',
  preSaleDays: '',
  preSaleInventory: '',
  totalInventory: '',
  value: '',
  waitingTime: '',
  isPostageFree: false,
  packagesDeliveredVolume: '',
  historicalSalesVolume: '',
  afterSalesTerms: '',
  pictureIds: '',
  skuProducts: [
  ],
  merchant: {
    brand: '',
  }
}
const defaultFormData = {
  id: '',
  merchantId: '',
  merchantStoreId: '',
  code: '',
  registrationTime: '',
  status: '',
  pictureIds: '',
  merchant: {},
  merchantCompany: {},
  merchantStore: {},
  skus: [
    { ...skuDefault }
  ]
}

export default {
  name: 'InvitationDetail',
  components: { BasicInfo, ProductInfo, AuditRecord, BroadcastHistory, CommonChooseDialog },
  mixins: [detailMixin],
  data() {
    return {
      SaleContract,
      Collection,
      showDeleted: false, // 是否显示商家已删除图片
      activeNames: ['1'],
      // 增删改查集合
      apis: {
        ...invitationRegisterApi
      },
      auditApi: null,
      auditTitle: '',
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      myPalletList: []
    }
  },
  computed: {
    // status 0未报名；1已作废；2待提交；3待审核；4审核通过；5审核不通过
    canEdit() {
      return (this.isAdd || [0, 2, 5].indexOf(this.formData.perfectStatus) > -1) && !this.$isAdmin
    },
    // 是否能编辑评分图片
    canEditRatingFiles() {
      return this.canEdit || this.formData.needUploadStoreRatingImages || this.formData.needSupplementQualificationDocuments
    },
    // 是否能编辑图片
    canEditFiles() {
      return this.canEdit || this.formData.needSupplementQualificationDocuments
    },
    ...mapGetters([
      'userInfo'
    ])
  },
  watch: {
    'formData.skus': {
      immediate: true,
      handler(skus) {
        if (skus.length) {
          this.activeNames = ['1'].concat(skus.map((sku, index) => `sku信息${index + 1}`))
        }
      }
    }
  },
  async mounted() {
    if (!this.$isAdmin && this.isAdd) {
      this.formData.merchantId = this.userInfo.id
      this.formData.merchant.brand = this.userInfo.brand
    }
    await this.pullData()
    if (this.$isAdmin && this.formData.perfectStatus === 4) {
      this.getMyPallet()
    }
  },
  methods: {
    getMyPallet() {
      palletApi.getPager({
        pageIndex: 1,
        pageSize: 1000
      }).then(res => {
        this.myPalletList = res.data.list
      })
    },
    removeSku(sku) {
      const index = this.formData.skus.indexOf(sku)
      this.formData.skus.splice(index, 1)
    },
    addSku() {
      this.formData.skus.push({ ...skuDefault })
    },
    getHasPallets() {
      if (!this.myPalletList || !this.myPalletList.length) {
        this.$message.warning('您还没有货盘，请先创建货盘')
      }
    },
    // 加入我的货盘
    addToMyPalletHandle(cargoCombinationId) {
      this.$confirm('确定加入货盘?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        palletApi.batchAddSkuToPallet(this.formData.skus.map(item => {
          return {
            skuId: item.id,
            cargoCombinationId
          }
        })).then(res => {
          this.$message.success('加入成功')
          this.pullData()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    // 发起盲拍
    createBlindBuyingHandle() {
      this.$router.push({
        name: 'BlindBuyAdd',
        query: {
          signUpId: this.formData.id
        }
      })
    },
    // 匹配合同
    chooseSkuContract() {
      this.$refs.saleContractChooseDialogRef.show(null, items => {
        this.apis.updateSkuContract({
          id: this.formData.skus[0].id,
          salesContractId: items[0].id
        }).then(res => {
          this.$message.success('匹配成功')
          this.pullData()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    // 匹配收款单
    chooseCollection() {
      this.$refs.collectionChooseDialogRef.show(null, items => {
        this.apis.updateSkuCollection({
          id: this.formData.skus[0].id,
          salesReceiptsId: items[0].id
        }).then(res => {
          this.$message.success('匹配成功')
          this.pullData()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate(),
        ...this.$refs.productInfoRef.map(ref => ref.validate())
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
