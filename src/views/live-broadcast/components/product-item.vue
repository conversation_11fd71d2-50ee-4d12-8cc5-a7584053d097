<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">{{ name }}</div>
      <div v-if="formData.productPriority === 2" class="collapse-title-right">
        <el-button type="text" size="mini" @click.stop="remove">删除</el-button>
      </div>
    </template>
    <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" label-width="100px">
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            label="商品编号"
            prop="product.code"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.product.code"
                clearable
                disabled
                placeholder="请选择"
              />
              <el-button type="primary" plain class="ml5" @click="chooseProduct">选择</el-button>
            </div>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="商品名称"
            prop="product.name"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.product.name"
                clearable
                placeholder="请输入"
              />
              <el-button type="primary" plain class="ml5" @click="chooseProduct">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品类"
            prop="product.productCategoryCode"
          >
            <ProductCategorySelect v-model="formData.product.productCategoryCode" :disabled="!canEdit" @change="changeProductCategory" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="抖音商品类目"
            prop="product.dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="formData.product.dyProductCategories" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌名称"
            prop="product.brand"
          >
            <el-input
              v-model="formData.product.brand"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="规格型号"
            prop="product.specifications"
          >
            <el-input
              v-model="formData.product.specifications"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="直播机制"
            prop="product.mechanism"
          >
            <el-input
              v-model="formData.product.mechanism"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入直播机制"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item
            label="数量"
            prop="quantity"
          >
            <el-input
              v-model="formData.quantity"
              clearable
              type="number"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item
            label="商品链接"
            prop="product.productLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.product.productLink"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" plain class="ml5" style="width:55px;" @click="$copy(formData.product.productLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            prop="type"
          >
            <el-checkbox v-for="item in formData.product.condition" :key="item.productCategoryCode" v-model="item.selected" :label="true">{{ item.productCategoryName }}</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.product">
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="product.pictureIds"
          >
            <div v-if="!$isAdmin" slot="label">
              相关附件
              <el-form :model="formData" :disabled="!canEditFiles" label-position="right" label-width="90px">
                <el-button size="small" type="primary" @click="$refs.formAttachmentRef.choosePics()">
                  <div>历史</div>
                  <div class="mt5">附件</div>
                </el-button>
              </el-form>
            </div>
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.product.pictureIds"
                :disabled="!canEditFiles"
                :files="formData.product.files"
                :product-category-code-list="productCategoryCodeList"
                :delete-files="showDeleted ? formData.product.deleteFiles : []"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <CommonChooseDialog ref="goodsChooseDialogRef" title="选择商品" :component="Goods" :multiple="false" :init-search-params="{status: 2}" />
  </el-collapse-item>
</template>

<script>
import FormAttachment from '@/components/FormAttachment'
import ProductCategorySelect from '@/components/ProductCategorySelect'
import productCategoryApi from '@/api/productCategory'
import CommonChooseDialog from '@/components/ChooseDialogs/CommonChooseDialog'
import Goods from '@/views/goods/index.vue'
import { findHistoryFile } from '@/api/file'

export default {
  components: { FormAttachment, CommonChooseDialog, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    canEditFiles: {
      type: Boolean,
      default: true
    },
    // 显示已删除文件
    showDeleted: {
      required: false,
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      Goods,
      rules: {
        'product.name': [
          { required: true, message: '请选择商品', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入商品数量', trigger: 'change' }
        ],
        // 'product.pictureIds': []
      }
    }
  },
  computed: {
    productCategoryCodeList() {
      return [this.formData.product.productCategoryCode, ...(this.formData.product.condition || []).filter(item => item.selected).map(item => item.productCategoryCode)].filter(item => !!item)
    },
  },
  watch: {
    showDeleted(newVal) {
      if (newVal) {
        if (!this.formData.product.deleteFiles) {
          findHistoryFile('product', this.formData.product.id).then(res => {
            this.$set(this.formData.product, 'deleteFiles', res.data.map(item => item.file))
          })
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    async changeProductCategory() {
      if (!this.formData.product.productCategoryCode) {
        this.$set(this.formData.product, 'condition', [])
        return
      }
      const response = await productCategoryApi.getList(this.formData.product.productCategoryCode)
      this.isLoading = false
      if (response.data) {
        this.$set(this.formData.product, 'condition', (response.data || []).map(item => {
          return {
            productCategoryCode: item.code,
            productCategoryName: item.name,
            selected: false
          }
        }))
      }
    },
    remove() {
      this.$emit('remove')
    },
    chooseProduct() {
      this.$refs.goodsChooseDialogRef.show()
      this.$refs.goodsChooseDialogRef.show(null, (items) => {
        // console.log(items)
        if (items.length > 0) {
          const data = items[0]
          this.$set(this.formData, 'product', data)
        } else {
          this.$set(this.formData, 'product', {})
        }
      })
    },
    // 表单校验
    validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
