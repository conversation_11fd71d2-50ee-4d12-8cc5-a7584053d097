<template>
  <el-table
    class="list-table"
    :data="data"
    :stripe="true"
    border
    style="width: 100%;"
  >
    <el-table-column
      fixed
      label="序号"
      type="index"
      min-width="80"
    />
    <el-table-column
      label="直播时间"
      prop="realTime"
      min-width="100"
    >
      <template slot-scope="scope">
        {{ scope.row.realTime | parseDate }}
      </template>
    </el-table-column>
    <el-table-column
      label="sku名称"
      prop="skuName"
      min-width="200"
    />
    <el-table-column
      label="平均销量"
      prop="salesVolume"
      min-width="200"
    />
    <!-- <el-table-column
      fixed="right"
      label="操作"
      min-width="100"
    >
      <template slot-scope="scope">
        <el-button
          type="text"
          size="small"
          @click="$router.push({
            path: `/enterprise/detail`,
            query: {
              id: scope.row.id
            }
          })"
        >查看</el-button>
      </template>
    </el-table-column> -->
  </el-table>
</template>

<script>
// import enterpriseApi from '@/api/enterprise'
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
