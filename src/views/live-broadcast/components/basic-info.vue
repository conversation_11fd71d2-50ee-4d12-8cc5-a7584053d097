<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">基本信息</div>
    </template>
    <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" label-width="90px">
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            label="报名单号"
            prop="id"
          >
            <el-input
              v-model="formData.id"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="公司名称"
            prop="merchantCompanyId"
          >
            <div class="flex-content">
              <el-input
                :value="formData.merchantCompany ? formData.merchantCompany.companyName : ''"
                clearable
                disabled
                placeholder="请选择"
              />
              <CompanyChoose v-if="!$isAdmin" v-model="formData.merchantCompanyId" :merchant-id="formData.merchantId" :editable="true" @change="(val, obj) => formData.merchantCompany = obj || {}">
                <el-button style="width:55px" type="primary" plain class="ml5">选择</el-button>
              </CompanyChoose>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌名称"
            prop="brand"
          >
            <el-input
              v-model="formData.merchant.brand"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="店铺名称"
            prop="merchantStoreId"
          >
            <div class="flex-content">
              <el-input
                :value="formData.merchantStore ? formData.merchantStore.storeName : ''"
                clearable
                disabled
                placeholder="请选择"
              />
              <StoreChoose v-if="!$isAdmin" v-model="formData.merchantStoreId" :merchant-id="formData.merchantId" :editable="true" @change="(val, obj) => formData.merchantStore = obj || {}">
                <el-button style="width:55px" type="primary" plain class="ml5">选择</el-button>
              </StoreChoose>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="店铺ID"
            prop="merchantStore.dyStoreId"
          >
            <el-input
              v-model="formData.merchantStore.dyStoreId"
              disabled
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="店铺UID"
            prop="merchantStore.dyStoreUid"
          >
            <el-input
              v-model="formData.merchantStore.dyStoreUid"
              disabled
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="店铺评分"
            prop="merchantStore.storeRating"
          >
            <el-input
              v-model="formData.merchantStore.storeRating"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="店铺链接"
            prop="merchantStore.storeLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.merchantStore.storeLink"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.merchantStore.storeLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="12">
          <el-form-item
            label="报名链接"
            prop="registrationLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.registrationLink"
                disabled
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.registrationLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="pictureIds"
          >
            <div v-if="!$isAdmin" slot="label">
              相关附件
              <el-form :model="formData" :disabled="!canEditRatingFiles" label-position="right" label-width="90px">
                <el-button size="small" type="primary" @click="$refs.formAttachmentRef.choosePics()">
                  <div>历史</div>
                  <div class="mt5">附件</div>
                </el-button>
              </el-form>
            </div>
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :disabled="!canEditRatingFiles"
                :files="formData.files"
                :product-category-code-list="['TY']"
                :delete-files="showDeleted ? formData.deleteFiles : []"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!isAdd">
        <el-col v-if="type === 'signUp'" :span="6">
          <el-form-item
            label="报名状态"
          >
            {{ formData.status | toEnumName('liveBroadcastSignUpStatus') }}
          </el-form-item>
        </el-col>
        <template v-else>
          <el-col :span="6">
            <el-form-item
              label="完善状态"
            >
              {{ formData.perfectStatus | toEnumName('liveBroadcastPerfectStatus') }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="推送人"
              prop="pushBy"
            >
              <el-input
                v-model="formData.pushBy"
                disabled
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="$isAdmin" :span="6">
            <el-form-item
              label="直播账号"
              prop="liveBroadcastAccount"
            >
              <el-input
                v-model="formData.liveBroadcastAccount"
                disabled
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import CompanyChoose from '@/components/Company/CompanyChoose'
import StoreChoose from '@/components/Store/StoreChoose'
import EnterpriseChoose from '@/components/ChooseDialogs/EnterpriseChoose'
import FormAttachment from '@/components/FormAttachment'
import { findHistoryFile } from '@/api/file'
export default {
  components: { CompanyChoose, StoreChoose, EnterpriseChoose, FormAttachment },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    canEditRatingFiles: {
      type: Boolean,
      default: true
    },
    // 类型，提报还是完善 signUp|perfect
    type: {
      type: String,
      default: 'signUp'
    },
    isAdd: {
      type: Boolean,
      default: true
    },
    // 显示已删除文件
    showDeleted: {
      required: false,
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        'merchantId': [{ required: true, message: '请选择商家', trigger: 'change' }],
        'merchantCompanyId': [{ required: true, message: '请选择公司', trigger: 'change' }],
        'merchantStoreId': [{ required: true, message: '请选择店铺', trigger: 'change' }],
        // brand: [{ required: true, message: '请填写品牌', trigger: 'change' }],
        // pictureIds: [{ required: true, message: '请选择相关附件', typr: 'array', trigger: 'change' }],
      }
    }
  },
  computed: {
  },
  watch: {
    showDeleted(newVal) {
      if (newVal) {
        if (!this.formData.deleteFiles) {
          findHistoryFile('registration', this.formData.id).then(res => {
            this.$set(this.formData, 'deleteFiles', res.data.map(item => item.file))
          })
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    // 表单校验
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
