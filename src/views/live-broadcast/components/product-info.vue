<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">{{ title }}</div>
      <div v-if="!$isAdmin && canEdit" class="collapse-title-right">
        <el-button v-if="canRemove" type="text" size="mini" @click.stop="onRemoveSku">删除sku</el-button>
        <!-- <el-button type="text" size="mini" @click.stop="handleAddMain">新增主品</el-button> -->
        <el-button type="text" size="mini" @click.stop="handleAddGift">新增赠品</el-button>
      </div>
    </template>
    <el-form ref="form" :model="formData" :disabled="!canEdit" :rules="rules" label-position="right" label-width="130px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="抖音商品ID"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="sku名称"
            prop="name"
          >
            <el-input
              v-model="formData.name"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品类"
            prop="productCategoryCode"
          >
            <ProductCategorySelect v-model="formData.productCategoryCode" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="抖音商品类目"
            prop="dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="formData.dyProductCategories" :limit="1" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item
            label="商品标签"
            prop="label"
          >
            <el-select v-model="formData.label" placeholder="请选择" clearable>
              <el-option
                v-for="item in $enums['goodLabels'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="接受补贴"
            prop="acceptSubsidies"
          >
            <el-checkbox v-model="formData.acceptSubsidies" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="sku链接"
            prop="skuLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.skuLink"
                clearable
                placeholder="请输入"
              />
              <el-form><el-button type="primary" plain class="ml5" @click="$copy(formData.skuLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
        <template v-if="$isAdmin">
          <el-col :span="6">
            <el-form-item
              label="合同编号"
            >
              <el-input
                v-model="formData.salesContractCode"
                disabled
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="收款单编号"
            >
              <el-input
                v-model="formData.salesReceiptsCode"
                disabled
                clearable
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="相关附件"
            prop="pictureIds"
          >
            <div v-if="!$isAdmin" slot="label">
              相关附件
              <el-form :model="formData" :disabled="!canEditFiles" label-position="right" label-width="90px">
                <el-button size="small" type="primary" @click="$refs.formAttachmentRef.choosePics()">
                  <div>历史</div>
                  <div class="mt5">附件</div>
                </el-button>
              </el-form>
            </div>
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :disabled="!canEditFiles"
                :files="formData.files"
                :product-category-code-list="formData.productCategoryCode ? ['SKU', formData.productCategoryCode] : ['SKU']"
                :delete-files="showDeleted ? formData.deleteFiles : []"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-collapse v-if="formData.skuProducts && formData.skuProducts.length" v-model="activeNames" class="product-list">
        <ProductItem
          v-for="(item, index) in formData.skuProducts"
          ref="productItemRef"
          :key="index"
          :name="item.productPriority === 1 ? '主品' : `赠品${index}`"
          :form-data="item"
          :can-edit="canEdit"
          :can-edit-files="canEditFiles"
          :show-deleted="showDeleted"
          class="mb15"
          @remove="handleRemove(index)"
        />
      </el-collapse>
      <el-row class="mt20">
        <el-col :span="6">
          <el-form-item
            label="直播价"
            prop="liveBroadcastPrice"
          >
            <el-input-number v-model="formData.liveBroadcastPrice" :min="0" :max="10000000" :precision="2" clearable style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="佣金比例"
            prop="commissionRatio"
          >
            <!-- {{ formData.commissionRatio }} -->
            <PercentInput v-model="formData.commissionRatio" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="佣金"
            prop="commission"
          >
            <el-input v-model="formData.commission" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="现货库存"
            prop="inventory"
          >
            <el-input-number v-model="formData.inventory" :min="0" :max="10000000" :precision="0" clearable style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="现货发货时间"
            prop="waitingTime"
          >
            <el-input
              v-model="formData.waitingTime"
              type="number"
              clearable
              placeholder="请输入"
            >
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="预售库存"
            prop="preSaleInventory"
          >
            <el-input-number v-model="formData.preSaleInventory" :min="0" :max="10000000" :precision="0" clearable style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="预售发货时间"
            prop="preSaleDays"
          >
            <el-input
              v-model="formData.preSaleDays"
              type="number"
              clearable
              placeholder="请输入"
            >
              <template slot="append">天</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            label="库存"
            prop="库存"
          >
            <el-input
              v-model="formData.库存"
              type="number"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="总库存"
            prop="totalInventory"
          >
            <el-input v-model="formData.totalInventory" type="number" disabled clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="货值"
            prop="value"
          >
            <el-input
              v-model="formData.value"
              type="number"
              disabled
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="包邮"
            prop="isPostageFree"
          >
            <el-select v-model="formData.isPostageFree" placeholder="请选择" clearable>
              <el-option
                v-for="item in [{
                  value: true,
                  label: '是'
                }, {
                  value: false,
                  label: '否'
                }]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="发货包裹数"
            prop="packagesDeliveredVolume"
          >
            <!-- <el-input
              v-model="formData.packagesDeliveredVolume"
              type="number"
              clearable
              placeholder="请输入"
            /> -->
            <el-input-number v-model="formData.packagesDeliveredVolume" :min="0" :max="10000000" :precision="0" clearable style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="是否有达人带过"
            prop="hasSold"
          >
            <el-checkbox v-model="formData.hasSold" />
          </el-form-item>
        </el-col>
        <el-col v-if="formData.hasSold" :span="6">
          <el-form-item
            label="达人最好单坑产出"
            prop="maximumSingleSalesVolume"
          >
            <el-input v-model="formData.maximumSingleSalesVolume" type="number" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.hasSold">
        <el-col :span="12">
          <el-form-item
            label="历史销量备注"
            prop="historicalSalesVolume"
          >
            <el-input
              v-model="formData.historicalSalesVolume"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入参加了哪些超头主播，销量情况如何"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="售后条款"
            prop="afterSalesTerms"
          >
            <el-input
              v-model="formData.afterSalesTerms"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import { validURL } from '@/utils/validate'
import ProductItem from './product-item'
import FormAttachment from '@/components/FormAttachment'
import ProductCategorySelect from '@/components/ProductCategorySelect'
import { findHistoryFile } from '@/api/file'
// import enterpriseApi from '@/api/enterprise'
// import { toFixed } from '@/utils/index'
function notZeroValidator(rule, value, callback) {
  if (value <= 0) {
    callback(new Error('不能为0'))
  } else {
    callback()
  }
}
const skuProductDefaultForm = {
  skuId: '',
  productId: '',
  productPriority: '', 	// 商品主次：1主品；2赠品
  quantity: '',
  product: {
    dyProductCategories: [],
  }
}
export default {
  components: { ProductItem, FormAttachment, ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    title: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    canRemove: {
      type: Boolean,
      default: false
    },
    canEditFiles: {
      type: Boolean,
      default: true
    },
    // 显示已删除文件
    showDeleted: {
      required: false,
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeNames: [],
      rules: {
        code: [{ required: true, message: '请输入抖音商品ID', trigger: 'change' }],
        name: [{ required: true, message: '请输入sku名称', trigger: 'change' }],
        productCategoryCode: [{ required: true, message: '请选择品类', trigger: 'change' }],
        label: [{ required: true, message: '请选择商品标签', trigger: 'change' }],
        skuLink: [
          { required: true, message: '请输入sku链接', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value && !validURL(value)) {
              callback(new Error('请输入正确的链接地址（格式：https://www.example.com）'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        afterSalesTerms: [{ required: true, message: '请输入售后条款', trigger: 'change' }],
        commissionRatio: [
          { required: true, message: '请输入合同佣金比例', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value >= 1) {
              callback(new Error('佣金比例不能为100%'))
            } else {
              callback()
            }
          }, trigger: ['blur', 'change'] },
        ],
        packagesDeliveredVolume: [{ required: true, message: '请输入发货包裹数', trigger: 'change' }],
        isPostageFree: [{ required: true, message: '请选择是否包邮', trigger: 'change' }],
        waitingTime: [{ required: true, message: '请输入现货发货小时数', trigger: 'change' }],
        value: [{ required: true, message: '请输入货值', trigger: 'change' }],
        totalInventory: [{ required: true, message: '请输入总库存', trigger: 'change' }],
        preSaleDays: [{ required: true, message: '请输入预售天数', trigger: 'change' }],
        inventory: [{ required: true, message: '请输入现货库存', trigger: 'change' }, { validator: notZeroValidator, trigger: ['blur', 'change'] }],
        liveBroadcastPrice: [{ required: true, message: '请输入直播价', trigger: 'change' }, { validator: notZeroValidator, trigger: ['blur', 'change'] }],
        preSaleInventory: [{ required: true, message: '请输入预售库存', trigger: 'change' }, { validator: notZeroValidator, trigger: ['blur', 'change'] }],
        commission: [{ required: true, message: '请输入佣金', trigger: 'change' }],
        historicalSalesVolume: [{ required: true, message: '请输入历史销量备注', trigger: 'change' }],
        maximumSingleSalesVolume: [{ required: true, message: '请输入达人最好单坑产出', trigger: 'change' }],
        dyProductCategories: [{ required: true, message: '请选择抖音商品类目', type: 'array', trigger: 'change' }],
      },
    }
  },
  computed: {
    valueEffact() {
      return {
        inventory: this.formData.inventory,
        preSaleInventory: this.formData.preSaleInventory,
        liveBroadcastPrice: this.formData.liveBroadcastPrice,
        commissionRatio: this.formData.commissionRatio,
      }
    },
  },
  watch: {
    // 总库存=现货+预售
    // 货值=直播价*总库存
    // 佣金=佣金比例*直播价
    valueEffact({ inventory, preSaleInventory, liveBroadcastPrice, commissionRatio }) {
      this.formData.totalInventory = (parseFloat(inventory) + parseFloat(preSaleInventory)).toFixed(2)
      this.formData.value = (this.formData.totalInventory * liveBroadcastPrice).toFixed(2)
      this.formData.commission = (commissionRatio * liveBroadcastPrice).toFixed(2)
    },
    'formData.skuProducts': {
      immediate: true,
      handler(skuProducts) {
        const diffNum = skuProducts.length - this.activeNames.length
        if (skuProducts.length !== this.activeNames.length) {
          for (let i = 0; i < skuProducts.length; i++) {
            if (this.activeNames.length > i) {
              continue
            }
            const name = this.activeNames.length === 0 ? '主品' : `赠品${i}`
            this.activeNames.push(name)
          }
        }
      }
    },
    showDeleted(newVal) {
      if (newVal) {
        if (!this.formData.deleteFiles) {
          findHistoryFile('sku', this.formData.id).then(res => {
            this.$set(this.formData, 'deleteFiles', res.data.map(item => item.file))
          })
        }
      }
    }
  },
  mounted() {
    // this.activeNames = []
    if (this.formData.skuProducts.length === 0) {
      this.handleAddMain()
    }
  },
  methods: {
    onRemoveSku() {
      this.$emit('removeSku', this.formData)
    },
    handleAddMain() {
      this.formData.skuProducts.push({
        ...JSON.parse(JSON.stringify(skuProductDefaultForm)),
        productPriority: 1
      })
    },
    handleAddGift() {
      this.formData.skuProducts.push({
        ...JSON.parse(JSON.stringify(skuProductDefaultForm)),
        productPriority: 2
      })
    },
    handleRemove(index) {
      this.formData.skuProducts.splice(index, 1)
    },
    // 表单校验
    async validate() {
      const validList = await Promise.all([new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      }), ...(this.$refs.productItemRef || []).map(productItemRef => productItemRef.validate())])

      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
.product-list {
  border-left-width: 1px;
  margin: 0 15px;
}
</style>
