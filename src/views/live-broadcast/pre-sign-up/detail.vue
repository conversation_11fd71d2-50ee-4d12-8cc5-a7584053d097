<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="报名信息">
        <el-collapse v-model="activeNames">
          <BasicInfo ref="basicInfoRef" :form-data="formData" :can-edit="canEdit" name="1" :is-add="isAdd" type="signUp" />
          <ProductInfo
            v-for="(item, index) in formData.skus || []"
            :key="index"
            ref="productInfoRef"
            :can-edit="canEdit"
            :form-data="item"
            :name="`sku信息${index + 1}`"
            :title="`sku信息`"
            class="mt15"
          />
        </el-collapse>
        <div class="dialog-footer mt10">
          <el-button v-if="!$isAdmin && canEdit" :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
          <el-button v-if="!$isAdmin && canEdit" :loading="isSubmiting" type="primary" @click="submitHandle">提 交</el-button>
          <template v-if="formData.status === 2">
            <AuditBtn v-permission="'button:selfRegistration:businessAudit'" level="businessAudit" :audit-api="apis.businessAudit" :is-enterprise-data="true" :audit-records="formData.preAuditRecords" :order-id="formData.id" :on-success="dataUpdateCallback" />
          </template>
          <el-button :loading="isSubmiting" plain type="primary" @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="formData.preAuditRecords && formData.preAuditRecords.length" label="审核记录">
        <AuditRecord :data="formData.preAuditRecords" />
      </el-tab-pane>
      <!-- <el-tab-pane v-if="formData.history && formData.history.length" label="历史直播记录">
        <BroadcastHistory :data="formData.history" />
      </el-tab-pane> -->
    </el-tabs>
    <EnterpriseInfoPerfectDialog ref="enterpriseInfoPerfectDialogRef" />
  </div>
</template>

<script>
import liveBroadcastSignUpApi from '@/api/liveBroadcastSignUp'
import BasicInfo from '../components/basic-info'
import ProductInfo from '../components/product-info'
import AuditRecord from '@/components/Audit/AuditRecord'
import BroadcastHistory from '../components/broadcast-history.vue'
import EnterpriseInfoPerfectDialog from '@/components/EnterpriseInfoPerfectDialog'
import detailMixin from '@/mixins/detailMixin'
import { mapGetters } from 'vuex'
const skuDefault = {
  registrationId: '',
  registrationType: 2, // 报名类型：1邀请报名，2商品提报
  merchantId: '',
  merchantStoreId: '',
  code: '',
  name: '',
  productCategoryCode: '',
  productCategoryName: '',
  label: '',
  acceptSubsidies: false,
  skuLink: '',
  liveBroadcastPrice: '',
  commission: '',
  commissionRatio: '',
  inventory: undefined,
  historicalSalesVolume: '',

  hasSold: false,
  maximumSingleSalesVolume: '',

  preSaleDays: '',
  preSaleInventory: '',
  totalInventory: '',
  value: '',
  waitingTime: '',
  isPostageFree: false,
  packagesDeliveredVolume: '',
  afterSalesTerms: '',
  pictureIds: '',
  skuProducts: [
  ],
  merchant: {
    brand: '',
  },
  dyProductCategories: []
}
const defaultFormData = {
  id: '',
  merchantId: '',
  merchantStoreId: '',
  code: '',
  registrationTime: '',
  status: '',
  pictureIds: '',
  merchant: {},
  merchantCompany: {},
  merchantStore: {},
  skus: [
    { ...skuDefault }
  ]
}

export default {
  name: 'PreSignUpDetail',
  components: { BasicInfo, ProductInfo, AuditRecord, BroadcastHistory, EnterpriseInfoPerfectDialog },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', 'sku信息'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      auditApi: liveBroadcastSignUpApi.businessAudit,
      auditTitle: '商务审核',
      // 增删改查集合
      apis: {
        ...liveBroadcastSignUpApi
      },
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
    // 状态：1待提交；2待审核；3审核通过；4审核不通过
    canEdit() {
      return this.isAdd || [1, 4].indexOf(this.formData.status) > -1
    },
  },
  watch: {
    'formData.skus': {
      immediate: true,
      handler(skus) {
        if (skus.length) {
          this.activeNames = ['1'].concat(skus.map((sku, index) => `sku信息${index + 1}`))
        }
      }
    }
  },
  mounted() {
    if (!this.$isAdmin && this.isAdd) {
      // 商品信息未完善，要先完善商品信息
      if (!this.userInfo.hasBeenImproved) {
        this.$refs.enterpriseInfoPerfectDialogRef.show(() => {
          this.formData.merchantId = this.userInfo.id
          this.formData.merchant.brand = this.userInfo.brand
        })
      } else {
        this.formData.merchantId = this.userInfo.id
        this.formData.merchant.brand = this.userInfo.brand
      }
    }
    this.pullData()
  },
  methods: {
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.basicInfoRef.validate(),
        ...this.$refs.productInfoRef.map(ref => ref.validate())
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
