<template>
  <el-container class="container">
    <!-- <el-main v-permission="'menu:liveBroadcast:plan'" style="padding: 0 15px 0 0;">
      <el-card shadow="hover" class="card">
        <Calendar class="calendar module" />
      </el-card>
    </el-main> -->
    <el-card shadow="hover" class="card mr10" style="flex: 1.5" header="待办消息">
      <Notice />
    </el-card>
    <!-- <el-aside style="padding: 0;background-color: transparent;" width="auto"> -->
    <el-card shadow="hover" class="card content">
      <p><svg-icon class-name="slogan" icon-class="slogan" />我们的使命：让每一次消费放心，安心，舒心。</p>
      <p><svg-icon class-name="slogan" icon-class="slogan" />我们的愿景：成为消费者长久信任的朋友。</p>
      <p><svg-icon class-name="slogan" icon-class="slogan" />核心价值观：真实数据，诚信担当，追求极致。</p>
    </el-card>
  </el-container>
</template>

<script>
import Notice from './components/notice'
export default {
  name: 'Dashboard',
  components: {
    Notice
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  }
}
</script>

<style scoped lang="scss">
.container {
  background-color: #fafbfe;
  height: calc(100vh - 142px);
  padding: 0;
  padding: 15px 15px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  .card {
    flex: 1;
    overflow-y: auto;
  }
  .content {
    p {
      font-size: 15px;
      .slogan {
        font-size:20px;
        margin-right: 10px;
        color: #FA8B15;
      }
    }
  }
  ::v-deep .el-card__header {
    font-weight: bold;
    font-size: 14px;
  }
  .module {
    // background-color: #fff;
    // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    // box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    // padding: 20px 30px;
  }
}
</style>
