<template>
  <div class="notice-list">
    <div v-for="(item, index) in tableData" :key="index" class="notice-item b-border" @click="rowClick(item)">
      <div class="email-icon">
        <svg-icon v-if="item.isRead" class-name="email-icon c-gray" icon-class="email_open" title="已读" />
        <svg-icon v-else class-name="email-icon c-warning not-read" icon-class="email-fill" title="点击设置已读" @click="batchRead(scope.row)" />
      </div>
      <div class="time">{{ formatTime(item.sendTime) }}</div>
      <div class="content">{{ item.content }}</div>
    </div>
    <div v-if="!tableData || !tableData.length" class="no-data">
      暂无代办内容
    </div>
  </div>
</template>

<script>
import noticeLogApi from '@/api/notice/log'
import { formatTime } from '@/utils/index'
export default {
  components: {
  },
  data() {
    return {
      isLoading: false,
      searchForm: {
        isRead: false,
      },
      tableData: [],
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
      },
      // 增删改查集合
      apis: {
        ...noticeLogApi,
        getPager: noticeLogApi.getMyPager
      },
    }
  },
  mounted() {
    this.pullData()
  },
  methods: {
    formatTime,
    async pullData() {
      this.isLoading = true
      const response = await this.apis.getPager({
        ...this.searchForm,
        ...this.pageSet
      })
      this.isLoading = false
      if (response.data) {
        this.tableData = response.data.list
      }
      return response
    },
    // 批量设置已读
    batchRead(row) {
      if (row && row.isRead) {
        return
      }
      const params = [row.id]
      noticeLogApi.batchRead(params).then(res => {
        if (res.code === 200) {
          this.$store.dispatch('user/getUnReadNoticeCount')
          this.pullData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    rowClick(row) {
      this.batchRead(row)
      row.jumpUrl = row.jumpUrl || ''
      // row.jumpUrl = 'http://www.baidu.com'
      // row.jumpUrl = '/enterprise/detail?id=37'
      if (row.jumpUrl.startsWith('http')) {
        window.open(row.jumpUrl, '_blank')
      } else {
        this.$router.push(row.jumpUrl)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-list {
  .notice-item {
    position: relative;
    padding: 10px 0 10px 35px;
    // border: 1px solid red;
    .email-icon {
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -5px;
      // translate: translateY(-50%);
      font-size: 19px;
      &.not-read {
        cursor: pointer;
      }
    }
    .time {
      color: #666;
      font-size: 12px;
      margin-bottom: 5px;
    }
    .content {
      font-size: 14px;
      // line-height: 30px;
    }
  }
  .no-data {
    color: #666;
    text-align: center;
    margin-top: 15px;
    font-size: 13px;
  }
}
</style>
