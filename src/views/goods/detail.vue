<template>
  <div ref="form" class="app-container oprate-form">
    <el-collapse v-model="activeNames">
      <GoodsInfo ref="goodsInfoRef" :form-data="formData" name="1" />
      <el-collapse-item name="2" class="collapse-module mt15">
        <template slot="title">
          <div class="collapse-title-pre">商品附件</div>
        </template>
        <el-row>
          <el-col :span="24">
            <div class="flex-content">
              <FormAttachment
                ref="formAttachmentRef"
                v-model="formData.pictureIds"
                :files="formData.files"
                :product-category-code-list="productCategoryCodeList"
              />
            </div>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
    <div class="dialog-footer mt10">
      <el-button :loading="isSubmiting" plain type="primary" @click="close">取 消</el-button>
      <el-button :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
    </div>
  </div>
</template>

<script>
import goodsApi from '@/api/goods'
import GoodsInfo from './components/goods-info'
import FormAttachment from '@/components/FormAttachment'
import detailMixin from '@/mixins/detailMixin'

const defaultFormData = {
  id: 0,
  merchantId: 0,
  code: '',
  name: '',
  productCategoryCode: '',
  productCategoryName: '',
  condition: [ //
    // {
    //   productCategoryCode: '',
    //   productCategoryName: '',
    //   selected: true
    // }
  ],
  brand: '',
  specifications: '',
  productLink: '',
  pictureIds: '',
}
export default {
  components: { GoodsInfo, FormAttachment },
  mixins: [detailMixin],
  data() {
    return {
      activeNames: ['1', '2'],
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      // 增删改查集合
      apis: {
        ...goodsApi
      },
    }
  },
  computed: {
    productCategoryCodeList() {
      return [this.formData.productCategoryCode, ...(this.formData.condition || []).filter(item => item.selected).map(item => item.productCategoryCode)].filter(item => !!item)
    },
  },
  mounted() {
    this.pullData()
  },
  methods: {
    // 提交前校验
    async validate() {
      const validList = await Promise.all([
        this.$refs.goodsInfoRef.validate()
      ])
      return !validList.includes(false)
    },
  }
}
</script>

<style lang="scss" scoped>
  .flex-content {
    display: flex;
    flex-direction: row;
    overflow: hidden;
  }
</style>
