<template>
  <div class="flex-content">
    <el-upload
      v-for="(item, index) in fixedFileList"
      :key="index"
      :file-list="item.url?[item]: []"
      :multiple="false"
      :limit="1"
      action="#"
      class="picture-component mr15"
      :show-file-list="true"
      :http-request="getOnHttpRequestFn(item)"
      :on-preview="handlePictureCardPreview"
      :on-remove="onRemove"
      :on-success="onSuccess"
      :before-upload="onBeforeUpload"
      accept="image/png, image/jpeg"
      list-type="picture-card"
      :auto-upload="true"
    >
      <i slot="default" class="el-icon-plus" />
      <div v-if="index <= fixedList.length - 1" slot="tip" class="el-upload__tip" style="text-align: center;">{{ fixedList[index] }}</div>
    </el-upload>
    <el-upload
      action="#"
      multiple
      show-file-list
      :file-list="notFixedFileList"
      class="picture-component mr15"
      :http-request="onNotFixedHttpRequest"
      :on-preview="handlePictureCardPreview"
      :on-remove="onRemove"
      :on-success="onSuccess"
      :before-upload="onBeforeUpload"
      accept="image/png, image/jpeg"
      list-type="picture-card"
      :auto-upload="true"
    >
      <i slot="default" class="el-icon-plus" />
      <!-- <div slot="file" slot-scope="{file}">
        <img
          class="el-upload-list__item-thumbnail"
          :src="file.url"
          alt=""
        >
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file.url)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handleDownload(file)"
          >
            <i class="el-icon-download" />
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="onRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div> -->
    </el-upload>
  </div>
</template>

<script>
import { uploadSingleFileSub } from '@/api/file'
import { previewMethod } from '@/components/ImagePreview/index'
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    /**
     * 需要固定的上传的数组说明
    */
    fixedList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
  computed: {
    // 固定的
    fixedFileList() {
      return this.value.filter((item, index) => {
        return index <= this.fixedList.length - 1
      })
    },
    // 非固定的
    notFixedFileList() {
      return this.value.filter((item, index) => {
        return index > this.fixedList.length - 1
      })
    }
  },
  watch: {
    value() {
      this.getInit()
    },
    fixedList() {
      this.getInit()
    }
  },
  mounted() {
  },
  methods: {
    getInit() {
      while (this.value.length < this.fixedList.length) {
        this.value.push({
          url: ''
        })
        console.log('长度不够我来凑')
      }
    },
    async onNotFixedHttpRequest({ file }) {
      console.log('onNotFixedHttpRequest', file)
      const fileObj = await this.uploadSingleFile(file)
      if (fileObj) {
        this.notFixedFileList.push(fileObj)
      }
    },
    async uploadSingleFile(file) {
      const formData = new FormData()
      formData.append('file', file)
      const resData = await uploadSingleFileSub(formData)
      if (resData.code !== 200 || !resData.data.visitUrl) {
        this.$message({
          message: resData.msg,
          type: 'error'
        })
        return
      }
      return { id: resData.data.id, url: resData.data.visitUrl }
    },
    getOnHttpRequestFn(item) {
      return async({ file }) => {
        const fileObj = await this.uploadSingleFile(file)
        if (fileObj) {
          Object.assign(item, fileObj)
        }
      }
    },
    handlePictureCardPreview({ url }) {
      console.log('handlePictureCardPreview', url)
      previewMethod([url], 0)
    },
    // handleDownload(file)
    // onRemove(file)
    onRemove(item) {
      item.url = ''
    },
    onSuccess(data) {
      console.log('onSuccess', data)
    },
    onBeforeUpload(file) {
      // console.log('onBeforeUpload', file)
    },
    // updateVal() {
    //   this.$emit('input', [])
    // },
  }
}
</script>

<style lang="scss" scoped>
</style>
