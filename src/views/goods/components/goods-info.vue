<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">商品信息</div>
    </template>
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="商品编号"
            prop="code"
          >
            <el-input
              v-model="formData.code"
              clearable
              disabled
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="商品名称"
            prop="name"
          >
            <el-input
              v-model="formData.name"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品类"
            prop="productCategoryCode"
          >
            <ProductCategorySelect v-model="formData.productCategoryCode" @change="changeProductCategory" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="抖音商品类目"
            prop="dyProductCategories"
          >
            <DyProductCategoriesSelect v-model="formData.dyProductCategories" :limit="1" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="品牌名称"
            prop="brand"
          >
            <el-input
              v-model="formData.brand"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item
            label="商品链接"
            prop="productLink"
          >
            <div class="flex-content">
              <el-input
                v-model="formData.productLink"
                clearable
                placeholder="请输入"
              />
              <el-form><el-button style="width:55px" type="primary" plain class="ml5" @click="$copy(formData.productLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item
            label="规格型号"
            prop="specifications"
          >
            <el-input
              v-model="formData.specifications"
              clearable
              placeholder="请输入"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            prop="type"
          >
            <el-checkbox v-for="item in formData.condition" :key="item.productCategoryCode" v-model="item.selected" :label="true">{{ item.productCategoryName }}</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
import productCategoryApi from '@/api/productCategory'
import ProductCategorySelect from '@/components/ProductCategorySelect'
export default {
  components: { ProductCategorySelect },
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'change' }
        ],
        productCategoryCode: [
          { required: true, message: '请选择品类', trigger: 'change' }
        ],
        brand: [
          { required: true, message: '请输入商品品牌', trigger: 'change' }
        ],
        // productLink: [
        //   { required: true, message: '请输入商品链接', trigger: 'change' }
        // ],
        specifications: [
          { required: true, message: '请输入规格型号', trigger: 'change' }
        ],
      }
    }
  },
  watch: {
    // // 修改了品类，清空选择项
    // 'formData.productCategoryCode'(newVal, oldVal) {
    //   if (newVal && oldVal) {
    //     this.formData.condition = []
    //   }
    // }
  },
  mounted() {
  },
  methods: {
    async changeProductCategory() {
      if (!this.formData.productCategoryCode) {
        this.$set(this.formData, 'condition', [])
        return
      }
      const response = await productCategoryApi.getList(this.formData.productCategoryCode)
      this.isLoading = false
      if (response.data) {
        this.$set(this.formData, 'condition', (response.data || []).map(item => {
          return {
            productCategoryCode: item.code,
            productCategoryName: item.name,
            selected: false
          }
        }))
      }
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
