<template>
  <div class="app-container oprate-form mr15">
    <div class="container">
      <div class="module mr15">
        <!-- <div>pictureIds:{{ formData.pictureIds }}</div> -->
        <FormAttachment
          ref="formAttachmentRef"
          v-model="formData.pictureIds"
          :files="formData.files"
          :attachment-type="attachmentType"
        />
      </div>
    </div>
    <div class="dialog-footer mt10">
      <el-button :loading="isSubmiting" type="primary" @click="saveHandle">保 存</el-button>
      <el-button :loading="isSubmiting" plain type="primary" @click="close">取 消</el-button>
    </div>
  </div>
</template>

<script>
import enterpriseApi from '@/api/enterprise'
import attachmentApi from '@/api/attachment'
// import productCategoryApi from '@/api/productCategory'
import detailMixin from '@/mixins/detailMixin'
import FormAttachment from '@/components/FormAttachment'
export default {
  components: { FormAttachment },
  mixins: [detailMixin],
  data() {
    return {
      // 增删改查集合
      apis: {
        ...enterpriseApi
      },
      rules: {
      },
      formData: {},
      attachmentType: {}
    }
  },
  computed: {
  },
  async mounted() {
    this.attachmentType = { ...this.dialogData }
    // this.$nextTick(() => this.$refs.form.clearValidate())
    this.pullData(this.dialogData.code)
  },
  methods: {
    async pullData(code) {
      this.isLoading = true
      enterpriseApi.getDetail().then(response => {
        this.formData = response.data
        // console.log(this.formData)
        // Object.assign(this.formData, response.data)
      }).finally(() => {
        this.isLoading = false
      })
    },
    async validate() {
      return new Promise(resolve => {
        resolve(true)
      })
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.isSubmiting = true
      enterpriseApi.update({
        ...this.formData
      }).then(res => {
        this.$message.success('保存成功')
        this.$eventBus.$emit('submit-attachment')
        this.$emit('success', res.data)
        // this.close()
      }).finally(() => {
        this.isSubmiting = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.oprate-form {
  background-color: #fff;
  .container {
    display: flex;
    .module {
      flex: 1;
    }
  }
}
</style>
