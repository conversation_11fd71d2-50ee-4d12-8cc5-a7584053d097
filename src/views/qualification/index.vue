<template>
  <el-container type="flex" class="app-container">
    <el-aside class="aside" width="350px">
      <el-tree
        ref="treeData"
        :data="treeData"
        :props="defaultProps"
        highlight-current
        :expand-on-click-node="false"
        default-expand-all
        node-key="$treeNodeId"
        @node-click="clickNode"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node" :class="{true: 'chosed-node'}[currData && data.code === currData.code]">
          <span class="node-text" :title="node.label">{{ node.label }}</span>
        </span>
      </el-tree>
    </el-aside>
    <el-main v-if="showDetail">
      <header class="left-line">
        配置附件【{{ currData.name }}】的资质文件
      </header>
      <Detail v-model="showDetail" :dialog-type="currData ? 'edit' : 'new'" :dialog-data="currData" @success="saveCallback" />
    </el-main>
  </el-container>
</template>

<script>
import attachmentApi from '@/api/attachment'
import Detail from './detail'

export default {
  components: {
    Detail,
  },
  props: {
  },
  data() {
    return {
      treeData: [],
      showDetail: false,
      currData: null,
      defaultProps: {
        children: 'children',
        label: 'name',
        // disabled(data, node) {
        //   return node.level !== 2
        // }
      },
      apis: {
        ...attachmentApi
      }
    }
  },
  watch: {
    showDetail(val) {
      if (!val) {
        this.$refs.treeData.setCurrentKey(null)
      }
    }
  },
  mounted() {
    this.pullData()
  },
  methods: {
    async pullData() {
      this.isLoading = true
      const response = await this.apis.getTrees()
      // const response = {
      //   'data': {
      //     '资质文件': [
      //       {
      //         'code': 'SBZCZ',
      //         'name': '商标注册证',
      //         'group': '资质文件',
      //         'type': '.pdf',
      //         'size': 2,
      //         'maximumQuantity': 1,
      //         'displayCompanyName': true,
      //         'displayValidityPeriod': true,
      //         'sort': 4
      //       },
      //       {
      //         'code': 'SBZCSQSLTZS',
      //         'name': '商标注册申请受理通知书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 5
      //       },
      //       {
      //         'code': 'JYGSYYZZ',
      //         'name': '经营公司营业执照',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 6
      //       },
      //       {
      //         'code': 'SCGSYYZZ',
      //         'name': '生产公司营业执照',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 7
      //       },
      //       {
      //         'code': 'SBSQLL',
      //         'name': '商标授权链路/采购链路',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 8
      //       },
      //       {
      //         'code': 'WTJGXY',
      //         'name': '委托加工协议',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 9
      //       },
      //       {
      //         'code': 'CPZJBG',
      //         'name': '产品质检报告',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 10
      //       },
      //       {
      //         'code': 'SPSCXKZ',
      //         'name': '食品生产许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 11
      //       },
      //       {
      //         'code': 'SPJYXKZ',
      //         'name': '食品经营许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 12
      //       },
      //       {
      //         'code': 'HZPSCXKZ',
      //         'name': '化妆品生产许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 13
      //       },
      //       {
      //         'code': 'SYDDSCQYZS',
      //         'name': '食盐定点生产企业证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 14
      //       },
      //       {
      //         'code': 'SYPFXKZ',
      //         'name': '食盐批发许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 15
      //       },
      //       {
      //         'code': 'JLSPLSXKZ',
      //         'name': '酒类商品零售许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 16
      //       },
      //       {
      //         'code': 'JLSPPFXKZ',
      //         'name': '酒类商品批发许可证（上海）',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 17
      //       },
      //       {
      //         'code': 'YYEPFRFCPPFZCZS',
      //         'name': '婴幼儿配方乳粉产品配方注册证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 18
      //       },
      //       {
      //         'code': 'GCBJSPZCZS',
      //         'name': '国产保健食品注册证书（批准文号）',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 19
      //       },
      //       {
      //         'code': 'BAPZ',
      //         'name': '备案凭证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 20
      //       },
      //       {
      //         'code': 'JSZRHT',
      //         'name': '技术转让合同',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 21
      //       },
      //       {
      //         'code': 'JKBJSPZCZS',
      //         'name': '进口保健食品注册证书（批准文号）',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 22
      //       },
      //       {
      //         'code': 'CRJJYJYZM',
      //         'name': '出入境检验检疫证明',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 23
      //       },
      //       {
      //         'code': 'WSZS',
      //         'name': '卫生证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 24
      //       },
      //       {
      //         'code': 'BGD',
      //         'name': '报关单',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 25
      //       },
      //       {
      //         'code': 'WGHNCPRZZS',
      //         'name': '无公害农产品认证证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 26
      //       },
      //       {
      //         'code': 'NCPDLBZDJZS',
      //         'name': '农产品地理标志登记证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 27
      //       },
      //       {
      //         'code': 'LSSPRZZS',
      //         'name': '绿色食品认证证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 28
      //       },
      //       {
      //         'code': 'YJSPRZZS',
      //         'name': '有机食品认证证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 29
      //       },
      //       {
      //         'code': 'BJSPPZZS',
      //         'name': '保健食品批准证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 30
      //       },
      //       {
      //         'code': 'XDCPSCQYWSXKZ',
      //         'name': '消毒产品生产企业卫生许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 31
      //       },
      //       {
      //         'code': 'GCTSYTHZPWSXKPJ',
      //         'name': '国产特殊用途化妆品卫生许可批件',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 32
      //       },
      //       {
      //         'code': 'GCFTSYTHZPBADZPZ',
      //         'name': '国产非特殊用途化妆品备案电子凭证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 33
      //       },
      //       {
      //         'code': 'JKTSYTHZPWSXKPJ',
      //         'name': '进口特殊用途化妆品卫生许可批件',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 34
      //       },
      //       {
      //         'code': 'JKFTSYTHZPBADZPZ',
      //         'name': '进口非特殊用途化妆品备案电子凭证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 35
      //       },
      //       {
      //         'code': '3CZS',
      //         'name': '3C证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 36
      //       },
      //       {
      //         'code': 'JDZS',
      //         'name': '鉴定证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 37
      //       },
      //       {
      //         'code': 'DYSPQ3CRZ',
      //         'name': '电源适配器的3C认证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 38
      //       },
      //       {
      //         'code': 'WXDFSSBXHHZZ',
      //         'name': '无线电发射设备型号核准证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 39
      //       },
      //       {
      //         'code': 'QGGYCPSCXKZ',
      //         'name': '全国工业产品生产许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 40
      //       },
      //       {
      //         'code': '3CAQRZZS',
      //         'name': '3C安全认证证书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 41
      //       },
      //       {
      //         'code': 'XZXKZ',
      //         'name': '行政许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 42
      //       },
      //       {
      //         'code': 'WLWHJYXKZ',
      //         'name': '网络文化经营许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 43
      //       },
      //       {
      //         'code': 'YYSDLHTHHZXY',
      //         'name': '运营商代理合同或合作协议',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 44
      //       },
      //       {
      //         'code': 'YYSHZXY',
      //         'name': '运营商合作协议',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 45
      //       },
      //       {
      //         'code': 'CBSSQWTS',
      //         'name': '出版社授权委托书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 46
      //       },
      //       {
      //         'code': 'CBSJYXKZ',
      //         'name': '出版物经营许可证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 47
      //       },
      //       {
      //         'code': 'SYTTYZZ',
      //         'name': '水域滩涂养殖证',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 48
      //       },
      //       {
      //         'code': 'DLBZSYXYWJ',
      //         'name': '地理标志使用协议文件',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 49
      //       },
      //       {
      //         'code': 'SQS',
      //         'name': '授权书',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 50
      //       },
      //       {
      //         'code': 'GXZM',
      //         'name': '关系证明（养殖证所有人→商家）',
      //         'group': '资质文件',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 51
      //       }
      //     ],
      //     '商品图片': [
      //       {
      //         'code': 'SPZT',
      //         'name': '商品主图',
      //         'group': '商品图片',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 1
      //       },
      //       {
      //         'code': 'WBZT',
      //         'name': '外包装图',
      //         'group': '商品图片',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 2
      //       },
      //       {
      //         'code': 'SPBS',
      //         'name': '商品标识',
      //         'group': '商品图片',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 3
      //       }
      //     ],
      //     '其他': [
      //       {
      //         'code': 'DPPFJT',
      //         'name': '店铺评分截图',
      //         'group': '其他',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 52
      //       },
      //       {
      //         'code': 'HT',
      //         'name': '合同',
      //         'group': '其他',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 53
      //       },
      //       {
      //         'code': 'FKPZ',
      //         'name': '付款凭证',
      //         'group': '其他',
      //         'type': null,
      //         'size': null,
      //         'maximumQuantity': null,
      //         'displayCompanyName': false,
      //         'displayValidityPeriod': false,
      //         'sort': 54
      //       }
      //     ]
      //   }
      // }
      this.isLoading = false
      if (response.data) {
        if (response.data) {
          const treeData = []
          const data = response.data
          for (const key in data) {
            treeData.push({
              name: key,
              children: data[key]
            })
          }
          this.treeData = treeData
        }
      }
      return response
    },
    // // 遍历树形，生成nodeKey
    // cyclicGetNodeKey(list, deep) {
    //   list.forEach((item, index) => {
    //     item.nodeKey = `${deep}_${index}`
    //     if (item.children) {
    //       this.cyclicGetNodeKey(item.children, deep + 1)
    //     }
    //   })
    // },
    clickNode(row, node) {
      if (node.level === 1) {
        return
      }
      if (this.currData !== row) {
        this.showDetail = false
        this.currData = row
        this.$nextTick(() => {
          this.showDetail = true
        })
      }
    },
    addHandle() {
      this.showDetail = false
      this.currData = null
      this.$refs.treeData.setCurrentKey(null)
      this.$nextTick(() => {
        this.showDetail = true
      })
    },
    saveCallback() {
      this.$message.success('保存成功')
      this.pullData()
      if (this.currData === null) {
        this.showDetail = false
        this.currData = null
      }
    },
    deleteHandle(item) {
      this.$confirm('你确定删除该条数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.apis.delete(item.code).then(reponse => {
          if (reponse.code === 200) {
            if (this.currData === item) {
              this.showDetail = false
              this.currData = null
            }
            this.$message({
              showClose: true,
              type: 'success',
              message: '删除成功!'
            })
            this.pullData()
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
        })
      }).catch(() => {
        // this.$message({
        //   type: 'info',
        //   message: '已取消删除'
        // })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/element-variables.scss";
.app-container {
  // border: 5px solid red;
  height: 100%;
  padding: 0;
  .aside {
    padding: 15px;
    margin-bottom: 0;
    border-right: 1px solid #DCDFE6;
    background-color: #fff;
    .el-tree {
      margin-top: 10px;
      ::v-deep .is-current .el-tree-node__content{
        &::first-child {
          background: #ebf2fe !important;
        }
      }
      .chosed-node {
        // flex: 1;
      }
      .custom-tree-node {
        flex: 1;
        display: flex;
        // border: 1px solid green;
        overflow: hidden;
        .node-text {
          flex: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          // border: 1px solid red;
        }
      }
    }
  }
  .el-main {
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 0px;
      padding-left: 10px;
      margin: 15px 10px 0 15px;
    }
    .el-divider {
      margin-top: 12px;
      margin-bottom: 0px;
    }
    .oprate-form {
      flex: 1;
      overflow-y: auto;
      padding: 15px 0;
      background-color: transparent;
    }
  }
  .footer {
    padding-left: 80px;
    // text-align: right;
  }
  .left-line {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: $--color-primary;
      border-radius: 2px;
    }
  }
}
</style>
