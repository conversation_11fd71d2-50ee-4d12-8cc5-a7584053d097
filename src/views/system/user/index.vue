<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form :inline="true" :model="queryParams" label-suffix="：">
          <el-form-item label="登录名">
            <el-input v-model="queryParams.loginName" placeholder="登录名" />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="queryParams.name" placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitClick">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-button type="primary" @click="newUserClick">新增</el-button>
    <el-table
      border
      :data="tableData"
      :stripe="true"
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column
        prop="id"
        label="序号"
        min-width="50"
      />
      <el-table-column
        prop="loginName"
        label="登录名"
      />
      <el-table-column
        prop="no"
        label="工号"
      />
      <el-table-column
        prop="name"
        label="姓名"
      />
      <el-table-column
        prop="isEnable"
        label="是否有效"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isEnable" type="success">启用</el-tag>
          <el-tag v-else type="info">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="sex"
        label="性别"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.sex === 1">男</span>
          <span v-if="scope.row.sex === 2">女</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="nation"
        label="民族"
      />
      <el-table-column
        prop="email"
        label="电子邮件"
      />
      <el-table-column
        prop="phone"
        label="手机号码"
        min-width="150"
      />
      <el-table-column
        prop="birthday"
        label="生日"
      >
        <template slot-scope="scope">
          {{ scope.row.birthday | parseDate('{y}-{m}-{d}') }}
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        min-width="150px"
      >
        <template slot-scope="scope">
          <!-- <el-button size="small" type="text" @click="showEmploymentHistorys(scope.row.id)">任职履历</el-button> -->
          <el-button size="small" type="text" @click="editUserDetailClick(scope.row.id)">编辑</el-button>
          <el-button v-if="!scope.row.isEnable" size="small" type="text" @click="enableUser(scope.row.id)">启用</el-button>
          <el-button v-if="scope.row.isEnable" size="small" type="text" @click="disableUser(scope.row.id)">禁用</el-button>
          <el-button size="small" type="text" @click="deleteUserClick(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pageSet.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSet.pageSize"
      :total="pageSet.total"
      layout="total, sizes, prev, pager, next, jumper"
      background
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <el-dialog :title="userDetailDialog.type === 'new' ? '新增用户' : '编辑用户'" :visible.sync="userDetailDialog.isShow" :close-on-click-modal="false" width="1100px" @closed="closeDialog('currentUserDataForm')">
      <el-form ref="currentUserDataForm" :model="userInfo" label-width="140px" :rules="formRules">
        <el-row>
          <el-col :span="11">
            <el-form-item label="编号">
              <el-input v-model="userInfo.code" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="登录名" prop="loginName">
              <el-input v-model="userInfo.loginName" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="userInfo.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="英文名" prop="nameEn">
              <el-input v-model="userInfo.nameEn" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="身份证号码" prop="idCard">
              <el-input v-model="userInfo.idCard" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="userInfo.sex" prop="sex">
                <el-radio-button label="1">男</el-radio-button>
                <el-radio-button label="2">女</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="员工号" prop="no">
              <el-input v-model="userInfo.no" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="民族" prop="nation">
              <el-input v-model="userInfo.nation" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="政治面貌" prop="politicalStatus">
              <el-input v-model="userInfo.politicalStatus" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="婚姻状态" prop="maritalStatus">
              <el-select v-model="userInfo.maritalStatus" placeholder="请选择" style="width: 100%" clearable>
                <el-option
                  v-for="(item) in maritalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="户籍所在地" prop="selectedKeys">
              <el-cascader
                :key="modelKey"
                v-model="userInfo.selectedKeys"
                :props="areaProps"
                style="width: 100%"
                clearable
                placeholder="请选择所属地区"
                @change="areaChange()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="详细地址">
              <el-input v-model="userInfo.domicilePlaceAddress" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="第一学历学校" prop="firstDegreeSchool">
              <el-input v-model="userInfo.firstDegreeSchool" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="第一学历专业" prop="firstDegreeMajor">
              <el-input v-model="userInfo.firstDegreeMajor" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="最高学历学校" prop="highestDegreeSchool">
              <el-input v-model="userInfo.highestDegreeSchool" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="最高学历专业" prop="highestDegreeMajor">
              <el-input v-model="userInfo.highestDegreeMajor" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="是否服兵役" prop="isMilitaryService">
              <el-select v-model="userInfo.isMilitaryService" placeholder="请选择" style="width: 100%" clearable>
                <el-option
                  v-for="(item) in isMilitaryServiceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="相关工作年限" prop="basicWorkYears">
              <el-input-number
                v-model="userInfo.basicWorkYears"
                controls-position="right"
                :min="0"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="座机" prop="mobile">
              <el-input v-model="userInfo.mobile" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="userInfo.phone" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input v-model="userInfo.emergencyContact" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="紧急联系人电话" prop="emergencyContactPhone">
              <el-input v-model="userInfo.emergencyContactPhone" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="userInfo.email" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                v-model="userInfo.birthday"
                type="date"
                format="yyyy-MM-dd"
                style="width:100%"
                value-format="timestamp"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="技能水平自评" prop="skillLevelSelfAssess">
              <el-input v-model="userInfo.skillLevelSelfAssess" type="textarea" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="头像" prop="photo">
              <el-upload
                class="avatar-uploader"
                accept=".jpg, .jpeg, .png, .gif, .bmp, .JPG, .JPEG, .PBG"
                action="#"
                :file-list="imgFileList"
                list-type="picture-card"
                :show-file-list="true"
                :before-upload="beforeAvatarUpload"
                :http-request="uploadAvatar"
                :on-remove="handleImgFileRemove"
              >
                <i class="el-icon-plus" />
                <div slot="tip" class="el-upload__tip" style="color: red">建议尺寸：800*800像素</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
      <el-button type="primary" @click="newOrganizeRowClick">新增用户部门</el-button>
      <el-table :data="userInfo.sysUserOrganizeVos">
        <el-table-column label="序号" type="index" />
        <el-table-column label="组织名称">
          <template slot-scope="scope">
            <el-input v-model="scope.row.organizeName " @focus="openOrganizeTree(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="岗位名称">
          <template slot-scope="scope">
            <el-input v-model="scope.row.postName " @focus="openPostTable(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="是否主岗位">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.isMain" @change="selectMainPost(scope.$index)">
              <el-radio-button label="true">是</el-radio-button>
              <el-radio-button label="false">否</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="工作范围">
          <template slot-scope="scope">
            <el-input v-model="scope.row.workRange" clearable />
          </template>
        </el-table-column>
        <el-table-column label="任职开始时间">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.appointmentStartTime"
              type="date"
              format="yyyy-MM-dd"
              style="width:100%"
              value-format="timestamp"
              clearable
            />
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="userInfo.sysUserOrganizeVos.splice(scope.$index, 1)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-divider />
      <el-form :inline="true">
        <el-form-item label="角色">
          <div class="tag-group">
            <el-tag
              v-for="item in userInfo.sysUserRoleVos"
              :key="item.roleId"
              closable
              :disable-transitions="false"
              @close="removeRoleTag(item)"
            >
              {{ item.roleName }}
            </el-tag>
            <el-button class="button-new-tag" size="small" @click="openRoleDialog">+ 新职位</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="userDetailDialog.isShow = !userDetailDialog.isShow">取 消</el-button>
        <el-button type="primary" @click="saveUserDetailClick('currentUserDataForm')">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="请选择用户组织" :visible.sync="organizeDialogIsShow" :close-on-click-modal="false">
      <el-tree
        ref="organizeTree"
        :props="orgTrees"
        node-key="id"
        lazy
        :load="loadOrganizeNode"
        @node-click="selectOrganizeClick"
      />
    </el-dialog>
    <el-dialog title="请选择用户职位" :visible.sync="postDialogIsShow" :close-on-click-modal="false">
      <el-table :data="postData" @row-click="selectPostClick">
        <el-table-column label="序号" prop="id" />
        <el-table-column label="职位名称" prop="name" />
      </el-table>
    </el-dialog>
    <el-dialog title="请选择用户角色" :visible.sync="roleDialogIsShow" :close-on-click-modal="false">
      <el-table :data="roleData" @row-click="selectRoleClick">
        <el-table-column label="序号" prop="id" />
        <el-table-column label="角色名称" prop="name" />
      </el-table>
    </el-dialog>
    <el-dialog :title="historyDialog.title" :visible.sync="historyDialog.isShow" width="900px" :close-on-click-modal="false" append-to-body @closed="closeHistoryDialog">
      <div class="app-container">
        <el-table
          class="list-table"
          :data="historyDialog.tableData"
          :stripe="true"
          border
          style="width: 100%;margin-top: 10px;"
        >
          <el-table-column
            fixed
            label="组织名称"
            prop="organizeName"
            min-width="110"
          />
          <el-table-column
            label="岗位名称"
            prop="postName"
            min-width="150"
          />
          <el-table-column
            label="工作范围"
            prop="workRange"
            min-width="150"
          />
          <el-table-column
            label="是否主岗位"
            prop="isMain"
            min-width="100"
          >
            <template slot-scope="scope">
              {{ scope.row.isMain ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            min-width="100"
          ><template slot-scope="scope">
            {{ scope.row.startTime | parseDate('{y}-{m}-{d}') }}
          </template>
          </el-table-column>
          <el-table-column
            label="截止时间"
            min-width="100"
          ><template slot-scope="scope">
            {{ scope.row.endTime | parseDate('{y}-{m}-{d}') }}
          </template>
          </el-table-column>
          <el-table-column
            label="任职评价"
            prop="evaluate"
            min-width="120"
          />
          <el-table-column
            fixed="right"
            label="操作"
            min-width="150px"
          >
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="openevaluateDialog(scope.row)">评价</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :limit.sync="historyDialog.pageSet.pageSize"
          :page.sync="historyDialog.pageSet.pageIndex"
          :total="historyDialog.pageSet.total"
          @pagination="findHistoryPage"
        />
      </div>
    </el-dialog>
    <el-dialog
      :title="'任职履历评价'"
      :visible.sync="evaluateDialog.isShow"
      :close-on-click-modal="false"
      width="800px"
      style="margin-right: 20px"
      @closed="closeEvaluateDialog('evaluateDataForm')"
    >
      <el-form
        ref="evaluateDataForm"
        :model="evaluateDialog.formData"
        :rules="evaluateDialog.formRules"
        label-width="80px"
      >
        <el-form-item label="评价内容" prop="evaluate">
          <el-input v-model="evaluateDialog.formData.evaluate" type="textarea" clearable placeholder="请输入评价内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluateDialog.isShow = false">取 消</el-button>
        <el-button type="primary" @click="updateEvaluate('evaluateDataForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userList, deleteUser, getUserDetail, updateUserInfo, saveUserInfo, enableUser, disableUser } from '@/api/user'
import { findTreeById } from '@/api/organize'
import { findAllEnable } from '@/api/post'
import { findList } from '@/api/role'
import { list } from '@/api/area'
import { uploadSingleFileSub } from '@/api/file'
import { pageSub, updateSub } from '@/api/employment-history'

const defaultUserInfo = {
  id: null,
  code: null,
  loginName: null,
  name: null,
  nameEn: null,
  idCard: null,
  sex: 1,
  no: null,
  birthday: null,
  politicalStatus: null,
  maritalStatus: null,
  domicilePlaceProvinceCode: null,
  domicilePlaceProvinceName: null,
  domicilePlaceCityCode: null,
  domicilePlaceCityName: null,
  domicilePlaceCountyCode: null,
  domicilePlaceCountyName: null,
  nation: null,
  firstDegreeSchool: null,
  firstDegreeMajor: null,
  highestDegreeSchool: null,
  highestDegreeMajor: null,
  isMilitaryService: null,
  basicWorkYears: null,
  mobile: null,
  phone: null,
  emergencyContact: null,
  emergencyContactPhone: null,
  email: null,
  photo: null,
  skillLevelSelfAssess: null,
  domicilePlaceAddress: null,
  sysUserOrganizeVos: [],
  sysUserRoleVos: [],
  sysUserOrganizeRos: [],
  sysUserRoleRos: [],
  selectedKeys: []
}

export default {
  name: 'User',
  components: {
  },
  props: {},
  data() {
    return {
      queryParams: {
        loginName: null,
        name: null
      },
      tableData: null,
      pageSet: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      userInfo: Object.assign({}, defaultUserInfo),
      userDetailDialog: {
        type: 'new',
        isShow: false
      },
      editOrganize: null,
      organizeDialogIsShow: false,
      orgTrees: {
        children: null,
        label: 'name'
      },
      postDialogIsShow: false,
      postData: null,
      roleDialogIsShow: false,
      roleData: null,
      imgFileList: [],
      maritalStatusOptions: [
        { label: '未婚', value: 1 },
        { label: '已婚', value: 2 },
        { label: '离异', value: 3 },
        { label: '丧偶', value: 4 }
      ],
      isMilitaryServiceOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      areaProps: {
        value: 'code',
        label: 'name',
        lazy: true,
        async lazyLoad(node, resolve) {
          // 获取当前node对象中的level属性
          const { level } = node
          list({
            code: node.data == null ? 'CN' : node.data.code,
            type: node.data == null ? 2 : node.data.type + 1,
            isDelete: 0
          }).then(reponse => {
            if (reponse.code === 200) {
              const nodes = reponse.data
              if (level > 0) {
                nodes.forEach(item => {
                  item.leaf = level >= 2 // 判断是否为末尾节点，这个地方是0,1,2,3四级
                })
              }
              resolve(nodes)
            } else {
              // console.log(reponse)
            }
          }).catch({})
        }
      },
      modelKey: 0,
      formRules: {
        loginName: [{ required: true, message: '请输入登录名', trigger: 'change' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        nameEn: [{ required: true, message: '请输入英文名', trigger: 'change' },
          { pattern: /^[a-zA-Z]+$/, message: '请輸入正确的格式', trigger: 'change' }],
        idCard: [{ required: true, message: '请输入身份证号码', trigger: 'change' },
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请輸入正确的格式', trigger: 'change' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        no: [{ required: true, message: '请输入员工号', trigger: 'change' }],
        birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
        politicalStatus: [{ required: true, message: '请输入政治面貌', trigger: 'change' }],
        maritalStatus: [{ required: true, message: '请选择婚姻状态', trigger: 'change' }],
        selectedKeys: [{ type: 'array', required: true, message: '请选择户籍所在地', trigger: 'change' }],
        nation: [{ required: true, message: '请输入民族', trigger: 'change' }],
        // firstDegreeSchool: [{ required: true, message: '请输入第一学历学校', trigger: 'change' }],
        // firstDegreeMajor: [{ required: true, message: '请输入第一学历专业', trigger: 'change' }],
        // highestDegreeSchool: [{ required: true, message: '请输入最高学历学校', trigger: 'change' }],
        // highestDegreeMajor: [{ required: true, message: '请输入最高学历专业', trigger: 'change' }],
        // isMilitaryService: [{ required: true, message: '请选择是否服兵役', trigger: 'change' }],
        basicWorkYears: [{ required: true, message: '请输入相关工作年限', trigger: 'change' }],
        phone: [{ required: true, message: '请输入手机号码', trigger: 'change' },
          { pattern: /^(((400)-(\d{4})-(\d{3}$))|([1]+[0-9]{10}))$/, message: '请輸入正确的格式', trigger: 'change' }
        ],
        mobile: [
          // { required: true, message: '请输入座机', trigger: 'change' },
          { pattern: /^[0-9]{3,4}-[0-9]{7,8}$/, message: '请輸入正确的格式(如：0539-55665891)', trigger: 'change' }
        ],
        // emergencyContact: [{ required: true, message: '请输入紧急联系人', trigger: 'change' }],
        emergencyContactPhone: [
          // { required: true, message: '请输入紧急联系人电话', trigger: 'change' },
          { pattern: /^(((400)-(\d{4})-(\d{3}$))|([1]+[0-9]{10}))$/, message: '请輸入正确的格式', trigger: 'change' }
        ],
        email: [
          // { required: true, message: '请输入电子邮箱', trigger: 'change' },
          { pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: '请輸入正确的格式', trigger: 'change' }
        ]
        // skillLevelSelfAssess: [{ required: true, message: '请输入技能水平自评', trigger: 'change' }],
        // photo: [{ required: true, message: '请上传图片', trigger: 'change' }]
      },
      historyDialog: {
        title: '任职履历',
        isShow: false,
        searchForm: {},
        tableData: [],
        pageSet: {
          pageIndex: 1, // 当前页
          pageSize: 10, // 每页多少条
          total: 0 // 共多少条数据
        }
      },
      evaluateDialog: {
        title: '任职履历',
        isShow: false,
        formData: {},
        formRules: {
          evaluate: [{ required: true, message: '请输入评价', trigger: 'change' }]
        }
      }
    }
  },
  created() {
  },
  mounted() {
    this.submitClick()
  },
  methods: {
    submitClick() {
      userList({
        ...this.queryParams,
        ...this.pageSet
      }).then(
        response => {
          this.tableData = response.data.list
          this.pageSet.total = response.data.total
          this.pageSet.currentPage = response.data.pageIndex
          this.pageSet.pageSize = response.data.pageSize
        }
      )
    },
    newUserClick() {
      this.userInfo = {
        id: null,
        loginName: null,
        no: null,
        name: null,
        sex: null,
        nation: null,
        email: null,
        mobile: null,
        phone: null,
        photo: null,
        birthday: null,
        sysUserOrganizeVos: [],
        sysUserRoleVos: [],
        sysUserOrganizeRos: [],
        sysUserRoleRos: []
      }
      this.userDetailDialog.type = 'new'
      this.userDetailDialog.isShow = !this.userDetailDialog.isShow
    },
    handleCurrentChange(val) {
      this.pageSet.currentPage = val
      this.submitClick()
    },
    handleSizeChange(val) {
      this.pageSet.pageSize = val
      this.submitClick()
    },
    saveUserDetailClick(formName) {
      this.userInfo.sysUserOrganizeRos = this.userInfo.sysUserOrganizeVos
      this.userInfo.sysUserRoleRos = this.userInfo.sysUserRoleVos
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          return false
        }
        if (this.userInfo.id) {
          updateUserInfo(this.userInfo).then(response => {
            this.userDetailDialog.isShow = !this.userDetailDialog.isShow
            this.submitClick()
          })
        } else {
          saveUserInfo(this.userInfo).then(response => {
            this.userDetailDialog.isShow = !this.userDetailDialog.isShow
            this.submitClick()
          })
        }
      })
    },
    editUserDetailClick(id) {
      this.userDetailDialog.type = 'edit'
      getUserDetail(id).then(
        response => {
          this.userInfo = response.data
          this.userInfo.selectedKeys = [
            this.userInfo.domicilePlaceProvinceCode,
            this.userInfo.domicilePlaceCityCode,
            this.userInfo.domicilePlaceCountyCode
          ]
          this.userDetailDialog.isShow = !this.userDetailDialog.isShow
        }
      )
    },
    deleteUserClick(id) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUser(id).then(response => {
          this.$message({
            showClose: true,
            type: 'success',
            message: '删除成功!'
          })
          this.submitClick()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    newOrganizeRowClick() {
      this.userInfo.sysUserOrganizeVos.push({
        'userId': this.userInfo.id,
        'organizeId': null,
        'organizeName': null,
        'postId': null,
        'postName': null,
        'isMain': false
      })
    },
    openOrganizeTree(row) {
      this.editOrganize = row
      this.organizeDialogIsShow = !this.organizeDialogIsShow
    },
    loadOrganizeNode(node, resolve) {
      const id = node.data ? node.data.id : 0
      findTreeById(id).then(response => {
        resolve(response.data)
      })
    },
    selectOrganizeClick(data) {
      this.editOrganize.organizeId = data.id
      this.editOrganize.organizeName = data.name
      this.editOrganize.postId = null
      this.editOrganize.postName = null
      this.organizeDialogIsShow = !this.organizeDialogIsShow
    },
    openPostTable(row) {
      this.editOrganize = row
      findAllEnable(row.organizeId).then(response => {
        this.postData = response.data
        this.postDialogIsShow = !this.postDialogIsShow
      })
    },
    selectPostClick(row) {
      this.editOrganize.postId = row.id
      this.editOrganize.postName = row.name
      this.postDialogIsShow = !this.postDialogIsShow
    },
    selectMainPost(index) {
      for (let i = 0; i < this.userInfo.sysUserOrganizeVos.length; i++) {
        if (i !== index) {
          this.userInfo.sysUserOrganizeVos[i].isMain = false
        }
      }
    },
    openRoleDialog() {
      findList().then(response => {
        this.roleData = response.data.filter(item => {
          return !this.userInfo.sysUserRoleVos.find(it => it.roleId === item.id)
        })
      })
      this.roleDialogIsShow = !this.roleDialogIsShow
    },
    selectRoleClick(row) {
      this.userInfo.sysUserRoleVos.push({
        'userId': this.userInfo.id,
        'roleId': row.id,
        'roleName': row.name
      })
      this.roleDialogIsShow = !this.roleDialogIsShow
    },
    removeRoleTag(tag) {
      this.userInfo.sysUserRoleVos.splice(this.userInfo.sysUserRoleVos.indexOf(tag), 1)
    },
    uploadAvatar(data) {
      const formData = new FormData()
      formData.append('file', data.file)
      uploadSingleFileSub(formData).then(response => {
        if (response.code !== 200) {
          this.$message({
            message: response.msg,
            type: 'error'
          })
          return
        }
        console.log(response)
        this.userInfo.photo = response.data.visitUrl
        this.imgFileList = []
        this.imgFileList.push({ id: response.data.id, url: response.data.visitUrl })
      })
    },
    handleImgFileRemove() {
      this.imgFileList = []
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isLt2M
    },
    enableUser(id) {
      this.$confirm('确认启用该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableUser(id).then(response => {
          this.$message({
            showClose: true,
            type: 'success',
            message: '启用成功!'
          })
          this.submitClick()
        })
      })
    },
    disableUser(id) {
      this.$confirm('确认禁用该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        disableUser(id).then(response => {
          this.$message({
            showClose: true,
            type: 'success',
            message: '禁用成功!'
          })
          this.submitClick()
        })
      })
    },
    // 获取省市区
    areaChange() {
      var data = this.userInfo.selectedKeys
      if (data === undefined) {
        return
      }
      if (data.length === 1) {
        this.userInfo.domicilePlaceProvinceCode = data[0]
      } else if (data.length === 2) {
        this.userInfo.domicilePlaceProvinceCode = data[0]
        this.userInfo.domicilePlaceCityCode = data[1]
      } else if (data.length === 3) {
        this.userInfo.domicilePlaceProvinceCode = data[0]
        this.userInfo.domicilePlaceCityCode = data[1]
        this.userInfo.domicilePlaceCountyCode = data[2]
      }
      // console.log(this.dialogData.selectedKeys)
    },
    // 清空数据
    closeDialog(formName) {
      this.userInfo = Object.assign({}, defaultUserInfo)
      this.$refs[formName].resetFields()
    },
    showEmploymentHistorys(userId) {
      this.historyDialog.isShow = true
      this.historyDialog.searchForm = { 'userId': userId }
      this.findHistoryPage()
    },
    findHistoryPage() {
      pageSub({ ...this.historyDialog.searchForm, ...this.historyDialog.pageSet }).then(
        response => {
          this.historyDialog.tableData = response.data.list
          this.historyDialog.pageSet.total = response.data.total
          this.historyDialog.pageSet.pageIndex = response.data.pageIndex
          this.historyDialog.pageSet.pageSize = response.data.pageSize
        }
      )
    },
    closeHistoryDialog() {
      this.historyDialog.tableData = []
    },
    openevaluateDialog(row) {
      this.evaluateDialog.formData = row
      this.evaluateDialog.isShow = !this.evaluateDialog.isShow
    },
    closeEvaluateDialog(formName) {
      this.evaluateDialog.formData = {}
      this.$refs[formName].resetFields()
    },
    updateEvaluate(formName) {
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          return false
        }
        updateSub(this.evaluateDialog.formData).then(
          response => {
            if (response.code === 200) {
              this.$message({
                showClose: true,
                message: '提交成功',
                type: 'success'
              })
            } else {
              this.$message({
                showClose: true,
                type: 'error',
                message: response.msg
              })
            }
            this.evaluateDialog.isShow = !this.evaluateDialog.isShow
            this.findHistoryPage()
          }
        ).catch({})
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.el-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
