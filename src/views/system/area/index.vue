<template>
  <div class="app-container">
    <el-row :gutter="12">
      <el-col :span="12">
        <el-card>
          <el-row>
            <el-col :span="15">
              <div style="padding: 12px 0;">
                当前选择: {{ currentData.data.name }}
                <el-button v-if="currentData.data.name" type="text" @click="cancelSelect">
                  取消选择
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-card body-style="height:70vh;overflow:auto;">
                <el-tree
                  ref="tree"
                  empty-text="当前无地区数据"
                  node-key="code"
                  :accordion="true"
                  :props="defaultProps"
                  :load="loadNode"
                  lazy
                >
                  <span slot-scope="{ node, data }" class="flex-adapter" @click="handNodeClick(node, data)">
                    <span style="font-size: 12px;">{{ node.label }}</span>
                    <span style="font-size: 12px;">
                      <el-button v-if="data.type !== 4" style="font-size: 12px;" type="text" @click.stop="openAddArea(node, data)">添加下级地区</el-button>
                      <el-button v-if="data.type === 4 || node.isLeaf" style="font-size: 12px;" type="text" @click.stop="deleteItem(node, data)">删除</el-button>
                    </span>
                  </span>
                </el-tree>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>{{ currentData.title }}</span>
          </div>
          <el-form
            ref="currentDataForm"
            class="form-width-300"
            label-suffix=":"
            :model="currentData.data"
            :rules="currentData.rules"
            label-width="100px"
          >
            <el-form-item label="编码" prop="code">
              <el-input v-model="currentData.data.code" class="input-width-150" placeholder="请输入地区编码" :disabled="currentData.readOnly" />
            </el-form-item>
            <el-form-item label="父编码">
              <el-input v-model="currentData.data.parentCode" class="input-width-200" disabled />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="currentData.data.name" class="input-width-200" placeholder="请输入地区名称" clearable />
            </el-form-item>
            <el-form-item label="简称" prop="nameAbbr">
              <el-input v-model="currentData.data.nameAbbr" class="input-width-200" placeholder="请输入地区简称" clearable />
            </el-form-item>
            <el-form-item label="略写" prop="nameSimple">
              <el-input v-model="currentData.data.nameSimple" class="input-width-200" placeholder="请输入地区略写" clearable />
            </el-form-item>
            <el-form-item label="拼音全称">
              <el-input v-model="currentData.data.completeSpelling" class="input-width-300" placeholder="请输入地区拼音全称" clearable />
            </el-form-item>
            <el-form-item label="邮政编码" prop="postalCode">
              <el-input v-model="currentData.data.postalCode" class="input-width-200" placeholder="请输入地区邮政编码" clearable />
            </el-form-item>
            <el-form-item label-width="0" style="margin-left: 5px">
              <el-row>
                <el-col :span="5">
                  <el-form-item label="地区类型" prop="type">
                    <el-checkbox v-model="currentData.checked" :disabled="true">{{ currentTypeName }}</el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="排序">
                    <el-input-number
                      v-model="currentData.data.sort"
                      controls-position="right"
                      :min="1"
                      :max="100"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="是否启用">
                    <el-switch
                      v-model="currentData.data.isDelete"
                      active-color="#ff4949"
                      inactive-color="#13ce66"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="cancelSelect()">关闭</el-button>
              <el-button type="primary" @click="saveArea('currentDataForm')">确定</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { list, save, businessDel, update } from '@/api/area'

export default {
  name: 'Area',
  props: {},
  data() {
    return {
      defaultProps: {
        children: 'childrenList',
        isLeaf: 'isLeaf',
        label: 'name',
        code: 'code'
      },
      // 地区基本信息数据
      currentData: {
        // type: 0:新增，1：修改
        title: '添加国家',
        readOnly: false,
        checked: true,
        type: 0,
        node: {},
        data: this.initData(),
        rules: {
          code: [
            { required: true, message: '请输入地区编码', trigger: 'change' }
          ],
          name: [
            { required: true, message: '请输入地区名称', trigger: 'change' }
          ],
          nameAbbr: [
            { required: true, message: '请输入地区简称', trigger: 'change' }
          ],
          nameSimple: [
            { required: true, message: '请输入地区略写', trigger: 'change' }
          ],
          postalCode: [
            { required: true, message: '请输入邮政编码', trigger: 'change' }
          ]
        }
      }
    }
  },
  computed: {
    currentTypeName() {
      let result = null
      switch (this.currentData.data.type) {
        case 1:
          result = '国家'
          break
        case 2:
          result = '省'
          break
        case 3:
          result = '市'
          break
        case 4:
          result = '区/县'
          break
        default:
          break
      }
      return result
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    loadNode(node, resolve) {
      // 树节点懒加载
      list({
        code: node.data == null ? null : node.data.code,
        type: node.data == null ? 1 : null,
        isDelete: 0
      }).then(resp => {
        resolve(resp.data)
      }).catch(exception => {
        console.log(exception)
      })
    },
    handNodeClick(node, data) {
      if (data == null || data.code == null) {
        return
      }
      this.currentData.node = node
      this.currentData.data = data
      this.currentData.title = '地区基本信息详情'
      this.currentData.checked = true
      this.currentData.readOnly = true
      this.currentData.type = 1
    },
    cancelSelect() {
      this.currentData.node = {}
      this.currentData.data = this.initData()
      this.currentData.type = 0
      this.currentData.sort = 1
      this.currentData.title = '添加国家'
      this.currentData.checked = true
      this.currentData.readOnly = false
    },
    openAddArea(node, data) {
      this.currentData.node = node
      this.currentData.data = this.initData()
      this.currentData.data.type = data.type + 1
      this.currentData.data.parentCode = data.code
      this.currentData.title = '新增地区基本信息'
      this.currentData.type = 0
      this.currentData.checked = true
      this.currentData.readOnly = false
    },
    saveArea(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          return false
        }
        // 提交数据
        if (this.currentData.type === 0) {
          save(this.currentData.data).then(resp => {
            this.$message({
              showClose: true,
              message: ' "' + this.currentData.data.name + '" 地区数据保存成功！',
              type: 'success'
            })
            const childrenList = this.currentData.node.data.childrenList == null
              ? [] : this.currentData.node.data.childrenList
            this.currentData.node.data.childrenList = []
            this.currentData.node.isLeaf = false
            this.currentData.node.data.isLeaf = false
            childrenList.push(resp.data)
            this.$refs.tree.updateKeyChildren(this.currentData.node.data.code, childrenList)
            // 设置loaded为false；模拟一次节点展开事件，加载更新后的新数据；
            this.currentData.node.loaded = false
            this.currentData.node.expand()
          }).catch(exception => {
            console.log(exception)
          })
        }
        if (this.currentData.type === 1) {
          update(this.currentData.data).then(resp => {
            this.$message({
              showClose: true,
              message: ' "' + this.currentData.data.name + '" 地区数据更新成功！',
              type: 'success'
            })
            if (resp.data) {
              this.currentData.node.data = this.currentData.data
              this.$refs[formName].resetFields()
            }
          }).catch(exception => {
            console.log(exception)
          })
        }
      })
    },
    append(node, data) {
      if (node.childrenList == null) {
        node.data.childrenList = []
      }
      node.isLeaf = false
      node.isLeafByUser = false
      node.data.isLeaf = false
      node.data.childrenList.push(data)
    },
    deleteItem(node, data) {
      console.log(data)
      this.$confirm('确定删除 ’' + data.name + '‘ 地区数据吗？', '提示', {}).then(() => {
        businessDel(data.code).then(resp => {
          if (resp.data) {
            this.cancelSelect()
            this.$refs.tree.remove(data)
          } else {
            this.$message({
              showClose: true,
              message: ' "' + this.currentData.data.name + '" 地区数据删除失败！失败原因：' + resp.msg,
              type: 'error'
            })
          }
        }).catch(exception => {
          console.log(exception)
        })
      }).catch(() => {
        // 几点取消的提示
      })
    },
    initData() {
      return {
        code: null,
        parentCode: null,
        name: '',
        type: 1,
        completeSpelling: null,
        abbreviation: null,
        nameSimple: null,
        postalCode: null,
        sort: 1,
        isDelete: 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.input-width-150 {
  width: 150px
}

.input-width-200 {
  width: 200px
}

.input-width-300 {
  width: 300px
}

.flex-adapter {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
