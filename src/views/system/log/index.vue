<template>
  <div class="app-container">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="操作用户">
        <el-autocomplete
          v-model="formInline.name"
          class="inline-input"
          :fetch-suggestions="querySearch"
          placeholder="请输入用户姓名"
          value-key="name"
          value="id"
          :trigger-on-focus="false"
          @select="handleQuerySelect"
        />
      </el-form-item>
      <el-form-item label="日志类型">
        <el-select
          v-model="formInline.logType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作类型">
        <el-select
          v-model="formInline.operateType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in operateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="querySubmit">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%"
      border
    >
      <el-table-column
        prop="logContent"
        label="日志内容"
        min-width="250px"
      />
      <el-table-column label="日志类型" min-width="100px">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.logType === 1" type="info">操作日志</el-tag>
          <el-tag v-if="scope.row.logType === 2" type="primary">登录日志</el-tag>
          <el-tag v-if="scope.row.logType === 3" type="success">定时任务日志</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.operateType === 1" type="info">添加</el-tag>
          <el-tag v-if="scope.row.operateType === 2">修改</el-tag>
          <el-tag v-if="scope.row.operateType === 3" type="success">删除</el-tag>
          <el-tag v-if="scope.row.operateType === 4" type="primary">查询</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="ip"
        label="IP地址"
        min-width="150px"
      />
      <el-table-column
        prop="requestTime"
        :formatter="timeFormatter"
        label="请求时间"
        min-width="150px"
      />
      <el-table-column
        prop="timeConsuming"
        label="耗时"
        min-width="80px"
      />
      <el-table-column
        prop="requestResult"
        label="请求结果"
        min-width="80px"
      />
      <el-table-column
        prop="userName"
        label="操作用户"
        min-width="120px"
      />
      <el-table-column
        prop="userPhone"
        label="操作用户手机号"
        min-width="150px"
      />
      <el-table-column
        prop="organizeName"
        label="主岗位"
        min-width="180px"
      />
      <el-table-column align="center" label="请求参数" min-width="80px">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.requestParam == null || scope.row.requestParam == ''"
            type="text"
          >无</el-button>
          <el-button
            v-if="scope.row.requestParam != null && scope.row.requestParam != ''"
            type="text"
            @click="showRequestParam(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="请求方法" width="80px">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.apiMethod == null || scope.row.apiMethod == ''"
            type="text"
          >无</el-button>
          <el-button
            v-if="scope.row.apiMethod != null && scope.row.apiMethod != ''"
            type="text"
            @click="showMethod(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="margin-top: 5px;">
      <el-pagination
        background
        :current-page="formInline.current"
        :page-size="formInline.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="formInline.total"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :title="formDialog.title"
      :visible.sync="formDialog.dialogFormVisible"
      width="40%"
    >
      <span>{{ content }}</span>
    </el-dialog>
  </div>
</template>

<script>
import { findPage } from '@/api/log'
import { userList } from '@/api/user'
import { parseTime } from '@/utils'

export default {
  name: 'Leave',
  data() {
    return {
      content: '',
      dialogType: 'new',
      bizId: null,
      processInstanceId: null,
      currentUserId: null,
      showParam: {
        balance: null,
        exBalance: null,
        type: null
      },
      typeName: '',
      typeOptions: [{
        value: 1,
        label: '操作日志'
      }, {
        value: 2,
        label: '登录日志'
      }, {
        value: 3,
        label: '定时任务日志'
      }],
      operateOptions: [{
        value: 1,
        label: '添加'
      }, {
        value: 2,
        label: '修改'
      }, {
        value: 3,
        label: '删除'
      }, {
        value: 4,
        label: '查询'
      }],
      restaurants: [],
      userQuery: {
        loginName: null,
        name: null,
        currentPage: 1,
        pageSize: 10
      },
      formDialog: {
        title: '',
        dialogFormVisible: false,
        dialogAddFormVisible: false,
        buttonName: ''
      },
      tableData: [],
      typeQuery: {},
      formInline: {
        logType: null,
        operateType: null,
        userId: null,
        pageIndex: 1,
        pageSize: 10,
        total: 0
      },
      form: {
        id: null,
        userId: null,
        userName: null,
        userPhone: null,
        title: '',
        typeId: null,
        typeName: null,
        type: null,
        beginTime: null,
        endTime: null,
        countLength: null,
        subjectMatter: null,
        status: null,
        reject: null
      },
      addForm: {
        id: null,
        calculate: '0',
        balance: null
      },
      calculateForm: {
        typeId: null,
        beginTime: null,
        endTime: null
      },
      lastForm: {
        userId: null,
        typeId: null
      },
      rules: {
        userName: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'change' }
        ],
        typeName: [
          { required: true, message: '请选择请假单', trigger: 'change' }
        ],
        beginTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        balance: [
          { required: true, message: '请输入新增假期数', trigger: 'change' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: 'change' }
        ],
        calculate: [
          { required: true, message: '请输入新增假期数', trigger: 'change' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.querySubmit()
  },
  methods: {
    timeFormatter(row, column) {
      const date = row[column.property]
      if (date === undefined) {
        return ''
      }
      return parseTime(date, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    showMethod(data) {
      this.formDialog.title = '请求方法'
      this.formDialog.dialogFormVisible = true
      this.content = data.apiMethod
    },
    showRequestParam(data) {
      this.formDialog.title = '请求参数'
      this.formDialog.dialogFormVisible = true
      this.content = data.requestParam
    },
    querySubmit() {
      findPage(this.formInline).then(response => {
        this.tableData = response.data.list
        this.formInline.pageIndex = response.data.pageIndex
        this.formInline.pageSize = response.data.pageSize
        this.formInline.total = response.data.total
      })
    },
    querySearch(queryString, cb) {
      userList(this.userQuery).then(response => {
        const restaurants = response.data.list
        const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
        // 调用 callback 返回建议列表的数据
        console.info(results)
        cb(results)
      })
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleQuerySelect(item) {
      this.formInline.userId = item.id
    },
    handleCurrentChange(val) {
      this.formInline.pageIndex = val
      this.querySubmit()
    }
  }
}
</script>

<style scoped>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
</style>
