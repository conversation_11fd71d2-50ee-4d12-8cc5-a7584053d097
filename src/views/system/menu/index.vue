<template>
  <div class="app-container">
    <el-button type="primary" style="margin: 10px 0;" @click="add">添加菜单</el-button>
    <el-table
      :data="data"
      style="width: 100%;"
      row-key="id"
      default-expand-all
      :tree-props="{ children: 'children' }"
    >
      <el-table-column
        prop="name"
        label="名称"
        min-width="250px"
      />
      <el-table-column
        prop="code"
        label="编码"
        min-width="200px"
      />
      <el-table-column align="center" label="类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1">菜单</el-tag>
          <el-tag v-if="scope.row.type === 2" type="success">数据</el-tag>
          <el-tag v-if="scope.row.type === 3" type="danger">按钮</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="authorizeKey"
        label="授权标识"
        min-width="260px"
      />
      <el-table-column
        prop="status"
        label="状态"
        min-width="80"
      >
        <template slot-scope="scope">
          {{ scope.row.status ? '有效' : '无效' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="frontPath"
        label="前端路由"
        min-width="200px"
      />
      <el-table-column
        prop="frontPolymerization"
        label="前端组件"
        min-width="200px"
      />
      <el-table-column
        prop="frontName"
        label="前端名称"
        min-width="200px"
      />
      <el-table-column
        prop="defaultRedirect"
        label="跳转路径"
        min-width="200px"
      />
      <el-table-column align="center" label="隐藏路由">
        <template slot-scope="scope">
          <el-switch v-show="scope.row.type === 1" v-model="scope.row.hideRoute" active-color="#13ce66" @change="updateMenuByTable(scope.row, '隐藏路由')" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="聚合路由">
        <template slot-scope="scope">
          <el-switch v-show="scope.row.type === 1" v-model="scope.row.hideRoute" active-color="#13ce66" @change="updateMenuByTable(scope.row, '聚合路由')" />
        </template>
      </el-table-column>
      <el-table-column
        prop="cache"
        label="是否缓存"
        min-width="80"
      >
        <template slot-scope="scope">
          <div v-show="scope.row.type === 1">{{ scope.row.cache ? '是' : '否' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="isTopDropDown"
        label="顶部下拉菜单"
        min-width="120"
      >
        <template slot-scope="scope">
          <div v-show="scope.row.type === 1">{{ scope.row.isTopDropDown ? '是' : '否' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="outLink"
        label="是否外连接"
        min-width="100"
      >
        <template slot-scope="scope">
          <div v-show="scope.row.type === 1">{{ scope.row.outLink ? '是' : '否' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="250" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="get(scope.row)"
          >编辑</el-button>
          <el-button type="text" @click="remove(scope.row)">删除</el-button>
          <el-dropdown v-if="scope.row.type === 1">
            <el-button style="margin-left: 10px;" type="text">更多<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button v-if="scope.row.type === 1" type="text" @click="add(scope.row, 1)">添加下级菜单</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-if="scope.row.type === 1" type="text" @click="add(scope.row, 2)">添加数据</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-if="scope.row.type === 1" type="text" @click="add(scope.row, 3)">添加按钮</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog top="10px" :visible.sync="dialog.dialogFormVisible" :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-name">{{ dialog.title }}</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="上级菜单">
          <el-cascader
            v-model="form.parentId"
            :options="data"
            :props="selectProps"
            clearable
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="form.type" placeholder="请选择类型" :disabled="true">
            <el-option label="菜单" value="1" />
            <el-option label="数据" value="2" />
            <el-option label="按钮" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="顶部下拉菜单">
          <el-switch v-model="form.isTopDropDown" />
        </el-form-item>
        <el-form-item
          prop="name"
          label="名称"
        >
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item v-if="form.type !== '1'" prop="authorizeKey" label="授权标识">
          <el-input v-model="form.authorizeKey" />
        </el-form-item>
        <el-form-item label="前端路径">
          <el-input v-model="form.frontPath" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="前端组件">
          <el-input v-model="form.frontPolymerization" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="前端名称">
          <el-input v-model="form.frontName" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="前端默认跳转">
          <el-input v-model="form.defaultRedirect" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="图标">
          <el-input v-model="form.icon" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="隐藏路由">
          <el-switch v-model="form.hideRoute" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="聚合路由">
          <el-switch v-model="form.polymerizationRoute" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="是否缓存">
          <el-switch v-model="form.cache" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input v-model="form.sort" />
        </el-form-item>
        <el-form-item v-show="form.type === '1'" label="外连接">
          <el-switch v-model="form.outLink" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" />
        </el-form-item>
        <el-form-item label="是否有效">
          <el-switch v-model="form.status" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel('form')">取 消</el-button>
        <el-button type="primary" @click="onSubmit('form')">{{ dialog.buttonName }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { del, findTree, get, insert, update } from '@/api/menu'
import { delButton, findButtonList, getButton, insertButton, updateButton } from '@/api/button'

export default {
  data() {
    return {
      showParams: true,
      ruleDataDrawer: false,
      buttonDataDrawer: false,
      dialog: {
        title: '',
        dialogFormVisible: false,
        dialogButtonFormVisible: false,
        dialogParamFormVisible: false,
        buttonName: ''
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      selectProps: {
        value: 'id',
        children: 'children',
        label: 'name',
        checkStrictly: true
      },
      form: {
        id: null,
        parentId: null,
        name: '',
        code: '',
        icon: '',
        frontPath: '',
        frontPolymerization: '',
        frontName: '',
        defaultRedirect: '',
        hideRoute: false,
        polymerizationRoute: false,
        cache: true,
        outLink: false,
        isTopDropDown: false,
        sort: 1,
        type: null,
        status: true,
        description: ''
      },
      buttonForm: {},
      buttonData: [],
      buttonsData: {
        name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
        authorizeKey: [
          { required: true, message: '请输入授权标识', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.findAll()
  },
  methods: {
    get(data) {
      get(data.id).then(response => {
        console.info(response.data)
        this.form = response.data
        this.form.type = response.data.type.toString()
      })
      this.dialog.dialogFormVisible = true
      this.dialog.buttonName = '立即修改'
      this.dialog.title = '修改菜单'
    },
    getButton(data) {
      getButton(data.id).then(response => {
        this.buttonForm = response.data
      })
      this.dialog.dialogButtonFormVisible = true
      this.dialog.buttonName = '立即修改'
      this.dialog.title = '修改数据规则'
    },
    findAll() {
      findTree().then(response => {
        this.data = JSON.parse(JSON.stringify(response.data))
      })
    },
    showButton(data) {
      this.buttonDataDrawer = true
      this.buttonForm = {}
      this.buttonForm.frontPath = data.frontPath
      findButtonList(this.buttonForm).then(response => {
        this.buttonData = response.data
        this.buttonForm.isEnable = true
      })
    },
    updateButtonByTable(data) {
      var sign = ''
      if (data.isEnable) {
        sign = '启用'
      } else {
        sign = '禁用'
      }
      var messageShow = '按钮' + data.name + sign + '成功'
      updateButton(data).then(response => {
        this.$message({
          message: messageShow,
          type: 'success'
        })
      })
    },
    updateMenuByTable(data, sign) {
      var messageShow = '设置菜单' + data.name + '为' + sign + '成功'
      update(data).then(response => {
        this.$message({
          message: messageShow,
          type: 'success'
        })
      })
    },
    onSubmitButton(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.buttonForm.id) {
            updateButton(this.buttonForm).then(response => {
              this.$message({
                message: '按钮修改成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              findButtonList(this.buttonForm).then(response => {
                this.buttonData = response.data
              })
              this.dialog.dialogButtonFormVisible = false
            })
          } else {
            insertButton(this.buttonForm).then(response => {
              this.$message({
                message: '按钮新增成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              findButtonList(this.buttonForm).then(response => {
                this.buttonData = response.data
              })
              this.dialog.dialogButtonFormVisible = false
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    add(data, type) {
      if (type == null) {
        this.form.type = '1'
      } else {
        this.form.type = type.toString()
      }
      this.form.id = null
      this.form.parentId = data.id
      this.form.name = ''
      this.form.code = ''
      this.form.frontPath = data.frontPath
      this.form.status = true
      this.form.description = ''
      this.dialog.buttonName = '立即创建'
      this.dialog.dialogFormVisible = true
      this.dialog.title = '新增菜单'
    },
    addButton() {
      this.dialog.buttonName = '立即创建'
      this.dialog.dialogButtonFormVisible = true
      this.dialog.title = '新增按钮'
    },
    remove(data) {
      if (data.children) {
        this.$message({
          message: '该菜单拥有子菜单，不允许直接删除，请先删除子菜单',
          type: 'error'
        })
        return
      }
      this.$confirm('此操作将删除该菜单及其子菜单, 是否继续?', '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del(data).then(response => {
          this.$message({
            message: '菜单删除成功',
            type: 'success'
          })
          this.findAll()
        })
      }).catch(() => {})
    },
    removeButton(data) {
      this.$confirm('该操作将删除该按钮, 是否继续?', '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delButton(data).then(response => {
          this.$message({
            message: '按钮删除成功',
            type: 'success'
          })
          findButtonList(this.buttonForm).then(response => {
            this.buttonData = response.data
          })
        })
      })
    },
    cancel(formName) {
      this.dialog.dialogFormVisible = false
      this.$refs[formName].resetFields()
    },
    cancelButton(formName) {
      this.dialog.dialogButtonFormVisible = false
      this.$refs[formName].resetFields()
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            update(this.form).then(response => {
              this.$message({
                message: '菜单修改成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              this.findAll()
              this.dialog.dialogFormVisible = false
            })
          } else {
            // 获取编码
            if (this.form.parentId != null && this.form.parentId[0] != null) {
              var num = this.form.parentId.length
              this.form.parentId = this.form.parentId[num - 1]
            }
            insert(this.form).then(response => {
              this.$message({
                message: '菜单新增成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              this.findAll()
              this.dialog.dialogFormVisible = false
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    chooseCondition(val) {
      this.showParams = val !== '11'
    },
    selectParamValue(val) {
      this.ruleForm.value = val.itemValue
      this.dialog.dialogParamFormVisible = false
    }
  }
}
</script>

<style>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
</style>
