<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <div class="search-view">
        <el-form :inline="true" :model="formData" label-suffix="：">
          <el-form-item label="岗位名称">
            <el-input v-model="formData.keyword" placeholder="请输入关键字" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryByKeyword()">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-button type="primary" @click="addEdit()">添加</el-button>
    <!-- 列表 -->
    <div>
      <el-table
        border
        :data="tableData"
        :stripe="true"
        style="width: 100%; margin-top: 20px;"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
        />
        <el-table-column
          prop="name"
          label="名称"
          width="120"
        />
        <el-table-column
          prop="code"
          label="编码"
          width="240"
        />
        <el-table-column
          prop="belongRootOrganizeName"
          label="所属根组织"
          width="120"
        />
        <el-table-column
          prop="workDuty"
          label="工作职责"
          width="240"
        />
        <el-table-column
          label="状态"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnable === false" type="danger">禁用</el-tag>
            <el-tag v-else type="success">启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createBy"
          label="创建人"
          width="120"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime | parseDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateBy"
          label="更新人"
          width="120"
        />
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime | parseDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注"
          width="150"
        />
        <el-table-column
          fixed="right"
          label="操作"
          min-width="230"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="addEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deletePost(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="block">
      <!--      <span class="demonstration">共 {{pageSet.assign}} 条</span>-->
      <el-pagination
        :current-page="pageSet.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSet.pageSize"
        :total="pageSet.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <div>
      <el-dialog
        :title="title"
        :visible.sync="dialogFormVisible"
        :close-on-click-modal="false"
        width="550px"
        @closed="closeDialog('currentPostDataForm')"
        @open="openDialog"
      >
        <el-form
          ref="currentPostDataForm"
          :model="dialogData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="岗位名称" prop="name">
            <el-input v-model="dialogData.name" clearable placeholder="请输入岗位名称" />
          </el-form-item>
          <el-form-item label="岗位编码" prop="code">
            <el-input v-model="dialogData.code" />
          </el-form-item>
          <el-form-item label="所属根组织" prop="belongRootOrganizeId">
            <el-select v-model="dialogData.belongRootOrganizeId" placeholder="请选择" style="width: 100%" clearable>
              <el-option v-for="(item) in rootOrganizeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工作职责" prop="workDuty">
            <el-input v-model="dialogData.workDuty" autocomplete="off" type="textarea" placeholder="请输入工作职责" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-switch
              v-model="dialogData.isEnable"
              style="display: block"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="dialogData.remarks" type="textarea" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" :disabled="btnDisabled" @click="addOrUpdatePost('currentPostDataForm')">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { queryByKeywordSub, addSub, updateSub, detailSub, deleteSub } from '@/api/post'
import { findTreeById } from '@/api/organize'

export default {
  name: 'Post',
  props: {},
  data() {
    return {
      btnDisabled: false,
      tableData: [],
      formData: {
        keyword: null
      },
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      dialogFormVisible: false,
      dialogData: {},
      rootOrganizeList: [],
      title: '',
      formRules: {
        name: [{ required: true, message: '请输入岗位名称', trigger: 'change' }],
        code: [{ required: true, message: '请输入岗位编码', trigger: 'change' }],
        belongRootOrganizeId: [{ required: true, message: '请选择所属根组织', trigger: 'change' }],
        workDuty: [{ required: true, message: '请输入工作职责', trigger: 'change' }]
      }
    }
  },
  created() {
  },
  mounted() {
    this.queryByKeyword()
  },
  methods: {
    // 获取组织所有根节点
    findTreeRoot() {
      findTreeById(0).then(response => {
        this.rootOrganizeList = response.data
      }).catch({})
    },
    async queryByKeyword() {
      queryByKeywordSub({
        ...this.pageSet,
        ...this.formData
      }).then(response => {
        this.tableData = response.data.list
        this.pageSet.total = response.data.total
        this.pageSet.pageIndex = response.data.pageIndex
        this.pageSet.pageSize = response.data.pageSize
      }).catch({})
    },
    // 跳页
    handleCurrentChange(val) {
      this.$set(this.pageSet, 'pageIndex', val)
      this.queryByKeyword()
    },
    // 设置页size
    handleSizeChange(val) {
      this.$set(this.pageSet, 'pageSize', val)
      this.queryByKeyword()
    },
    // 添加或者修改
    addEdit(row) {
      this.findTreeRoot()
      this.dialogData.id = row ? row.id : null
      this.title = (row !== undefined) ? `修改岗位` : `新增岗位`
      if (row) {
        detailSub(row.id).then(response => {
          this.dialogData = response.data
          this.dialogFormVisible = true
        }).catch({})
      } else {
        this.dialogData = { isEnable: true, code: '' }
        this.dialogFormVisible = true
      }
    },
    openDialog() {
    },
    // 清空数据,刷新页面
    closeDialog(formName) {
      this.dialogData = {}
      this.$refs[formName].resetFields()
    },
    // 添加或编辑
    addOrUpdatePost(formName) {
      this.btnDisabled = true
      if (this.dialogData.id) {
        this.updatePost(formName)
      } else {
        this.addPost(formName)
      }
    },
    // 添加api
    addPost(formName) {
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          this.btnDisabled = false
          return false
        }
        addSub(this.dialogData).then(
          response => {
            if (response.code === 200) {
              this.$message({
                showClose: true,
                message: '提交成功!',
                type: 'success'
              })
            } else {
              this.$message({
                showClose: true,
                type: 'error',
                message: response.msg
              })
            }
            this.dialogFormVisible = false
            this.queryByKeyword()
            this.btnDisabled = false
          }
        ).catch({})
      })
    },
    // 修改api
    updatePost(formName) {
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          this.btnDisabled = false
          return false
        }
        updateSub(this.dialogData).then(
          response => {
            if (response.code === 200) {
              this.$message({
                showClose: true,
                message: '提交成功',
                type: 'success'
              })
            } else {
              this.$message({
                showClose: true,
                type: 'error',
                message: response.msg
              })
            }
            this.dialogFormVisible = false
            this.queryByKeyword()
            this.btnDisabled = false
          }
        ).catch({})
      })
    },
    // 删除弹框
    deletePost(id) {
      this.$confirm('你确定删除该条数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSub(id).then(reponse => {
          if (reponse.code === 200) {
            this.$message({
              showClose: true,
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
          this.dialogFormVisible = false
          this.queryByKeyword()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
