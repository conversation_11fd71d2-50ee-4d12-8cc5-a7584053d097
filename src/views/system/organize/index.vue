<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div>
      <el-form :inline="true" :model="formData" class="demo-form-inline">
        <!--        <el-form-item label="组织名称">-->
        <!--          <el-input v-model="formData.keyword" placeholder="请输入关键字"></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <!--          <el-button type="primary">查询</el-button>-->
          <el-button type="primary" @click="addEdit()">添加根组织</el-button>
          <el-button type="primary" @click="findTreeRoot()">刷新</el-button>
          <!-- <el-button type="primary" @click="testReload()">加载钉钉组织机构</el-button>
          <el-button type="primary" @click="testReloadUser()">加载钉钉用户</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :stripe="true"
        header-cell-class-name="table-header"
        lazy
        :load="load"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column
          label="序号"
          type="index"
          width="50"
        />
        <el-table-column
          prop="name"
          label="名称"
          width="200"
        />
        <el-table-column
          prop="code"
          label="机构编码"
          width="240"
        />
        <el-table-column
          prop="isEnable"
          label="状态"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnable === false" type="danger">禁用</el-tag>
            <el-tag v-else type="success">启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="nameEn"
          label="名称(英文)"
          width="150"
        />
        <el-table-column
          prop="nameSimple"
          label="简称"
          width="80"
        />
        <el-table-column
          prop="nameSimplePinyin"
          label="简称拼音"
          width="80"
        />
        <el-table-column
          prop="sort"
          label="排序"
          width="50"
        />
        <el-table-column
          prop="provinceName"
          label="省名称"
          width="100"
        />
        <el-table-column
          prop="cityName"
          label="市名称"
          width="100"
        />
        <el-table-column
          prop="countyName"
          label="县名称"
          width="150"
        />
        <el-table-column
          prop="description"
          label="描述"
          width="200"
        />
        <el-table-column
          prop="category"
          label="机构类别"
          width="80"
        >
          <template slot-scope="scope">
            {{ getCategoryText(scope) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="mobile"
          label="电话号码"
          width="100"
        />
        <el-table-column
          prop="fax"
          label="传真"
          width="150"
        />
        <el-table-column
          prop="address"
          label="详细地址"
          width="230"
        />
        <el-table-column
          prop="contactsName"
          label="部门联系人"
          width="100"
        />
        <el-table-column
          prop="leaderName"
          label="部门负责人"
          width="100"
        />
        <el-table-column
          prop="createBy"
          label="创建人"
          width="120"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime | parseDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateBy"
          label="更新人"
          width="120"
        />
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime | parseDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注"
          width="250"
        />
        <el-table-column
          fixed="right"
          label="操作"
          min-width="230"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="addEdit(scope.row)">添加下级组织</el-button>
            <el-button type="text" size="small" @click="addEdit(scope.row,true)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteOrg(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div>
      <el-dialog
        :title="title"
        :visible.sync="dialogFormVisible"
        :close-on-click-modal="false"
        width="1000px"
        @closed="closeDialog('currentOrgDataForm')"
        @open="openDialog"
      >
        <el-form
          ref="currentOrgDataForm"
          :model="dialogData"
          :label-width="'120px'"
          :rules="formRules"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="组织名称" prop="name">
                <el-input v-model="dialogData.name" autocomplete="off" placeholder="请输入组织名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="上级组织">
                <el-input v-model="dialogData.parentName" autocomplete="off" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="机构编码">
                <el-input v-model="dialogData.code" autocomplete="off" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="机构类别" prop="category">
                <el-select v-model="dialogData.category" placeholder="请选择" style="width: 100%" clearable>
                  <el-option v-for="(item) in categoryData" :key="item.itemValue" :label="item.itemText" :value="item.itemValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="名称(英文)" prop="nameEn">
                <el-input v-model="dialogData.nameEn" autocomplete="off" placeholder="请输入名称(英文)" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="状态">
                <el-switch
                  v-model="dialogData.isEnable"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="简称" prop="nameSimple">
                <el-input v-model="dialogData.nameSimple" autocomplete="off" placeholder="请输入略写" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="简称拼音" prop="nameSimplePinyin">
                <el-input v-model="dialogData.nameSimplePinyin" autocomplete="off" placeholder="请输入简称拼音" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="电话号码" prop="mobile">
                <el-input v-model="dialogData.mobile" autocomplete="off" placeholder="请输入电话号码" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="传真" prop="fax">
                <el-input v-model="dialogData.fax" autocomplete="off" placeholder="请输入传真" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="所属地区" prop="selectedKeys">
                <el-cascader
                  :key="modelKey"
                  v-model="dialogData.selectedKeys"
                  :props="areaProps"
                  style="width: 100%"
                  clearable
                  placeholder="请选择所属地区"
                  @change="areaChange()"
                />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="详细地址" prop="address">
                <el-input v-model="dialogData.address" autocomplete="off" placeholder="请输入详细地址" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="部门联系人" prop="contactsName">
                <el-input v-model="dialogData.contactsName" autocomplete="off" placeholder="请选择部门联系人" style="width: 75%" disabled clearable />
                <el-button style="width: 24%" type="primary" icon="el-icon-search" @click="queryUserList(1)">搜索</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="部门负责人" prop="leaderName">
                <el-input v-model="dialogData.leaderName" autocomplete="off" placeholder="请选择部门负责人" style="width: 75%" disabled clearable />
                <el-button type="primary" style="width: 24%" icon="el-icon-search" @click="queryUserList(2)">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="排序" prop="sort">
                <el-input-number
                  v-model="dialogData.sort"
                  controls-position="right"
                  :min="1"
                  :max="100"
                  placeholder="请输入排序"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col v-if="isShow" :span="11">
              <el-form-item label="成立时间">
                <el-date-picker
                  v-model="dialogData.establishDate"
                  type="date"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  style="width: 100%"
                  value-format="timestamp"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShow">
            <el-col :span="11">
              <el-form-item label="法人姓名">
                <el-input v-model="dialogData.legalPersonName" autocomplete="off" placeholder="请输入法人姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="法人身份证号">
                <el-input v-model="dialogData.legalPersonIdCard" autocomplete="off" placeholder="请输入身份证号" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShow">
            <el-col :span="11">
              <el-form-item label="住所">
                <el-input v-model="dialogData.residence" autocomplete="off" placeholder="请输入住所" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="办公地址">
                <el-input v-model="dialogData.officeAddress" autocomplete="off" placeholder="请输入办公地址" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShow">
            <el-col :span="11">
              <el-form-item label="营业期限">
                <el-input-number
                  v-model="dialogData.businessTerm"
                  controls-position="right"
                  :precision="2"
                  :min="0"
                  placeholder="请输入营业期限"
                  style="width: 100%"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="注册资本">
                <el-input-number
                  v-model="dialogData.registeredCapital"
                  controls-position="right"
                  :precision="2"
                  :min="0"
                  placeholder="请输入营业期限"
                  style="width: 85%"
                  clearable
                />
                <span style="margin-left: 5%">万元</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShow">
            <el-col :span="22">
              <el-form-item label="经营范围">
                <el-input v-model="dialogData.businessRange" autocomplete="off" type="textarea" placeholder="请输入经营范围" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="isShow">
            <el-col :span="22">
              <el-form-item label="工作范围">
                <el-input v-model="dialogData.workRange" autocomplete="off" type="textarea" placeholder="请输入工作范围" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="描述">
                <el-input v-model="dialogData.description" autocomplete="off" type="textarea" placeholder="请输入描述" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label="备注">
                <el-input v-model="dialogData.remarks" autocomplete="off" type="textarea" placeholder="请输入备注" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" :disabled="btnDisabled" @click="addOrUpdateOrg('currentOrgDataForm')">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <div>
      <el-drawer
        :title="drawerTitle"
        :visible.sync="drawerVisible"
        :direction="direction"
        :close-on-press-escape="false"
        size="30%"
        @closed="handleClose"
      >
        <el-container>
          <el-header>
            <el-form :inline="true" :model="userQuery">
              <el-form-item label="登录名">
                <el-input v-model="userQuery.loginName" placeholder="登录名" style="width: 120px" clearable />
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="userQuery.name" placeholder="姓名" style="width: 120px" clearable />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onSubmit">查询</el-button>
              </el-form-item>
            </el-form>
          </el-header>
          <el-main>
            <el-table
              :data="userTableData"
              border
              style="width: 100%"
              tooltip-effect="light"
              highlight-current-row
              @current-change="handleCurrentChange"
            >
              <el-table-column label="" width="30" fixed>
                <template slot-scope="scope">
                  <el-radio
                    v-model="radio"
                    :label="scope.$index"
                    @change.native="getCurrentRow(scope.$index)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="id"
                width="50"
                label="编号"
              />
              <el-table-column
                prop="loginName"
                width="120"
                label="登录名"
              />
              <el-table-column
                prop="no"
                width="120"
                label="工号"
              />
              <el-table-column
                prop="name"
                width="120"
                label="姓名"
              />
              <el-table-column
                prop="sex"
                width="50"
                label="性别"
                :formatter="formatterSex"
              />
            </el-table>
            <div class="block">
              <pagination
                :limit.sync="userQuery.pageSize"
                :page.sync="userQuery.currentPage"
                :total="pageTotal"
                @pagination="onSubmit"
              />
            </div>
          </el-main>
        </el-container>
        <div class="float-button">
          <el-button type="primary" @click="drawerVisible = false">取 消</el-button>
          <el-button type="primary" @click="checkUser">确 定</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  addOrgSub,
  deleteOrgSub,
  detailOrgSub,
  findTreeById,
  testReload,
  testReloadUser,
  updateOrgSub
} from '@/api/organize'
import { findByCodeValue } from '@/api/dictValue'
import { list } from '@/api/area'
import { userList } from '@/api/user'

export default {
  name: 'Organize',
  components: {},
  props: {},
  data() {
    return {
      btnDisabled: false,
      formData: {
        keyword: null
      },
      tableData: [],
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      dialogTableVisible: false,
      dialogFormVisible: false,
      dialogData: {},
      formLabelWidth: '120px',
      categoryData: [],
      title: '',
      modelKey: 0,
      areaProps: {
        value: 'code',
        label: 'name',
        lazy: true,
        async lazyLoad(node, resolve) {
          // 获取当前node对象中的level属性
          const { level } = node
          list({
            code: node.data == null ? 'CN' : node.data.code,
            type: node.data == null ? 2 : node.data.type + 1,
            isDelete: 0
          }).then(reponse => {
            if (reponse.code === 200) {
              const nodes = reponse.data
              if (level > 0) {
                nodes.forEach(item => {
                  item.leaf = level >= 2 // 判断是否为末尾节点，这个地方是0,1,2,3四级
                })
              }
              resolve(nodes)
            } else {
              // console.log(reponse)
            }
          }).catch({})
        }
      },
      formRules: {
        name: [{ required: true, message: '请输入组织名称', trigger: 'change' }],
        nameEn: [{ required: true, message: '请输入名称(英文)', trigger: 'change' }],
        nameSimple: [{ required: true, message: '请输入简称', trigger: 'change' }],
        mobile: [{ required: true, message: '请输入手机号码', trigger: 'change' }],
        leaderName: [{ required: true, message: '请输入部门负责人', trigger: 'change' }],
        contactsName: [{ required: true, message: '请输入部门联系人', trigger: 'change' }],
        fax: [{ required: true, message: '请输入传真', trigger: 'change' }],
        category: [{ required: true, message: '请输入机构类别', trigger: 'change' }],
        selectedKeys: [{ type: 'array', required: true, message: '请输入归属地区', trigger: 'change' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'change' }],
        nameSimplePinyin: [{ required: true, message: '请输入简称拼音', trigger: 'change' }]
      },
      parentOrgId: null,
      // 抽屉数据
      chooseData: {},
      drawerVisible: false,
      direction: 'rtl',
      drawerTitle: null,
      userQuery: {
        loginName: null,
        name: null,
        currentPage: 1,
        pageSize: 10
      },
      userTableData: null,
      pageTotal: 0,
      radio: false,
      currentRow: null,
      isShow: null
    }
  },
  watch: {
    dialogData: {
      handler(val) {
        console.log(val)
        if (val.contactsName) {
          this.handleSelectChange('currentOrgDataForm', 'contactsName')
        }
        if (val.leaderName) {
          this.handleSelectChange('currentOrgDataForm', 'leaderName')
        }
      },
      deep: true
    }
  },
  created() {
  },
  mounted() {
    this.getCategorys()
    this.findTreeRoot()
  },
  methods: {
    // 选中，清除提示
    handleSelectChange(ref, prop) {
      if (this.$refs[ref]) {
        this.$refs[ref].clearValidate(prop)
      }
    },
    testReload() {
      testReload().then(response => {
      }).catch({})
    },
    testReloadUser() {
      testReloadUser().then(response => {
      }).catch({})
    },
    // 获取所有根节点
    findTreeRoot() {
      findTreeById(0).then(response => {
        this.tableData = response.data
      }).catch({})
    },
    // 子节点懒加载
    load(tree, treeNode, resolve) {
      const parentId = tree.id
      findTreeById(parentId).then(response => {
        resolve(response.data)
      })
    },
    // 添加或者修改
    addEdit(row, flag) {
      this.dialogData = { isEnable: true }
      this.dialogData.id = (row && flag) ? row.id : null
      this.dialogData.parentName = (row && flag === undefined) ? row.name : null
      this.dialogData.parentId = (row && flag === undefined) ? row.id : (row ? null : 0)
      this.title = (row !== undefined && flag) ? `修改组织` : `新增组织`
      this.parentOrgId = (row && flag === undefined) ? row.id : (row ? row.parentId : 0)
      this.isShow = this.parentOrgId === 0
      if (row && flag) {
        detailOrgSub(row.id).then(response => {
          this.dialogData = response.data
          this.dialogData.category = this.dialogData.category ? this.dialogData.category.toString() : null
          this.dialogData.selectedKeys = [
            this.dialogData.provinceCode,
            this.dialogData.cityCode,
            this.dialogData.countyCode
          ]
          this.modelKey++
          this.$nextTick(() => {
            this.dialogFormVisible = true
          })
        }).catch({})
      } else {
        this.$nextTick(() => {
          this.dialogData.code = ''
          this.dialogData.selectedKeys = ['', '', '']
          this.modelKey++
          this.dialogFormVisible = true
        })
      }
    },
    // Dialog打开初始化数据
    openDialog() {
      this.getCategorys()
    },
    // 清空数据
    closeDialog(formName) {
      this.dialogData = {}
      this.$refs[formName].resetFields()
    },
    // 获取机构类别
    getCategorys() {
      var code = 'categoryType'
      findByCodeValue({ code }).then(response => {
        this.categoryData = response.data
      }).catch({})
    },
    // 获取省市区
    areaChange() {
      var data = this.dialogData.selectedKeys
      if (data === undefined) {
        return
      }
      if (data.length === 1) {
        this.dialogData.provinceCode = data[0]
      } else if (data.length === 2) {
        this.dialogData.provinceCode = data[0]
        this.dialogData.cityCode = data[1]
      } else if (data.length === 3) {
        this.dialogData.provinceCode = data[0]
        this.dialogData.cityCode = data[1]
        this.dialogData.countyCode = data[2]
      }
      // console.log(this.dialogData.selectedKeys)
    },
    // 添加或编辑
    addOrUpdateOrg(formName) {
      this.btnDisabled = true
      if (this.dialogData.id) {
        this.updateOrg(formName)
      } else {
        this.addOrg(formName)
      }
    },
    // 添加api
    addOrg(formName) {
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          this.btnDisabled = false
          return false
        }

        addOrgSub(this.dialogData).then(
          response => {
            if (response.code === 200) {
              if (this.parentOrgId === 0) {
                this.findTreeRoot()
              } else {
                this.refreshRow(this.parentOrgId)
              }
              this.$message({
                showClose: true,
                message: '提交成功!',
                type: 'success'
              })
            } else {
              this.$message({
                showClose: true,
                type: 'error',
                message: response.msg
              })
            }
            this.dialogFormVisible = false
            this.dialogData = {}
            this.btnDisabled = false
          }
        ).catch({})
      })
    },
    // 修改api
    updateOrg(formName) {
      this.$refs[formName].validate(valid => {
        // 校验
        if (!valid) {
          this.btnDisabled = false
          return false
        }
        updateOrgSub(this.dialogData).then(
          response => {
            if (response.code === 200) {
              if (this.parentOrgId === 0) {
                this.findTreeRoot()
              } else {
                this.refreshRow(this.parentOrgId)
              }
              this.$message({
                showClose: true,
                message: '提交成功',
                type: 'success'
              })
            } else {
              this.$message({
                showClose: true,
                type: 'error',
                message: response.msg
              })
            }
            this.dialogFormVisible = false
            this.dialogData = {}
            this.btnDisabled = false
          }
        ).catch({})
      })
    },
    // 删除弹框
    deleteOrg(row) {
      this.$confirm('你确定删除该条数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrgSub(row.id).then(reponse => {
          if (reponse.code === 200) {
            if (row.parentId === 0) {
              this.findTreeRoot()
            } else {
              this.refreshRow(row.parentId)
            }
            this.$message({
              showClose: true,
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 刷新节点
    refreshRow(id) {
      findTreeById(id).then(res => {
        if (res.code === 200) {
          this.$set(this.$refs['multipleTable'].store.states.lazyTreeNodeMap, id, res.data)
        }
      })
    },
    // table机构类别转换
    getCategoryText(scope) {
      return scope.row.category ? this.categoryData.find(function(item) {
        return (scope.row.category !== null && (item.itemValue === scope.row.category.toString()))
      })['itemText'] : ''
    },
    // 查询用户列表，每次数据改变重新判断单选状态
    queryUserList(type) {
      if (type === 1) {
        this.drawerTitle = '联系人选择'
        this.chooseData.id = this.dialogData.contacts
      } else if (type === 2) {
        this.drawerTitle = '负责人选择'
        this.chooseData.id = this.dialogData.leader
      }
      this.chooseData.type = type
      this.onSubmit()
    },
    // 设置单选框选择状态
    getInitChoose() {
      console.log(this.chooseData)
      if (this.chooseData) {
        const index = this.userTableData.findIndex(
          item => item.id === this.chooseData.id
        )
        if (index > -1) {
          this.radio = index
        } else {
          this.radio = false
        }
      } else {
        this.radio = false
      }
    },
    // 查询用户列表
    onSubmit() {
      userList(this.userQuery).then(
        response => {
          this.userTableData = response.data.list
          this.pageTotal = response.data.total
          this.userQuery.pageIndex = response.data.pageIndex
          this.getInitChoose()
          this.drawerVisible = true
        }
      )
    },
    // // 由于翻页后重新获取列表了，需要把选择框选中状态取消
    // changePage(val) {
    //   this.userQuery.currentPage = val
    //   this.radio = false
    //   this.onSubmit()
    // },
    /* current-change    当表格的当前行发生变化的时候会触发该事件，如果要高亮当前行，请打开表格的 highlight-current-row 属性    currentRow, oldCurrentRow */
    // 保证点击当前行的任意位置不止是单选框也能够选择
    handleCurrentChange(val) {
      if (val) {
        this.currentRow = val
        const index = this.userTableData.findIndex(
          item => item.id === this.currentRow.id
        )
        if (index > -1) {
          this.radio = index
        }
        this.$emit('data', val.pkg)
      }
    },
    getCurrentRow(index) {
      this.radio = index
    },
    formatterSex(row) {
      return row.sex === 1 ? '男' : '女'
    },
    handleClose() {
      this.chooseData = {}
      this.drawerTitle = null
      this.userQuery = { name: null, currentPage: 1, pageSize: 10, loginName: null }
      this.userTableData = null
      this.pageTotal = 0
      this.radio = false
      this.currentRow = null
    },
    checkUser() {
      // console.log(this.currentRow)
      // console.log(this.chooseData)
      if (this.currentRow) {
        if (this.chooseData.type === 1) {
          this.dialogData.contacts = this.currentRow.id
          this.dialogData.contactsName = this.currentRow.name
        } else if (this.chooseData.type === 2) {
          this.dialogData.leader = this.currentRow.id
          this.dialogData.leaderName = this.currentRow.name
        }
      }
      this.$nextTick(() => {
        this.drawerVisible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep :focus {
  outline: none;
}

.float-button {
  position: absolute;
  right: 20px;
  bottom: 20px;
}
</style>
