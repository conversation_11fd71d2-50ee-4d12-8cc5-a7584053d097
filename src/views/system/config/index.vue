<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div>
      <el-form :inline="true" :model="formData" class="demo-form-inline">
        <el-form-item label="配置名称">
          <el-input v-model="formData.name" placeholder="请输入关键字" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchConf()">查询</el-button>
          <el-button type="primary" @click="addEdit()">添加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--    列表-->
    <div>
      <el-table
        v-loading="isLoading"
        border
        :data="tableData"
        :stripe="true"
        style="width: 100%"
      >
        <el-table-column
          fixed
          prop="id"
          label="编号"
          width="80"
        />
        <el-table-column
          prop="name"
          label="名称"
          width="120"
        />
        <el-table-column
          prop="code"
          label="编码"
          width="120"
        />
        <el-table-column
          label="启用"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnable" type="success">
              启用
            </el-tag>
            <el-tag v-else type="info">
              禁用
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="启用状态是否可编辑"
          width="150"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.enableEdit === 0" type="info">
              不可编辑
            </el-tag>
            <el-tag v-else-if="scope.row.enableEdit === 1" type="success">
              可编辑
            </el-tag>
            <el-tag v-else-if="scope.row.enableEdit === 2" type="warning">
              不显示
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createBy"
          label="创建人"
          width="120"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime | parseDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateBy"
          label="更新人"
          width="120"
        />
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime | parseDate }}
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          label="操作"
          min-width="230"
        >
          //嵌套在表格内
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="addEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteConf(scope.row.code)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--    分页-->
    <div class="block">
      <el-pagination
        :current-page="pageSet.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSet.pageSize"
        :total="pageSet.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <!--    添加或者编辑-->
    <div>
      <el-dialog
        :title="title"
        :visible.sync="dialogFormVisible"
        @close="closeDialog"
      >
        <el-form
          ref="formName"
          :model="dialogData"
          :label-width="'150px'"
          :rules="rules"
        >
          <el-form-item label="配置编码" prop="code">
            <el-input v-model="dialogData.code" :disabled="dialogData.disabled" clearable />
          </el-form-item>
          <el-form-item label="配置名称" prop="name">
            <el-input v-model="dialogData.name" placeholder="请输入配置名称" clearable />
          </el-form-item>
          <el-form-item label="配置项值" prop="value">
            <el-input v-model="dialogData.value" clearable placeholder="请输入配置项值" />
          </el-form-item>
          <el-form-item label="是否启用" prop="isEnable">
            <el-select v-model="dialogData.isEnable" placeholder="请选择">
              <el-option
                v-for="(item,index) in statusData"
                :key="index"
                :label="item.key"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="启用状态是否可编辑" prop="enableEdit">
            <el-select v-model="dialogData.enableEdit" placeholder="请选择">
              <el-option
                v-for="(item,index) in enableEdit"
                :key="index"
                :label="item.key"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="addConf('formName')">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { searchConf, saveConf, deleteConf, updateConf } from '@/api/config'

export default {
  name: 'Config',
  props: {},
  data() {
    return {
      tableData: [],
      formData: {
        name: null,
        code: null
      },
      pageSet: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      dialogTableVisible: false,
      dialogFormVisible: false,
      dialogData: {
        id: null,
        name: null,
        code: null,
        isEnable: true,
        value: null,
        enableEdit: 1,
        disabled: false
      },
      statusData: [
        {
          key: '是',
          value: true
        },
        {
          key: '否',
          value: false
        }
      ],
      enableEdit: [
        {
          key: '不可编辑',
          value: 0
        },
        {
          key: '可编辑',
          value: 1
        },
        {
          key: '不显示',
          value: 2
        }
      ],
      formLabelWidth: '120px',
      title: '',
      isLoading: false,
      rules: {
        code: [{
          required: true, message: '请输入配置编码', trigger: 'change'
        }],
        name: [{
          required: true, message: '请输入配置名称', trigger: 'change'
        }],
        value: [{
          required: true, message: '请输入配置项值', trigger: 'change'
        }],
        isEnable: [{
          required: true, message: '请输入配置项值', trigger: 'change'
        }],
        enableEdit: [{
          required: true, message: '请输入配置项值', trigger: 'change'
        }]
      }
    }
  },
  created() {
    this.searchConf()
  },
  mounted() {
    this.searchConf()
  },
  methods: {
    searchConf() {
      this.isLoading = true
      searchConf({
        ...this.pageSet,
        ...this.formData
      }).then(response => {
        // console.log(response.data)
        this.tableData = response.data.list
        this.pageSet.total = response.data.total
        this.pageSet.currentPage = response.data.pageIndex
        this.pageSet.pageSize = response.data.pageSize
      }).catch({})
      this.isLoading = false
    },
    // 跳页
    handleCurrentChange(val) {
      this.$set(this.pageSet, 'currentPage', val)
      this.searchConf()
    },
    // 设置页size
    handleSizeChange(val) {
      this.$set(this.pageSet, 'pageSize', val)
      this.searchConf()
    },
    addEdit(rowData) {
      if (rowData) {
        this.dialogData.code = rowData.code
        this.dialogData.name = rowData.name
        this.dialogData.isEnable = rowData.isEnable
        this.dialogData.value = rowData.value
        this.dialogData.enableEdit = rowData.enableEdit
        this.dialogData.id = rowData.id
        this.dialogData.disabled = true
        this.dialogFormVisible = true
      } else {
        this.dialogFormVisible = true
      }
      // this.dialogTableVisible= true
    },
    addConf(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogData.id) {
            updateConf(this.dialogData).then(
              response => {
                if (response.status === 200) {
                  this.$message({
                    showClose: true,
                    message: '提交成功',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    showClose: true,
                    type: 'error',
                    message: response.msg
                  })
                }
              }).catch({})
          } else {
            saveConf(this.dialogData).then(
              response => {
                if (response.status === 200) {
                  this.$message({
                    showClose: true,
                    message: '提交成功',
                    type: 'success'
                  })
                } else {
                  this.$message({
                    showClose: true,
                    type: 'error',
                    message: response.msg
                  })
                }
              }).catch({})
          }
          this.dialogFormVisible = false
          this.searchConf()
        }
      })
    },
    closeDialog() {
      this.dialogData = {
        id: null,
        name: null,
        code: null,
        isEnable: true,
        value: null,
        enableEdit: 1,
        disabled: false
      }
    },
    deleteConf(code) {
      this.$confirm('你确定删除该条数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteConf(code).then(reponse => {
          if (reponse.code === 200) {
            this.$message({
              showClose: true,
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
          this.searchConf()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
