<template>
  <div class="app-container">
    <el-row :gutter="15">
      <el-col :span="6">
        <el-card body-style="height:76vh;overflow:auto;">
          <slot v-for="item in topOrganizeList">
            <el-tree
              :ref="item.id"
              empty-text="当前无组织数据"
              node-key="id"
              :highlight-current="true"
              :accordion="true"
              :data="item"
              :props="defaultProps"
              :load="loadNode"
              lazy
              @node-click="handNodeClick"
            >
              <!--              <span slot-scope="{ node, data }" class="flex-adapter" @click="handNodeClick(node, data)">-->
              <!--                <span style="font-size: 12px;">{{ node.label }}</span>-->
              <!--              </span>-->
            </el-tree>
          </slot>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="infinite-list-wrapper" body-style="padding: 5px 0px 0px 20px;">
          <div slot="header">
            <span>当前组织：{{ currentOrganize == null ? '' : currentOrganize.name }}</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="searchUser">查找</el-button>
            <el-input
              v-model="searchOrganizeUser"
              prefix-icon="el-icon-search"
              style="float: right; width: 300px;margin-right: 10px;"
              placeholder="请输入搜索的用户名"
              @keyup.enter.native="searchUser"
            />
          </div>
          <div slot="default" style="padding:0 0 5px 5px;overflow:auto;height:70vh;">
            <ul
              v-infinite-scroll="loadPageData"
              infinite-scroll-disabled="disabled"
              class="infinite-list"
              style="padding:0 0 5px 5px;margin: 0 10px 10px 0;background-color: #ffffff;"
            >
              <li v-for="(listItem, listIndex) in organizeUserInfo.list" :key="listIndex" class="infinite-list-item">
                <el-row>
                  <el-col v-for="(item, index) in listItem" :key="item.id" style="margin-top:10px;" :offset="index%3 === 0 ? 0 : 1" :span="7">
                    <el-card shadow="hover" style="border:1px solid lightgray;" body-style="background-color:	#ffffff;">
                      <el-row>
                        <el-col :span="10">
                          <el-image
                            style="border-radius: 50%"
                            :src="defaultImage"
                          >
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline" />
                            </div>
                          </el-image>
                        </el-col>
                        <el-col :span="12" :push="1">
                          <el-row style="margin-top: 15px;">
                            <el-col>
                              <span>姓名：{{ item.name }}</span>
                            </el-col>
                          </el-row>
                          <el-row style="margin-top: 10px;">
                            <el-col>
                              <span>手机号：{{ item.phone }}</span>
                            </el-col>
                          </el-row>
                        </el-col>
                      </el-row>
                      <el-row style="margin-top: 10px;">
                        <el-col :push="2">
                          <span>邮箱：{{ item.email === null ? '<EMAIL>' : item.email }}</span>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :push="2">
                          <span>传真：{{ item.mobile }}</span>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :push="2">
                          <span>工号：{{ item.no }}</span>
                        </el-col>
                      </el-row>
                    </el-card>
                  </el-col>
                </el-row>
              </li>
            </ul>
            <p v-if="loading" style="text-align: center;">加载中...</p>
            <p v-if="lockStatus" style="text-align: center;">没有更多了</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { findTreeById, getCurrentUserTopOrganize } from '@/api/organize'
import { getUserListByOrganizeId } from '@/api/user'
import defaultImg from '@/assets/defualt_avatar.jpeg'

const defaultInfo = {
  pageIndex: 1,
  pageSize: 6,
  total: 0,
  list: []
}

export default {
  name: 'AddressBook',
  data() {
    return {
      defaultProps: {
        label: 'name',
        code: 'id'
      },
      topOrganizeList: [],
      lockStatus: false,
      loading: false,
      currentOrganize: null,
      searchOrganizeUser: null,
      totalCount: 0,
      organizeUserInfo: defaultInfo
    }
  },
  computed: {
    disabled() {
      return this.loading || this.lockStatus
    },
    defaultImage() {
      return defaultImg
    }
  },
  mounted() {
    getCurrentUserTopOrganize().then(resp => {
      resp.data.forEach(value => {
        const topOrganize = [value]
        this.topOrganizeList.push(topOrganize)
      })
    }).catch(exception => {
      console.log(exception)
    })
  },
  methods: {
    loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve(node.data)
      }
      // 树节点懒加载
      findTreeById(node.data == null ? 0 : node.data.id).then(resp => {
        console.log(resp.data)
        resolve(resp.data)
      }).catch(exception => {
        console.log(exception)
      })
    },
    handNodeClick(data, node) {
      this.searchOrganizeUser = null
      this.currentOrganize = null
      this.lockStatus = false
      if (data == null || data.id == null) {
        this.lockStatus = true
        return
      }
      this.currentOrganize = data
      this.searchUser()
    },
    searchUser() {
      if (this.currentOrganize == null || this.currentOrganize.id == null) {
        return
      }
      this.totalCount = 0
      this.organizeUserInfo = defaultInfo
      // 默认加载当前组织下9个用户信息
      getUserListByOrganizeId({
        organizeId: this.currentOrganize.id,
        pageIndex: 1,
        pageSize: this.organizeUserInfo.pageSize,
        name: this.searchOrganizeUser
      }).then(resp => {
        if (resp.data.list === undefined || resp.data.list.length <= 0) {
          this.lockStatus = true
          return
        }
        this.organizeUserInfo = resp.data
        this.totalCount += resp.data.list.length
        this.organizeUserInfo.list = this.getTotal3Group(resp.data.list)
        this.lockStatus = Number(this.organizeUserInfo.total) === Number(this.totalCount)
      }).catch(exception => {
        console.log(exception)
      })
    },
    loadPageData() {
      if (this.lockStatus) {
        return
      }
      if (this.currentOrganize == null || this.currentOrganize.id == null) {
        return
      }
      this.loading = true
      getUserListByOrganizeId({
        organizeId: this.currentOrganize.id,
        pageIndex: this.organizeUserInfo.pageIndex + 1,
        pageSize: this.organizeUserInfo.pageSize,
        name: this.searchOrganizeUser
      }).then(resp => {
        if (resp.data.list === undefined || resp.data.list.length <= 0) {
          this.lockStatus = true
          this.loading = false
          return
        }
        this.organizeUserInfo.list = this.addTotal3Group(resp.data.list)
        this.organizeUserInfo.total = resp.data.total
        this.organizeUserInfo.pageIndex = resp.data.pageIndex
        this.organizeUserInfo.pageSize = resp.data.pageSize
        this.totalCount += resp.data.list.length
        this.lockStatus = Number(this.organizeUserInfo.total) === Number(this.totalCount)
        this.loading = false
      }).catch(exception => {
        console.log(exception)
      })
    },
    addTotal3Group(data) {
      if (this.totalCount % 3 === 0) {
        this.organizeUserInfo.list = this.organizeUserInfo.list.concat(this.getTotal3Group(data))
        return this.organizeUserInfo.list
      }
      const remainder = this.totalCount % 3
      const lastArr = this.organizeUserInfo.list[this.organizeUserInfo.list.length - 1]
      if (data.length <= remainder) {
        for (let i = 0; i < data.length; i++) {
          lastArr.push(data[i])
        }
        return this.organizeUserInfo.list
      }
      for (let i = 0; i < remainder; i++) {
        lastArr.push(data[i])
      }
      this.organizeUserInfo.list = this.organizeUserInfo.list.concat(this.getTotal3Group(data).slice(0, remainder))
      return this.organizeUserInfo.list
    },
    getTotal3Group(data) {
      const total3Group = []
      let total3GroupMember = []
      for (let i = 0; i < data.length; i++) {
        if (i !== 0 && i % 3 === 0) {
          total3Group.push(total3GroupMember)
          total3GroupMember = []
        }
        total3GroupMember.push(data[i])
      }
      total3Group.push(total3GroupMember)
      return total3Group
    }
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 5px;
}
</style>
