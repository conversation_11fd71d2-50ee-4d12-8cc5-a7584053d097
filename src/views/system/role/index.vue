<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="search-view">
        <el-form :inline="true" :model="formInline" label-suffix="：">
          <el-form-item label="名称">
            <el-input v-model="formInline.name" placeholder="名称" />
          </el-form-item>
          <el-form-item label="编码">
            <el-input v-model="formInline.code" placeholder="编码" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="querySubmit">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-button type="primary" @click="add">新增</el-button>
    <el-table
      :data="tableData"
      style="width: 100%; margin-top: 20px;"
      border
    >
      <el-table-column
        prop="name"
        label="名称"
      />
      <el-table-column
        prop="code"
        label="编码"
      />
      <el-table-column
        prop="description"
        label="描述"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="userBuild(scope.row, 1)"
          >用户</el-button>
          <el-button
            type="text"
            @click="treeBuild(scope.row)"
          >授权</el-button>
          <el-button
            type="text"
            @click="get(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            @click="remove(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pageSet.pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSet.pageSize"
      :total="pageSet.total"
      layout="total, sizes, prev, pager, next, jumper"
      background
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <el-dialog :visible.sync="formDialog.dialogFormVisible" :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-name">{{ formDialog.title }}</span>
      </div>
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="150px">
        <el-form-item prop="code" label="编码">
          <el-input v-model="form.code" :disabled="formDialog.model === 'edit'" />
        </el-form-item>
        <el-form-item prop="name" label="名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item prop="name" label="描述">
          <el-input v-model="form.description" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialog.dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit('ruleForm')">{{ formDialog.buttonName }}</el-button>
      </div>
    </el-dialog>
    <el-drawer
      title="角色赋权"
      :visible.sync="treeDialog.dialogFormVisible"
      direction="rtl"
      size="50%"
    >
      <div>
        <el-scrollbar style="height:100%">
          <el-tree
            ref="tree"
            style="height: 600px; overflow-y: auto;"
            :data="menuData"
            show-checkbox
            node-key="id"
            :props="defaultProps"
            :check-strictly="checkStrictly"
            default-expand-all
            :expand-on-click-node="false"
            @check="handleCheck"
            @check-change="handleCheckChange"
          >
            <span slot-scope="{ node }" class="custom-tree-node">
              <span>
                {{ node.label }}
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
        <el-drawer
          title="按钮列表"
          :append-to-body="true"
          :visible.sync="innerButtonDrawer"
          size="30%"
        >
          <el-table
            ref="buttonCheckTable"
            :data="buttonData"
            @select="handleSelectionButtonChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column property="name" label="名称" />
          </el-table>
        </el-drawer>
        <div id="footerRule" slot="footer" class="dialog-footer">
          <el-button @click="treeDialog.dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="treeSubmit">{{ treeDialog.buttonName }}</el-button>
        </div>
      </div>
    </el-drawer>
    <el-drawer
      title="用户角色授权"
      :visible.sync="dialogUserFormVisible"
      direction="rtl"
      size="50%"
    >
      <div>
        <el-button type="primary" style="margin: 10px 10px;" @click="selectUserInfo(20,2,'角色授权')">添加用户</el-button>
        <el-table
          :data="roleUserData"
          @select="handleSelectionChange"
        >
          <el-table-column property="name" label="姓名" />
          <el-table-column property="no" label="工号" />
          <el-table-column property="phone" label="联系电话" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="removeUser(scope.row)"
              >移除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :limit.sync="queryParamsPageSet.pageSize"
          :page.sync="queryParamsPageSet.pageIndex"
          :total="queryParamsPageSet.total"
          @pagination="userBuild({ 'id': queryParams.roleId}, queryParamsPageSet.pageIndex)"
        />
      </div>
    </el-drawer>
    <el-dialog v-if="searchUsersDialogVisible" :title="userSelectModalName" :close-on-click-modal="false" append-to-body :visible.sync="searchUsersDialogVisible">
      <users-select-list
        ref="userSelectList"
        :limit="isSelectUserOne"
        :modal-name="userSelectModalName"
        :dialog-data="dialogData"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="searchUsersDialogVisible=false">取 消</el-button>
        <el-button type="primary" @click="checkSelectUserListData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { builder, builderUser, del, findPage, get, insert, removerUser, update } from '@/api/role'
import { findTree } from '@/api/menu'
import { findRuleList } from '@/api/rule'
import { findButtonList } from '@/api/button'
import { userListByRole } from '@/api/user'
import UsersSelectList from '@/components/Users/<USER>'

export default {
  components: {
    UsersSelectList
  },
  data() {
    return {
      innerDrawer: false,
      innerButtonDrawer: false,
      ruleData: [],
      buttonData: [],
      roleUserData: [],
      ruleForm: {},
      buttonForm: {},
      dialogUserFormVisible: false,
      formDialog: {
        title: '',
        dialogFormVisible: false,
        buttonName: '',
        model: ''
      },
      treeDialog: {
        title: '',
        dialogFormVisible: false,
        buttonName: '',
        ids: [],
        permissionList: []
      },
      buildDialog: {
        title: '',
        dialogFormVisible: false,
        buttonName: '',
        ids: []
      },
      tableData: [],
      formInline: {
        name: '',
        code: ''
      },
      form: {
        id: null,
        name: '',
        code: ''
      },
      rules: {
        code: [
          { required: true, message: '请输入编码', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ]
      },
      buildUserForm: {},
      buildForm: {
        id: null,
        ids: [],
        ruleIds: [],
        buttonIds: []
      },
      menuData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      menuChecks: [],
      queryParams: {
        roleId: null
      },
      queryParamsPageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      optionType: null,
      dialogData: [],
      isSelectUserOne: false,
      userSelectModalName: null,
      searchUsersDialogVisible: false,
      checkStrictly: true
    }
  },
  mounted() {
    this.querySubmit()
  },
  methods: {
    typeSourceFormat(cellValue) {
      if (cellValue === 1) {
        return '菜单'
      }
      if (cellValue === 2) {
        return '数据'
      }
      if (cellValue === 3) {
        return '按钮'
      }
      return cellValue
    },
    querySubmit() {
      findPage({
        ...this.formInline,
        ...this.pageSet }).then(response => {
        this.tableData = response.data.list
        this.pageSet.total = response.data.total
        this.pageSet.pageIndex = response.data.pageIndex
        this.pageSet.pageSize = response.data.pageSize
      })
    },
    add() {
      this.formDialog.title = '角色新增'
      this.formDialog.buttonName = '确定'
      this.formDialog.dialogFormVisible = true
      this.formDialog.model = 'new'
      this.form = {}
    },
    remove(data) {
      this.$confirm('此操作将删除该角色, 是否继续?', '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del(data).then(response => {
          this.$message({
            message: '角色删除成功',
            type: 'success'
          })
          this.querySubmit()
        })
      })
    },
    removeUser(data) {
      this.$confirm('此操作将移除用户与角色的关系, 是否继续?', '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.buildUserForm.userIds.push(data.id)
        removerUser(this.buildUserForm).then(response => {
          this.$message({
            message: '用户移除成功',
            type: 'success'
          })
          userListByRole({
            ...this.queryParams,
            ...this.queryParamsPageSet
          }).then(
            response => {
              if (response.data) {
                this.roleUserData = response.data.list
                this.queryParamsPageSet.total = response.data.total
                this.queryParamsPageSet.pageIndex = response.data.pageIndex
                this.queryParamsPageSet.pageSize = response.data.pageSize
              }
            }
          )
        })
      })
    },
    userBuild(data, pageIndex) {
      this.dialogUserFormVisible = true
      this.buildUserForm = {}
      this.buildUserForm.userIds = []
      this.buildUserForm.id = data.id
      this.queryParams.roleId = data.id
      this.queryParamsPageSet.pageIndex = pageIndex
      userListByRole({
        ...this.queryParams,
        ...this.queryParamsPageSet
      }).then(
        response => {
          if (response.data) {
            this.roleUserData = response.data.list
            this.queryParamsPageSet.total = response.data.total
            this.queryParamsPageSet.pageIndex = response.data.pageIndex
            this.queryParamsPageSet.pageSize = response.data.pageSize
          }
        }
      )
    },
    treeBuild(data) {
      this.buildForm.id = data.id
      this.treeDialog.title = '权限配置'
      this.treeDialog.dialogFormVisible = true
      this.treeDialog.buttonName = '保存'
      this.treeDialog.permissionList = []
      findTree().then(response => {
        this.menuData = JSON.parse(JSON.stringify(response.data))
        get(this.buildForm.id).then(response => {
          this.buildForm.ruleIds = response.data.ruleIds
          this.buildForm.buttonIds = response.data.buttonIds
          this.buildForm.ids = response.data.permissionList
          this.$refs.tree.setCheckedNodes(response.data.permissionList)
        })
      })
    },
    build(data) {
      this.buildForm.id = data.id
      this.buildDialog.title = '权限配置'
      this.buildDialog.dialogFormVisible = true
      this.buildDialog.buttonName = '保存'
      findTree().then(response => {
        this.menuData = JSON.parse(JSON.stringify(response.data))
        this.initMenu(this.menuData)
        get(this.buildForm.id).then(response => {
          this.buildDialog.ids = response.data.ids
          Array.from(this.buildDialog.ids).forEach(item => {
            this.initCheckMenu(item)
          })
        })
      })
    },
    showRule(data) {
      var checkNodes = this.$refs.tree.getCheckedNodes()
      var checkOver = false
      if (checkNodes) {
        checkNodes.forEach(item => {
          if (item.id === data.id) {
            checkOver = true
          }
        })
      }
      if (!checkOver) {
        this.$message({
          message: '请先选中节点',
          type: 'success'
        })
        return
      }
      this.innerDrawer = true
      this.ruleForm = {}
      this.ruleForm.frontPath = data.frontPath
      findRuleList(this.ruleForm).then(response => {
        this.ruleData = response.data
        this.$nextTick(() => {
          if (this.ruleData) {
            this.ruleData.forEach(row => {
              if (this.buildForm.ruleIds) {
                this.buildForm.ruleIds.forEach(id => {
                  if (row.id === id) {
                    this.$refs.ruleCheckTable.toggleRowSelection(row)
                  }
                })
              }
            })
          }
        })
      })
    },
    showButton(data) {
      var checkNodes = this.$refs.tree.getCheckedNodes()
      var checkOver = false
      if (checkNodes) {
        checkNodes.forEach(item => {
          if (item.id === data.id) {
            checkOver = true
          }
        })
      }
      if (!checkOver) {
        this.$message({
          message: '请先选中节点',
          type: 'success'
        })
        return
      }
      this.innerButtonDrawer = true
      this.buttonForm = {}
      this.buttonForm.frontPath = data.frontPath
      findButtonList(this.buttonForm).then(response => {
        this.buttonData = response.data
        this.$nextTick(() => {
          if (this.buttonData) {
            this.buttonData.forEach(row => {
              if (this.buildForm.buttonIds) {
                this.buildForm.buttonIds.forEach(id => {
                  if (row.id === id) {
                    this.$refs.buttonCheckTable.toggleRowSelection(row)
                  }
                })
              }
            })
          }
        })
      })
    },
    get(data) {
      this.form.id = data.id
      this.form.name = data.name
      this.form.code = data.code
      this.formDialog.title = '角色修改'
      this.formDialog.dialogFormVisible = true
      this.formDialog.model = 'edit'
      this.formDialog.buttonName = '确定'
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            update(this.form).then(response => {
              this.$message({
                message: '角色修改成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              this.querySubmit()
              this.formDialog.dialogFormVisible = false
            })
          } else {
            // 获取编码
            insert(this.form).then(response => {
              this.$message({
                message: '角色新增成功',
                type: 'success'
              })
              this.$refs[formName].resetFields()
              this.querySubmit()
              this.formDialog.dialogFormVisible = false
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    treeSubmit() {
      const trees = this.$refs.tree.getCheckedNodes()
      this.buildForm.ids = []
      Array.from(trees).forEach(item => {
        this.buildForm.ids.push(item.id)
      })
      builder(this.buildForm).then(response => {
        this.$message({
          message: '角色权限配置成功',
          type: 'success'
        })
        this.treeDialog.dialogFormVisible = false
      })
    },
    buildSubmit() {
      const rows = this.$refs.districtTable.selection
      this.buildForm.ids = []
      Array.from(rows).forEach(item => {
        this.buildForm.ids.push(item.id)
      })
      console.info(this.buildForm.ids)
      builder(this.buildForm).then(response => {
        this.$message({
          message: '角色权限配置成功',
          type: 'success'
        })
        this.buildDialog.dialogFormVisible = false
      })
    },
    handleCurrentChange(val) {
      this.pageSet.pageIndex = val
      this.querySubmit()
    },
    handleSizeChange(val) {
      this.pageSet.pageSize = val
      this.querySubmit()
    },
    selectAll(selection, first) {
      if (!first) {
        this.isAllSelect = !this.isAllSelect
      }
      selection.map(el => {
        if (el.children) {
          el.children.map(j => {
            this.toggleSelection(j, this.isAllSelect)
          })
          if (el.children != null && el.children.length > 0) {
            this.selectAll(el.children, true)
          }
        }
      })
    },
    toggleSelection(row, select) {
      if (select) {
        this.$refs.districtTable.toggleRowSelection(row, select)
      } else {
        this.$refs.districtTable.clearSelection()
      }
    },
    selectOne(select, row) {
      const checkrow = []
      checkrow.push(row)
      if (row.isCheck === true) {
        if (select === 1) {
          this.$refs.districtTable.toggleRowSelection(row, false)
        }
        this.clearRow(checkrow)
      } else {
        if (select === 1) {
          this.$refs.districtTable.toggleRowSelection(row, true)
        }
        this.checkRow(checkrow)
      }
      this.doParent(row)
    },
    clearRow(data) {
      Array.from(data).forEach(row => {
        row.isCheck = false
        this.$refs.districtTable.toggleRowSelection(row, false)
        if (row.children) this.clearRow(row.children)
      })
    },
    checkRow(data) {
      Array.from(data).forEach(row => {
        row.isCheck = true
        this.$refs.districtTable.toggleRowSelection(row, true)
        if (row.children) this.checkRow(row.children)
      })
    },
    initCheckMenu(id) {
      Array.from(this.menuChecks).forEach(item => {
        if (item.id === id) {
          this.$refs.districtTable.toggleRowSelection(item, true)
        }
      })
    },
    initMenu(menu) {
      Array.from(menu).forEach(item => {
        this.menuChecks.push(item)
        if (item.children != null && item.children.length > 0) {
          this.initMenu(item.children, this.menuChecks)
        }
      })
    },
    checkParent(row, check) {
      Array.from(this.menuChecks).forEach(item => {
        if (row.parentId === item.id) {
          this.$refs.districtTable.toggleRowSelection(item, check)
          if (item.parentId > 0) {
            this.checkParent(item, check)
          }
        }
      })
    },
    doParent(row) {
      this.initMenu(this.menuData, this.menuChecks)
      this.checkParent(row, row.isCheck)
    },
    handleSelectionChange(selection, row) {
      var selected = selection.length && selection.indexOf(row) !== -1
      if (!this.buildForm.ruleIds) {
        this.buildForm.ruleIds = []
      }
      if (selected) {
        this.buildForm.ruleIds.push(row.id)
      } else {
        if (this.buildForm.ruleIds) {
          this.buildForm.ruleIds.splice(this.buildForm.ruleIds.indexOf(row.id), 1)
        }
      }
    },
    handleSelectionButtonChange(selection, row) {
      var selected = selection.length && selection.indexOf(row) !== -1
      if (!this.buildForm.buttonIds) {
        this.buildForm.buttonIds = []
      }
      if (selected) {
        this.buildForm.buttonIds.push(row.id)
      } else {
        if (this.buildForm.buttonIds) {
          this.buildForm.buttonIds.splice(this.buildForm.buttonIds.indexOf(row.id), 1)
        }
      }
    },
    selectUserInfo(limit, optionType, modalName) {
      this.optionType = optionType
      this.dialogData = []
      // if (this.optionType === 2) {
      //   if (this.roleUserData != null) {
      //     this.roleUserData.forEach(item => {
      //       this.dialogData.push({ id: item.id, name: item.name })
      //     })
      //   }
      // }
      this.userSelectModalName = modalName
      this.isSelectUserOne = limit
      this.searchUsersDialogVisible = true
    },
    checkSelectUserListData() {
      var data = this.$refs.userSelectList.chosedData
      var mappingUserIdList = []
      data.forEach(item => {
        mappingUserIdList.push(item.id)
      })
      this.buildUserForm.userIds = mappingUserIdList
      builderUser(this.buildUserForm).then(response => {
        this.$message({
          message: '添加用户成功',
          type: 'success'
        })
        userListByRole({
          ...this.queryParams,
          ...this.queryParamsPageSet
        }).then(
          response => {
            if (response.data) {
              this.roleUserData = response.data.list
              this.queryParamsPageSet.total = response.data.total
              this.queryParamsPageSet.pageIndex = response.data.pageIndex
              this.queryParamsPageSet.pageSize = response.data.pageSize
            }
          }
        )
        this.searchUsersDialogVisible = false
      })
    },
    selectChildren(data) {
      data && data.children && data.children.map(item => {
        this.$refs.tree.setChecked(item.id, true)
        if (data.children) {
          this.selectChildren(item)
        }
      })
    },
    handleCheck(data, { checkedKeys }) {
      // 节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点
      // 如果为取消
      console.log('handleCheck')
      console.log(data)
      if (checkedKeys.includes(data.id)) {
        // 如果当前节点有子集
        this.selectChildren(data)
      }
    },
    handleCheckChange(data, checked, indeterminate) {
      // 节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点
      // 如果为取消
      if (checked === false) {
        // 如果当前节点有子集
        if (data.children) {
        // 循环子集将他们的选中取消
          data.children.map(item => {
            this.$refs.tree.setChecked(item.id, false)
          })
        }
      } else {
        // 否则(为选中状态)
        // 判断父节点id是否为空
        if (data.parentId !== 0) {
          // 如果不为空则将其选中
          this.$refs.tree.setChecked(data.parentId, true)
        }
      }
    }
  }
}
</script>

<style scoped>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  #footerRule {
    font-size: 14px;
    position:absolute;
    bottom:0;
    width:98%;
    height:50px;   /* 底部高度 */
    margin-left: 30px;
  }
  #footerUser {
    font-size: 14px;
    position:absolute;
    bottom:0;
    width:98%;
    height:50px;   /* 底部高度 */
    margin-left: 30px;
  }

  .el-scrollbar .el-scrollbar__wrap {overflow-x: hidden;}
  .el-tree>.el-tree-node{
    min-width: 100%;
    display:inline-block;
  }
</style>
