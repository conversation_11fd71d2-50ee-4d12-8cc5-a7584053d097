<template>
  <div class="app-container">
    <el-form :inline="true" :model="formInline" class="el-form--inline">
      <el-form-item label="字典名称">
        <el-input v-model="formInline.name" placeholder="字典名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="searchList">查询</el-button>
        <el-button type="primary" icon="el-icon-refresh" @click="resetValue">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="openAdd">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="currentTableData.list"
      border
      style="width: 100%"
    >
      <el-table-column
        type="index"
        width="60"
      />
      <el-table-column property="name" label="字典名称" />
      <el-table-column property="code" label="字典编号" />
      <el-table-column property="description" label="描述" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="5">
              <div class="grid-content bg-purple-dark">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="openUpdate(scope.row)"
                >编辑
                </el-button>
              </div>
            </el-col>
            <el-col :span="7">
              <div class="grid-content bg-purple-dark">
                <el-button
                  type="text"
                  @click="dictSetting(scope.row)"
                >
                  <svg-icon icon-class="system-setting" />
                  字典配置
                </el-button>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="grid-content bg-purple-dark">
                <el-button slot="reference" type="text" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 30]"
      :total="currentTableData.total"
      :page-size="formInline.pageSize"
      :current-page="formInline.pageIndex"
      layout="total, sizes, prev, pager, next, jumper"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <el-dialog :title="dialogEdit.title" :visible="dialogEdit.isShowDialog" :close-on-click-modal="false" @close="closeDialog('dataEdit')">
      <el-form ref="dataEdit" :model="dialogEdit.data" :rules="dialogEdit.rules" label-width="100px">
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="dialogEdit.data.name" />
        </el-form-item>
        <el-form-item label="字典编码" prop="code">
          <el-input v-model="dialogEdit.data.code" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogEdit.data.description" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="closeDialog('dataEdit')">关闭</el-button>
          <el-button @click="saveDict('dataEdit')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--     回收站对话框-->
    <el-dialog :title="recycleBinDialog.title" :visible="recycleBinDialog.isShowDialog" :close-on-click-modal="false" @close="closeRecycleDialog()">
      <el-table
        :data="recycleBinDialog.data.list"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          width="50"
        />
        <el-table-column property="name" width="100%" label="字典名称" />
        <el-table-column property="code" width="100%" label="字典编号" />
        <el-table-column property="description" width="100%" label="描述" />
        <el-table-column width="300px" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-refresh"
              @click="cancelDelete(scope.row)"
            >字典取回
            </el-button>
            <el-button
              type="text"
              @click="recycleDelete(scope.row)"
            >
              <svg-icon icon-class="scissors" />
              彻底删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="recycleBinDialog.request.pageIndex"
        :page-sizes="[10, 20, 30]"
        :page-size="recycleBinDialog.request.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="recycleBinDialog.data.total"
        @size-change="handleRecycleSizeChange"
        @current-change="handleRecycleCurrentChange"
      />
    </el-dialog>
    <!--     字典值抽屉-->
    <el-drawer
      :size="dictValueDialog.screenWidth"
      :title="dictValueDialog.title"
      :visible.sync="dictValueDialog.isShowDialog"
      :direction="dictValueDialog.direction"
      @close="closeDictValueDialog()"
    >
      <el-form :inline="true" :model="dictValueDialog.request" class="el-form--inline">
        <el-form-item label="字典值名称">
          <el-input v-model="dictValueDialog.request.itemText" placeholder="字典值名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="searchDictValueDialog">查询</el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="resetDictValueValue">重置</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="el-form--inline">
        <el-form-item>
          <el-button type="primary" icon="el-icon-plus" @click="openDictValueAdd">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="dictValueDialog.data.list"
        border
        style="width: 100%"
      >
        <el-table-column
          type="index"
          width="50"
        />
        <el-table-column property="itemValue" label="字典编号" />
        <el-table-column property="itemText" label="字典值" />
        <el-table-column property="description" label="描述" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="18">
                <el-button type="text" @click="openDictValueEdit(scope.row)">编辑</el-button>
                <el-button type="text" @click="dictValueDelete(scope.row)">删除 </el-button>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 30]"
        :total="dictValueDialog.data.total"
        :page-size="dictValueDialog.request.pageSize"
        :current-page="dictValueDialog.request.pageIndex"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleDictValueSizeChange"
        @current-change="handleDictValueCurrentChange"
      />
      <el-dialog
        :title="dictValueForm.title"
        :visible="dictValueForm.isShowDialog"
        class="el-card__body"
        :append-to-body="true"
        @close="closeDictValueForm('dictValueEdit')"
      >
        <el-form ref="dictValueEdit" :model="dictValueForm.data" :rules="dictValueForm.rules" label-width="100px">
          <el-form-item label="字典值编码" prop="itemValue">
            <el-input v-model="dictValueForm.data.itemValue" placeholder="字典值key" />
          </el-form-item>
          <el-form-item label="字典值" prop="itemText">
            <el-input v-model="dictValueForm.data.itemText" placeholder="字典值value" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="dictValueForm.data.description" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="closeDictValueForm('dictValueEdit')">关闭</el-button>
            <el-button @click="saveDictValue('dictValueEdit')">确定</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-drawer>
  </div>
</template>

<script>
import { list, save, businessDel, businessDelCancel, physicsDel, update } from '@/api/dict'
import { dictValueList, saveDictValue, businessDelDictValue, updateDictValue, physicsDelDictValue, businessDelCancelDictValue } from '@/api/dictValue'

export default {
  name: 'Dict',
  data() {
    return {
      formInline: {
        name: null,
        code: null,
        pageIndex: 1,
        pageSize: 10
      },
      currentTableData: {
        total: null,
        list: []
      },
      tableBtnPopover: false,
      dialogEdit: {
        isShowDialog: false,
        title: null,
        // type: 0:新增，1：修改
        type: null,
        data: {
          id: null,
          name: null,
          code: null,
          description: null
        },
        rules: {
          name: [
            { required: true, message: '请输入字典名称', trigger: 'change' }
          ],
          code: [
            { required: true, message: '请输入字典编码', trigger: 'change' }
          ]
        }
      },
      // 回收站对话框
      recycleBinDialog: {
        isShowDialog: false,
        title: '回收站',
        data: {
          list: [],
          total: null
        },
        request: {
          isDelete: 1,
          pageIndex: 1,
          pageSize: 10
        }
      },
      // 字典值对话框
      dictValueDialog: {
        screenWidth: null,
        title: '字典列表',
        isShowDialog: false,
        // rtl：右侧打开，rtr：左侧打开
        direction: 'rtl',
        data: {
          list: [],
          total: null
        },
        request: {
          itemText: null,
          state: null,
          dictId: null,
          pageIndex: 1,
          pageSize: 10
        }
      },
      tableBtnDictValuePopover: false,
      dictValueForm: {
        isShowDialog: false,
        title: null,
        // type: 0:新增，1：修改
        type: null,
        data: {
          id: null,
          dictId: null,
          itemText: null,
          itemValue: null,
          isDelete: 0,
          description: null
        },
        rules: {
          itemText: [
            { required: true, message: '请输入字典值', trigger: 'change' }
          ],
          itemValue: [
            { required: true, message: '请输入字典值编码', trigger: 'change' }
          ]
        }
      }
    }
  },
  mounted() {
    this.searchList()
  },
  methods: {
    searchList() {
      // ajax请求
      list(this.formInline).then(response => {
        this.currentTableData = response.data
      }).catch(exception => {
        console.log(exception)
      })
    },
    resetValue() {
      this.formInline = {
        name: null,
        code: null,
        pageInfo: {
          pageIndex: 1,
          pageSize: 10
        }
      }
    },
    dictSetting(data) {
      this.dictValueDialog.isShowDialog = true
      this.dictValueDialog.request.dictId = data.id
      const screenWidth = document.body.clientWidth
      this.dictValueDialog.screenWidth = screenWidth < 800 ? screenWidth : '800px'
      this.searchDictValueDialog()
    },
    handleDelete(data) {
      this.$confirm('此操作将永久删除该字典数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        businessDel(data.id).then(resp => {
          // 页面刷新
          if (resp.data) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.searchList()
            return
          }
          this.$message({
            type: 'error',
            message: '删除字典项为： 【code: ' + data.code + '，name:  】 ' + data.name + ' 失败!失败原因：' + resp.msg
          })
        }).catch(exception => {
          console.log(exception)
        })
      })
    },
    openAdd() {
      this.dialogEdit.title = '新增'
      this.dialogEdit.isShowDialog = true
      this.dialogEdit.type = 0
    },
    openUpdate(data) {
      this.dialogEdit.title = '编辑'
      this.dialogEdit.isShowDialog = true
      this.dialogEdit.type = 1
      this.dialogEdit.data = data
    },
    saveDict(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          return false
        }
        // 提交数据
        if (this.dialogEdit.type === 0) {
          save(this.dialogEdit.data).then(resp => {
            // 页面刷新
            this.$message({
              showClose: true,
              message: ' 新增 "' + resp.data.name + '" 字典数据成功！',
              type: 'success'
            })
            this.searchList()
            this.dialogEdit.isShowDialog = false
            this.$refs[formName].resetFields()
          }).catch(exception => {
            console.log(exception)
          })
        }
        if (this.dialogEdit.type === 1) {
          update(this.dialogEdit.data).then(resp => {
            // 页面刷新
            this.$message({
              showClose: true,
              message: ' "' + this.dialogEdit.data.name + '" 字典数据更新成功！',
              type: 'success'
            })
            this.searchList()
            this.dialogEdit.isShowDialog = false
            this.$refs[formName].resetFields()
          }).catch(exception => {
            console.log(exception)
          })
        }
      })
    },
    closeDialog(formName) {
      this.dialogEdit.isShowDialog = false
      this.dialogEdit.title = null
      this.dialogEdit.data = {
        id: null,
        name: null,
        code: null,
        description: null
      }
      this.$refs[formName].resetFields()
    },
    handleCurrentChange(val) {
      this.formInline.pageIndex = val
      this.searchList()
    },
    handleSizeChange(val) {
      this.formInline.pageSize = val
      this.formInline.pageIndex = 1
      this.searchList()
    },
    // 回收站对话框
    searchRecycleBinDialog() {
      list(this.recycleBinDialog.request).then(response => {
        this.recycleBinDialog.data = response.data
      }).catch(exception => {
        console.log(exception)
      })
    },
    recycleShow() {
      this.recycleBinDialog.isShowDialog = true
      this.searchRecycleBinDialog()
    },
    cancelDelete(data) {
      businessDelCancel(data.id).then(resp => {
        // 页面刷新
        this.$message({
          showClose: true,
          message: ' "' + data.name + '" 字典数据回收成功！',
          type: 'success'
        })
        this.searchRecycleBinDialog()
      }).catch(exception => {
        console.log(exception)
      })
    },
    recycleDelete(data) {
      this.$confirm('确定要彻底删除该字典数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        physicsDel(data.id).then(resp => {
          // 对话页面刷新
          this.$message({
            showClose: true,
            message: ' "' + data.name + '" 字典数据彻底删除成功！',
            type: 'success'
          })
          this.searchRecycleBinDialog()
        }).catch(exception => {
          console.log(exception)
        })
      }).catch(exception => {
        console.log(exception)
      })
    },
    handleRecycleCurrentChange(val) {
      this.recycleBinDialog.request.pageIndex = val
      this.searchRecycleBinDialog()
    },
    handleRecycleSizeChange(val) {
      this.recycleBinDialog.request.pageSize = val
      this.recycleBinDialog.request.pageIndex = 1
      this.searchRecycleBinDialog()
    },
    closeRecycleDialog() {
      this.recycleBinDialog.isShowDialog = false
      this.recycleBinDialog.data = {
        list: [],
        total: null
      }
      this.searchList()
    },
    // 字典值对话框 抽屉的宽度随着屏幕大小来改变
    searchDictValueDialog() {
      dictValueList(this.dictValueDialog.request).then(response => {
        this.dictValueDialog.data = response.data
      }).catch(exception => {
        console.log(exception)
      })
    },
    resetDictValueValue() {
      const dictId = this.dictValueDialog.request.dictId
      this.dictValueDialog.request = {
        itemText: null,
        state: null,
        dictId: dictId,
        pageIndex: 1,
        pageSize: 10
      }
      this.searchDictValueDialog()
    },
    handleDictValueCurrentChange(val) {
      this.dictValueDialog.request.pageIndex = val
      this.searchDictValueDialog()
    },
    handleDictValueSizeChange(val) {
      this.dictValueDialog.request.pageSize = val
      this.dictValueDialog.request.pageIndex = 1
      this.searchDictValueDialog()
    },
    dictValueDelete(data) {
      this.tableBtnDictValuePopover = false
      this.$confirm('确定要彻底删除该字典值数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        physicsDelDictValue(data.id).then(resp => {
          // 页面刷新
          console.log(data)
          this.$message({
            showClose: true,
            message: ' "' + data.itemText + '" 字典值数据彻底删除成功！',
            type: 'success'
          })
          this.searchDictValueDialog()
        }).catch(exception => {
          console.log(exception)
        })
      }).catch(exception => {
        console.log(exception)
      })
    },
    closeDictValueDialog() {
      this.dictValueDialog.isShowDialog = false
      this.dictValueDialog.data = {
        list: [],
        total: null
      }
    },
    openDictValueAdd() {
      this.dictValueForm.isShowDialog = true
      this.dictValueForm.title = '新增'
      this.dictValueForm.type = 0
      this.dictValueForm.data.dictId = this.dictValueDialog.request.dictId
    },
    openDictValueEdit(data) {
      this.dictValueForm.isShowDialog = true
      this.dictValueForm.title = '修改'
      this.dictValueForm.type = 1
      this.dictValueForm.data = data
    },
    saveDictValue(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          return false
        }
        // 提交数据
        if (this.dictValueForm.type === 0) {
          saveDictValue(this.dictValueForm.data).then(resp => {
            // 页面刷新
            this.$message({
              showClose: true,
              message: '新增 "' + this.dictValueForm.data.itemText + '" 字典值数据成功！',
              type: 'success'
            })
            this.searchDictValueDialog()
            this.dictValueForm.isShowDialog = false
            this.$refs[formName].resetFields()
          }).catch(exception => {
            console.log(exception)
          })
        }
        if (this.dictValueForm.type === 1) {
          updateDictValue(this.dictValueForm.data).then(resp => {
            // 页面刷新
            this.$message({
              showClose: true,
              message: ' "' + this.dictValueForm.data.itemText + '" 字典值数据更新成功！',
              type: 'success'
            })
            this.searchDictValueDialog()
            this.dictValueForm.isShowDialog = false
            this.$refs[formName].resetFields()
          }).catch(exception => {
            console.log(exception)
          })
        }
      })
    },
    isUseDictValue(event, data) {
      if (!event) {
        businessDelCancelDictValue(data.id).then(resp => {
          // 页面刷新
          this.$message({
            showClose: true,
            message: ' "' + data.itemText + '" 字典值启用成功！',
            type: 'success'
          })
          data.isDelete = event
          this.searchDictValueDialog()
        }).catch(exception => {
          console.log(exception)
        })
      } else {
        businessDelDictValue(data.id).then(resp => {
          // 页面刷新
          this.$message({
            showClose: true,
            message: ' "' + data.itemText + '" 字典值禁用成功！',
            type: 'success'
          })
          data.isDelete = event
          this.searchDictValueDialog()
        }).catch(exception => {
          console.log(exception)
        })
      }
    },
    closeDictValueForm(formName) {
      this.dictValueForm.isShowDialog = false
      this.dictValueForm.title = null
      this.dictValueForm.data = {
        id: null,
        itemText: null,
        itemValue: null,
        isDelete: 0,
        description: null
      }
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
::v-deep :focus {
  outline: none;
}
</style>
