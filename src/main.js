import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Permission from '@/directive/permission'
import Drag from '@/directive/drag'
import Element from 'element-ui'
import './styles/element-variables.scss'
// import enLang from 'element-ui/lib/locale/lang/en'// 如果使用中文语言包请默认支持，无需额外引入，请删除该依赖

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as utils from '@/utils/index'
import * as filters from './filters' // global filters

import plugins from './components/index'
import BaiduMap from 'vue-baidu-map'
import ImagePreview from '@/components/ImagePreview'
import handleClipboard from '@/utils/clipboard'
import enums from '@/enums'
import settings from '@/settings'
import TenThousandInput from '@/components/BtForms/TenThousandInput'
import PercentInput from '@/components/BtForms/PercentInput'
import FormAttachment from '@/components/FormAttachment'
import AuditBtn from '@/components/Audit/AuditBtn'
import LiveBroadcastPlanChoose from '@/components/EmptyComponent'
import DyProductCategoriesSelect from '@/components/DyProductCategoriesSelect'
Vue.component('TenThousandInput', TenThousandInput)
Vue.component('PercentInput', PercentInput)
Vue.component('FormAttachment', FormAttachment)
Vue.component('AuditBtn', AuditBtn)
Vue.component('LiveBroadcastPlanChoose', LiveBroadcastPlanChoose)
Vue.component('DyProductCategoriesSelect', DyProductCategoriesSelect)
Vue.use(ImagePreview)
Vue.use(Permission)
Vue.use(Drag)
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'small'
  // , // set element-ui default size
  // locale: enLang // 如果使用中文，无需设置，请删除
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
Vue.prototype.$eventBus = new Vue()
Vue.prototype.$copy = handleClipboard
Vue.prototype.$enums = enums
Vue.prototype.$utils = utils
Vue.prototype.$settings = settings
Vue.prototype.$isAdmin = settings.siteType === 'manager'
Vue.prototype.$checkHasPermission = () => true
Vue.prototype.$returnBack = function() {
  this.$router.back()
}

Vue.use(plugins)
Vue.use(BaiduMap, { ak: '5fd9e8aab0d00c2d4caa8fe84c0acad0' })

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
