import { asyncRoutes, constantRoutes } from '@/router'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(menus, route) {
  if (route.meta && route.meta.menuCode) {
    return menus.some(menu => route.meta.menuCode === menu)
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, menus) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(menus, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, menus)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: [],
  currentRoutes: {},
  sysPermissionVos: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_CURRENT_ROUTES: (state, routes) => {
    state.currentRoutes = routes
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.sysPermissionVos = permissions
  }
}

const actions = {
  generateRoutes({ commit }, menus) {
    commit('SET_PERMISSIONS', menus)
    return new Promise(resolve => {
      let accessedRoutes = []
      const menuCodes = []
      menus.forEach(menu => {
        menuCodes.push(menu.authorizeKey)
      })
      accessedRoutes = filterAsyncRoutes(asyncRoutes, menuCodes)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
