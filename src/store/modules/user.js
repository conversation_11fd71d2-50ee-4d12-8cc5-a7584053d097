import { getCurrentUserInfo, login, logout } from '@/api/user'
import { getToken, removeToken, setToken } from '@/utils/auth'
import md5 from '@/utils/md5'
import Watermark from '@/utils/watermark'
import router, { resetRouter } from '@/router'
import noticeLogApi from '@/api/notice/log'

const state = {
  token: getToken(),
  userId: null,
  name: '',
  userCode: '',
  avatar: '',
  introduction: '',
  roles: [],
  info: null,
  unReadCount: 0,
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_USER_ID: (state, userId) => {
    state.userId = userId
  },
  SET_USER_CODE: (state, userCode) => {
    state.userCode = userCode
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_INFO: (state, info) => {
    state.info = info
  },
  SET_UNREAD_NOTICE_COUNT: (state, unReadCount) => {
    state.unReadCount = unReadCount
  },
}

const actions = {
  // 获取未读消息数量
  getUnReadNoticeCount({ commit }) {
    noticeLogApi.countUnRead().then(res => {
      if (res.code === 200) {
        commit('SET_UNREAD_NOTICE_COUNT', res.data || 0)
      }
    })
  },
  // user login
  login({ commit }, userInfo) {
    const { account, password, ...others } = userInfo
    return new Promise((resolve, reject) => {
      login({ account: account.trim(), password: md5(password), ...others }).then(response => {
        console.log(response)
        const { data } = response
        commit('SET_TOKEN', data)
        setToken(data)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getCurrentUserInfo(state.token).then(response => {
        const { data } = response

        if (!data) {
          reject('身份校验失败，请重新登录')
        }

        // const { sysUserVo, introduction } = data
        // 因为菜单权限还没做好，这里先写死一个，让路由加载进去
        const roles = ['roles']
        // roles must be a non-empty array
        // if (!roles || roles.length <= 0) {
        //   reject('getInfo: roles must be a non-null array!')
        // }

        commit('SET_ROLES', roles)
        commit('SET_USER_ID', data.id)
        commit('SET_USER_CODE', data.code)
        commit('SET_NAME', data.contactName)
        commit('SET_INFO', data)
        // commit('SET_AVATAR', data.photo)
        // commit('SET_INTRODUCTION', introduction)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()

        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })
        Watermark.set('')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
