<template>
  <el-dialog :title="title" :visible.sync="isShow" :width="width" :close-on-click-modal="false" append-to-body class="common-choose-dialog">
    <!-- {{ selectItems.map(item => item.id) }} -->
    <component :is="component" v-if="isShow" ref="pagerComponentRef" :init-select-items="selectItems" :can-choose="true" :multiple="multiple" :limit="limit" :init-search-params="initSearchParams" @confirm="confirmHandle" />
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
// import Contract from '@/views/contract'
// import Enterprise from '@/views/enterprise'
// import PreSignUp from '@/views/live-broadcast/pre-sign-up'
// import Collection from '@/views/finance/collection'
export default {
  components: {
    // Contract,
    // Enterprise,
    // PreSignUp,
    // Collection
  },
  props: {
    width: {
      type: String,
      default: '1000px'
    },
    // 列表组件
    component: {
      type: Object,
      default: null
    },
    // 标题
    title: {
      type: String,
      default: '',
    },
    // 行的唯一标识
    rowKey: {
      type: String,
      default: 'id'
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 限选数量，0表示不限
    limit: {
      type: Number,
      default: 0
    },
    // 选择回调
    onChosed: {
      type: Function,
      default: null
    },
    // 初始化的搜索参数
    initSearchParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      name: this.$route.meta.title,
      searchForm: {
        enableFlag: '是'
      },
      isShow: false,
      selectItems: [], // 选中的项
      callback: null
    }
  },
  computed: {
  },
  //
  created() {},
  mounted() {
  },
  methods: {
    // initSelection() {
    //   if (!this.$refs.pagerComponentRef) {
    //     return
    //   }
    //   const tableRef = this.$refs.pagerComponentRef.$refs.tableRef
    //   if (!tableRef) {
    //     return
    //   }
    //   // console.log('2323232323', tableRef.getCurrentRow())
    //   tableRef.data.forEach(row => {
    //     const index = this.getSelectedRowIndex(row)
    //     tableRef.toggleRowSelection(row, index > -1)
    //   })
    // },
    // 显示弹框
    show(items, callback) {
      this.isShow = true
      if (items) {
        this.selectItems = [...items]
      } else {
        this.selectItems = []
      }
      this.callback = callback
    },
    // rowClick(row) {
    //   // console.log('rowClick')
    //   const index = this.getSelectedRowIndex(row)
    //   if (index > -1) {
    //     this.selectItems.splice(index, 1)
    //   } else {
    //     if (!this.multiple) {
    //       this.selectItems.splice(0, this.selectItems.length, row)
    //     } else if (this.limit > 0 && this.selectItems.length >= this.limit) {
    //       this.$message.error('最多只能选择' + this.limit + '项')
    //     } else {
    //       this.selectItems.push(row)
    //     }
    //   }
    // },
    // rowDblClick(row) {
    //   if (!this.multiple) {
    //     this.selectItems = [row]
    //     this.confirmHandle()
    //   }
    // },
    // getSelectedRowIndex(row) {
    //   return this.selectItems.findIndex(item => item[this.rowKey] === row[this.rowKey])
    // },
    // // 复选框变化监听事件
    // handleSelectionChange(items) {
    //   console.log('手动勾选变化')
    //   const tableRef = this.$refs.pagerComponentRef.$refs.tableRef
    //   tableRef.data.forEach(row => {
    //     const isChecked = items.find(it => it === row)
    //     const index = this.getSelectedRowIndex(row)
    //     if (isChecked && index === -1 && this.selectItems.length < this.limit) {
    //       this.selectItems.push(row)
    //     } else if (!isChecked && index > -1) {
    //       this.selectItems.splice(index)
    //     }
    //   })
    //   // this.initSelection()
    //   // console.log('handleSelectionChange', items, tableData)
    //   // this.selectItems
    // },
    // tableRowClassName({ row }) {
    //   const index = this.getSelectedRowIndex(row)
    //   if (index > -1) {
    //     return 'chosed-row'
    //   }
    //   return ''
    // },
    confirmHandle() {
      this.selectItems = this.$refs.pagerComponentRef.selectItems
      if (!this.selectItems.length) {
        this.$message.info('请选择一条数据')
        return
      }
      // console.log('confirmHandle', this.selectItems)
      this.isShow = false
      this.onChosed && this.onChosed(this.selectItems)
      this.callback && this.callback(this.selectItems)
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  // .chosed-row td{
  //   background: #f0f9eb !important;
  // }
  .disabled-row td{
    background: #dfe4ed !important;
  }
}
// .common-choose-dialog {

// }
</style>
