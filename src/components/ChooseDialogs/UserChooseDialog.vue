<template>
  <el-dialog :title="title" :visible.sync="isShow" width="900px" :close-on-click-modal="false" append-to-body>
    <div class="app-container">
      <div class="filter-container">
        <div class="search-view">
          <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
            <el-form-item label="登录名">
              <el-input v-model="searchForm.loginName" placeholder="登录名" />
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="searchForm.name" placeholder="姓名" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
              <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-table
        class="list-table"
        :data="tableData"
        :stripe="true"
        :row-class-name="tableRowClassName"
        border
        style="width: 100%;margin-top: 10px;"
        @row-click="rowClick"
        @row-dblclick="rowDblClick"
      >
        <el-table-column
          prop="id"
          label="序号"
          min-width="50"
        />
        <el-table-column
          prop="loginName"
          label="登录名"
        />
        <el-table-column
          prop="no"
          label="工号"
        />
        <el-table-column
          prop="name"
          label="姓名"
        />
        <!-- <el-table-column
          prop="isEnable"
          label="是否有效"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnable" type="success">启用</el-tag>
            <el-tag v-else type="info">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="sex"
          label="性别"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.sex === 1">男</span>
            <span v-if="scope.row.sex === 2">女</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="nation"
          label="民族"
        />
        <el-table-column
          prop="email"
          label="电子邮件"
        />
        <el-table-column
          prop="phone"
          label="手机号码"
          min-width="150"
        />
        <el-table-column
          prop="birthday"
          label="生日"
        >
          <template slot-scope="scope">
            {{ scope.row.birthday | parseDate('{y}-{m}-{d}') }}
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        :limit.sync="pageSet.pageSize"
        :page.sync="pageSet.pageIndex"
        :total="pageSet.total"
        @pagination="pullData"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import listMixin from '@/mixins/listMixin'
import { userList } from '@/api/user'

export default {
  components: {
  },
  mixins: [listMixin],
  props: {
    // 选择回调
    onChosed: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      name: this.$route.meta.title,
      searchForm: {
        enableFlag: '是'
      },
      isShow: false,
      title: '',
      disabledNo: null,
      chosedItem: null,
      functionClassificationList: [],
      // 增删改查集合
      apis: {
        getPager: userList
      }
    }
  },
  computed: {
  },
  created() {},
  mounted() {
  },
  methods: {
    async pullData() {
      this.isLoading = true
      const response = await this.apis.getPager({
        ...this.searchForm,
        ...this.pageSet,
        currentPage: this.pageSet.pageIndex
      })
      this.isLoading = false
      if (response.data) {
        this.tableData = response.data.list
        this.pageSet.total = response.data.total
        this.pageSet.pageIndex = response.data.pageIndex
        this.pageSet.pageSize = response.data.pageSize
      }
      return response
    },
    // 显示弹框
    show(disabledNo, item) {
      this.pullData()
      this.isShow = true
      this.title = '选择人员'
      this.chosedItem = item
      this.disabledNo = disabledNo
    },
    rowClick(row) {
      if (row.code === this.disabledNo) {
        return false
      }
      if (!this.chosedItem || this.chosedItem.code !== row.code) {
        this.chosedItem = row
      } else {
        this.chosedItem = null
      }
    },
    rowDblClick(row) {
      if (row.code === this.disabledNo) {
        return false
      }
      this.chosedItem = row
      this.confirmHandle()
    },
    confirmHandle() {
      if (!this.chosedItem) {
        this.$message.info('请选择一条数据')
        return
      }
      this.isShow = false
      this.onChosed && this.onChosed(this.chosedItem)
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.code === this.disabledNo) {
        return 'disabled-row'
      }
      if (this.chosedItem && row.code === this.chosedItem.code) {
        return 'chosed-row'
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  .chosed-row td{
    background: #f0f9eb !important;
  }
  .disabled-row td{
    background: #dfe4ed !important;
  }
}
</style>
