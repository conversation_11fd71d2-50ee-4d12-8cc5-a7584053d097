<template>
  <div class="oprate-form">
    <div class="filter-container">
      <div class="search-view">
        <el-form ref="searchForm" inline :model="searchForm" label-suffix="：" class="search-form">
          <el-form-item label="sku名称">
            <el-input v-model="searchForm.name" placeholder="商品名称" />
          </el-form-item>
          <el-form-item label="商品类目">
            <ProductCategorySelect v-model="searchForm.productCategoryCode" />
          </el-form-item>
          <el-form-item label="商品品牌">
            <el-input v-model="searchForm.brand" placeholder="商品品牌" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="submitSearch">查询</el-button>
            <el-button type="default" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- {{ selectItems.map(item => item.id) }} -->
    <el-table
      ref="tableRef"
      class="list-table"
      :data="tableData"
      :stripe="true"
      :row-class-name="tableRowClassName"
      :highlight-current-row="true"
      border
      style="width: 100%;"
      @row-click="rowClick"
      @row-dblclick="rowDblClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="multiple"
        fixed
        type="selection"
        label="选择"
        align="center"
        min-width="45"
      />
      <el-table-column
        v-else
        fixed
        label="选择"
        align="center"
        min-width="45"
      >
        <template slot-scope="scope">
          <el-radio :value="selectItems.length ? selectItems[0].id : ''" :label="scope.row.id" class="no-text-radio" />
        </template>
      </el-table-column>
      <el-table-column
        prop="merchantCode"
        label="商户编号"
        min-width="130"
      />
      <el-table-column
        prop="companyName"
        label="公司名称"
        min-width="150"
      />
      <el-table-column
        prop="brand"
        label="品牌名称"
        min-width="180"
      />
      <el-table-column
        prop="code"
        label="抖音商品ID"
        min-width="180"
      />
      <el-table-column
        prop="name"
        label="sku名称"
        min-width="180"
      />
      <el-table-column
        prop="productCategoryName"
        label="商品类目"
        min-width="180"
      />
      <el-table-column
        prop="createTime"
        label="报名时间"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | parseDate }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="状态"
        prop="status"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.status | toEnumName('liveBroadcastPerfectStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        label="商务"
        prop="businessAudit"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.businessAudit | toEnumName('auditStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        label="运营"
        prop="operationsAudit"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.operationsAudit | toEnumName('auditStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        label="合规"
        prop="complianceAudit"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.complianceAudit | toEnumName('auditStatus') }}
        </template>
      </el-table-column>
      <el-table-column
        label="客服"
        prop="customerServiceAudit"
        min-width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.customerServiceAudit | toEnumName('auditStatus') }}
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      :limit.sync="pageSet.pageSize"
      :page.sync="pageSet.pageIndex"
      :total="pageSet.total"
      @pagination="pullData"
    />
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="close">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </div>
</template>
<script>
import listMixin from '@/mixins/listMixin'
import palletApi from '@/api/pallet'
import ProductCategorySelect from '@/components/ProductCategorySelect'

export default {
  components: {
    ProductCategorySelect
  },
  mixins: [listMixin],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 限选数量，0表示不限
    limit: {
      type: Number,
      default: 0
    },
    // 选择回调
    onChosed: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      searchForm: {
        name: null,
        productCategoryCode: null,
        brand: null
      },
      // isShow: false,
      title: '',
      selectItems: [], // 选中的项
      rowKey: 'id',
      // 增删改查集合
      apis: {
        getPager: palletApi.skuPager
      }
    }
  },
  computed: {
  },
  mounted() {
    this.pullData()
  },
  methods: {
    close() {
      // this.isShow = false
      this.$emit('input', false)
    },
    handleSelectionChange(val) {
      this.selectItems = val
      // console.log('handleSelectionChange', this.selectItems)
    },
    rowClick(row) {
      // console.log('rowClick')
      const index = this.getSelectedRowIndex(row)
      if (!this.multiple) { // 单选
        this.selectItems.splice(0, this.selectItems.length, row)
      } else {
        if (this.limit > 0 && this.selectItems.length >= this.limit) {
          this.$message.error('最多只能选择' + this.limit + '项')
        } else {
          this.$refs.tableRef.toggleRowSelection(row, index === -1)
          // this.selectItems.push(row)
        }
      }
    },
    rowDblClick(row) {
      if (!this.multiple) {
        this.selectItems = [row]
        this.confirmHandle()
      }
    },
    getSelectedRowIndex(row) {
      return this.selectItems.findIndex(item => item[this.rowKey] === row[this.rowKey])
    },
    tableRowClassName({ row }) {
      const index = this.getSelectedRowIndex(row)
      if (index > -1) {
        return 'chosed-row'
      }
      return ''
    },
    confirmHandle() {
      if (!this.selectItems.length) {
        this.$message.info('请选择一条数据')
        return
      }
      this.close()
      this.onChosed && this.onChosed(this.selectItems)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
