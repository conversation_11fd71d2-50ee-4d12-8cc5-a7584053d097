<template>
  <el-dialog :title="title" :visible.sync="isShow" width="1180px" :close-on-click-modal="false" append-to-body>
    <el-tabs v-if="isShow && type === ''" v-model="activeName" type="border-card" style="background-color:#f9fafc">
      <el-tab-pane v-for="item in componentList" :key="item.name" :label="item.label" :name="item.name">
        <component :is="item.component" v-if="activeName === item.name" v-model="isShow" :multiple="multiple" :limit="limit" :on-chosed="onChosed" />
      </el-tab-pane>
    </el-tabs>
    <div v-else-if="isShow" style="background-color: #f9fafc;padding: 15px;">
      <template v-for="item in componentList">
        <component :is="item.component" v-if="activeName === item.name" :key="item.name" v-model="isShow" :multiple="multiple" :limit="limit" :on-chosed="onChosed" @close="close" />
      </template>
    </div>
  </el-dialog>
</template>
<script>
import Product from './Product'
import MyPalletProduct from './MyPalletProduct'

export default {
  components: {
    Product,
    MyPalletProduct
  },
  props: {
    // 选择回调
    onChosed: {
      type: Function,
      default: null
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 限选数量
    limit: {
      type: Number,
      default: 0
    },
    /**
     * 类型
     * 空表示商品和我的货盘表
     * product 表示只选择商品
     * myPalletProduct 表示只选择我的货盘表
    */
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeName: 'product',
      isShow: false,
      title: '选择SKU',
      componentList: [
        { name: 'product', label: '商品', component: Product },
        { name: 'myPalletProduct', label: '我的货盘表', component: MyPalletProduct },
      ]
    }
  },
  // computed: {
  //   compileData() {
  //     return {
  //       isShow: this.isShow,
  //       activeName: this.activeName
  //     }
  //   }
  // },
  // watch: {
  //   compileData: {
  //     deep: true,
  //     handler({ isShow, activeName }) {
  //       if (isShow) {
  //         this.$nextTick(() => {
  //           if (activeName === '1') {
  //             this.$refs.productRef.show()
  //           } else {
  //             this.$refs.myPalletProductRef.show()
  //           }
  //         })
  //       }
  //     }
  //   }
  // },
  mounted() {
  },
  methods: {
    close() {
      this.isShow = false
      // this.$emit('input', false)
    },
    // 显示弹框
    show() {
      this.isShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  // .chosed-row td{
  //   background: #f0f9eb !important;
  // }
  .disabled-row td{
    background: #dfe4ed !important;
  }
}
</style>
