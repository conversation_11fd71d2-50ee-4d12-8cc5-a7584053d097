<template>
  <div class="flex-content">
    <el-input
      v-model="modalValue"
      clearable
      placeholder="请输入"
    />
    <el-button v-if="!disabled" style="width:55px" type="primary" plain class="ml5" @click="chooseHandle">选择</el-button>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  computed: {
    modalValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    chooseHandle() {
      this.modalValue = '公司111'
    },
  }
}
</script>
<style lang="scss" scoped>
</style>
