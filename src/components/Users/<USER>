<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="6">
        <el-input
          v-model.trim="filterText"
          placeholder="搜索过滤所属组织"
        />
        <el-divider />
        <div style="padding-bottom: 5px">
          当前选择: {{ currentData.data.name }}
          <el-button v-if="currentData.data.name" type="text" @click="cancelSelect">
            取消选择
          </el-button>
        </div>
        <el-tree
          ref="organizeTree"
          :props="orgTrees"
          node-key="id"
          class="filter-tree"
          lazy
          :load="loadOrgNode"
          highlight-current
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        />
      </el-col>
      <el-col :span="18">
        <el-form :inline="true" :model="userQuery">
          <el-form-item label="登录名">
            <el-input v-model="userQuery.loginName" placeholder="登录名" />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="userQuery.name" placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="chosed-box">
          <b>已选择：</b>
          <template v-if="limit===1">
            <el-tag
              v-for="(item, index) in chosedData"
              :key="index"
            >{{ item.name }}
            </el-tag>
          </template>
          <template v-else>
            <el-tag
              v-for="(item, index) in chosedData"
              :key="index"
              closable
              class="chosed-item"
              @close="removeItem(item, index)"
            >{{ item.name }}
            </el-tag>
          </template>
        </div>
        <!--- 渲染成单选的 ---->
        <template v-if="limit===1">
          <el-table
            key="single_choice"
            ref="table"
            :data="tableData"
            border
            style="width: 100%"
            highlight-current-row
            @current-change="handleSelectionChange"
          >
            <el-table-column
              type="index"
              width="50"
            />
            <el-table-column
              prop="loginName"
              label="登录名"
            />
            <el-table-column
              prop="no"
              label="工号"
            />
            <el-table-column
              prop="name"
              label="姓名"
            />
            <el-table-column
              prop="sex"
              label="性别"
              :formatter="formatterSex"
            />
            <el-table-column
              prop="nation"
              label="民族"
            />
            <el-table-column
              prop="email"
              label="电子邮件"
            />
            <el-table-column
              prop="mobile"
              label="座机"
            />
            <el-table-column
              prop="phone"
              label="手机号码"
            />
            <el-table-column
              prop="birthday"
              label="生日"
              :formatter="formatterData"
            />
          </el-table>
        </template>
        <template v-if="limit>1">
          <el-table
            key="multiple_choice"
            ref="table"
            :data="tableData"
            border
            style="width: 100%"
            tooltip-effect="dark"
            @select-all="handleSelectionChange"
            @select="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="50"
            />
            <el-table-column
              prop="loginName"
              label="登录名"
            />
            <el-table-column
              prop="no"
              label="工号"
            />
            <el-table-column
              prop="name"
              label="姓名"
            />
            <el-table-column
              prop="sex"
              label="性别"
              :formatter="formatterSex"
            />
            <el-table-column
              prop="nation"
              label="民族"
            />
            <el-table-column
              prop="email"
              label="电子邮件"
            />
            <el-table-column
              prop="mobile"
              label="座机"
            />
            <el-table-column
              prop="phone"
              label="手机号码"
            />
            <el-table-column
              prop="birthday"
              label="生日"
              :formatter="formatterData"
            />
          </el-table>
        </template>
        <pagination
          :limit.sync="userQuery.pageSize"
          :page.sync="userQuery.pageIndex"
          :total="pageTotal"
          @pagination="onSubmit"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { findTreeById } from '@/api/organize'
import { findListWhenSelect } from '@/api/user'
import { parseTime } from '@/utils'
import { isArray } from '@/utils/validate'

export default {
  name: 'UsersSelectList',
  props: {
    limit: {
      type: Number,
      require: true,
      default: 1
    },
    modalName: {
      type: String,
      default: '参会人员'
    },
    dialogData: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      filterText: '',
      orgTrees: {
        children: null,
        label: 'name'
      },
      chosedData: [],
      pageTotal: 0,
      tableData: [],
      userQuery: {
        loginName: null,
        name: null,
        pageIndex: 1,
        pageSize: 10
      },
      // 组织基本信息数据
      currentData: {
        node: {},
        data: this.initData()
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.organizeTree.filter(val)
    },
    dialogData: {
      immediate: true,
      handler(val, oldval) {
        this.chosedData = val
        if (oldval !== undefined) {
          this.$nextTick(() => {
            this.cleanSelection(val)
          })
        }
      }
    }
  },
  async mounted() {
    // 初始化选中
    this.onSubmit()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    loadOrgNode(node, resolve) {
      const id = node ? (node.data ? node.data.id : 0) : 0
      findTreeById(id).then(response => {
        resolve(response.data)
      })
    },
    onSubmit() {
      findListWhenSelect(this.userQuery).then(
        response => {
          this.tableData = response.data.list
          this.pageTotal = response.data.total
          this.userQuery.pageIndex = response.data.pageIndex
          this.$nextTick(() => {
            this.cleanSelection(this.chosedData)
          })
        }
      )
    },
    handleNodeClick(data, node) {
      this.userQuery.organizeId = data.id
      this.userQuery.pageIndex = 1
      this.currentData.node = node
      this.currentData.data = data
      this.onSubmit()
    },
    handleSelectionChange(rows) {
      if (!isArray(rows)) {
        rows = [rows]
      }
      this.$nextTick(() => { // 分页加载也会触发，这个时候tableData还是旧的会导致判断混淆，所以需要延时
        // 删掉在本页但是没选中的
        this.chosedData = this.chosedData.filter(item => {
          if (this.tableData.find(it => it.id === item.id) && !rows.find(it => it.id === item.id)) {
            return false
          }
          return true
        })
        let canAdd = true
        const toggleRows = []
        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]
          const existsRow = this.chosedData.find(it => it.id === item.id)
          if (!existsRow) {
            if (this.chosedData.length >= this.limit && this.limit > 0) {
              if (canAdd) {
                canAdd = false
                this.$message(`${this.modalName}最多配置${this.limit}个`)
              }
              toggleRows.push(item)
            } else {
              this.chosedData.push(item)
            }
          }
        }
        this.$nextTick(() => {
          if (this.chosedData.length === 0) {
            this.$message.error('请至少选择一个人员')
            return false
          }
          toggleRows.forEach(item => {
            if (this.limit === 1) {
              this.$refs.table.setCurrentRow(item)
            } else {
              this.$refs.table.toggleRowSelection(item)
            }
          })
        })
      })
    },
    formatterData(row) {
      return parseTime(row.birthday, '{y}-{m}-{d}')
    },
    formatterSex(row) {
      return row.sex === 1 ? '男' : '女'
    },
    cleanSelection(val) {
      this.$refs.table.clearSelection()
      val.forEach(row => {
        const currRow = this.tableData.find(item => item.id === row.id)
        if (currRow) {
          if (this.limit === 1) {
            this.$refs.table.setCurrentRow(currRow)
          } else {
            this.$refs.table.toggleRowSelection(currRow, true)
          }
        }
      })
    },
    removeItem(item, index) {
      const tableItem = this.tableData.find(it => it.id === item.id)
      this.chosedData.splice(index, 1)
      if (tableItem) {
        this.$refs.table.toggleRowSelection(tableItem, false)
      }
      if (this.chosedData.length === 0) {
        this.$message.error('请至少选择一个人员')
        return false
      }
    },
    initData() {
      return {
        id: null,
        code: null,
        parentCode: null,
        name: ''
      }
    },
    cancelSelect() {
      this.currentData.node = {}
      this.currentData.data = this.initData()
      this.$nextTick(() => {
        this.$refs['organizeTree'].setCurrentKey(null)
      })
      this.userQuery = {
        loginName: null,
        name: null,
        pageIndex: 1,
        pageSize: 10,
        organizeId: null
      }
      this.onSubmit()
    }
  }
}
</script>

<style scoped>
.chosed-box {
  margin-bottom: 5px;
}

.chosed-item {
  margin-right: 10px;
  margin-bottom: 5px;
}
</style>
