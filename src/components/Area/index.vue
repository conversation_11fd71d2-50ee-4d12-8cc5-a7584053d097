<template>
  <div class="area-cascarder-component">
    <el-cascader
      v-model="cascarder.currentAreaData"
      :props="cascarder.props"
      :clearable="true"
      :options="cascarder.options"
      :disabled="disabled"
      class="area-cascarder"
      @change="changeCheckedNodes"
    />
  </div>
</template>

<script>
import { findChinaAllArea } from '@/api/area'

export default {
  name: 'AreaCasCarder',
  props: {
    // 根地区的地区编码
    currentArea: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      cascarder: {
        props: {
          children: 'childrenList',
          disabled: 'isDelete',
          label: 'name',
          value: 'code',
          leaf: 'isLeaf'
        },
        options: [],
        currentAreaData: []
      }
    }
  },
  computed: {
  },
  watch: {
    'currentArea': {
      handler(newValue, oldValue) {
        this.cascarder.currentAreaData = newValue
      },
      deep: true
    }
  },
  created() {
  },
  mounted() {
    findChinaAllArea().then(resp => {
      this.cascarder.options = resp.data
    }).catch(exception => {
      console.log(exception)
    })
    this.cascarder.currentAreaData = this.currentArea
  },
  methods: {
    changeCheckedNodes() {
      this.$emit('selectedData', this.cascarder.currentAreaData)
    }
  }
}
</script>

<style lang="scss" scoped>
.area-cascarder {
  width: 100%;
}
</style>
