<template>
  <el-row>
    <el-col>
      <area-cas-carder :current-area="currentArea" @selectedData="selectedData" />
    </el-col>
  </el-row>
</template>

<script>
import AreaCasCarder from '@/components/Area/index'

export default {
  name: 'Example',
  components: {
    AreaCasCarder
  },
  data() {
    return {
      currentArea: []
      // currentArea: ['130000', '130200', '130202']
    }
  },
  methods: {
    selectedData(value) {
      console.log('-----------selectedData 返回的选中的值----------')
      console.log(value)
      console.log('---------------------')
    }
  }
}
</script>

<style scoped>

</style>
