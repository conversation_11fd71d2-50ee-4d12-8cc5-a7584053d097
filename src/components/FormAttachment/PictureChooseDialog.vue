<template>
  <el-dialog v-if="isShow" :title="title" :visible.sync="isShow" width="915px" :close-on-click-modal="false" append-to-body>
    <el-container class="container">
      <el-container class="main-container">
        <el-aside width="200px" class="aside">
          <el-tree
            ref="treeData"
            :data="attachmentTypeList"
            :props="defaultProps"
            highlight-current
            :expand-on-click-node="false"
            default-expand-all
            node-key="$treeNodeId"
            @node-click="clickNode"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node" :class="{true: 'chosed-node'}[currData && data.code === currData.code]">
              <span class="node-text" :title="node.label">{{ node.label }}</span>
            </span>
          </el-tree>
        </el-aside>
        <el-main class="main pic-list">
          <!-- <div v-infinite-scroll="pullData" class="infinite-list" style="overflow:auto"> -->
          <!-- <div v-for="(item, index) in tableData" :key="index" class="infinite-list-item">
              <img :src="item.url || 'https://syfile.hzlingluo.com/tmps/20231019/5ed8a3c0f32bc1dd93a242e9/project/202310191701111574841.png'">
            </div> -->
          <div v-for="(file, index) in tableData" :key="index" class="pic-item" :class="chosedItems.findIndex(it => it.id === file.id) > -1 ? 'chosed' : ''" @click="handleCheckItem(file)">
            <div v-if="file.fileUrl" class="el-upload-list__item-thumbnail img-box">
              <img :src="file.fileUrl">
            </div>
            <img v-else :src="file.visitUrl">
            <svg-icon class-name="chosed-icon" icon-class="chosed" />
          </div>
          <!-- </div> -->
        </el-main>
        <!-- <pagination
          :limit.sync="pageSet.pageSize"
          :page.sync="pageSet.pageIndex"
          :total="pageSet.total"
          @pagination="pullData"
        /> -->
      </el-container>
      <el-footer class="footer">
        <span>已选：</span><div class="chosed-box">
          <div v-for="(file, index) in chosedItems" :key="index" class="pic-item">
            <div v-if="file.fileUrl" class="el-upload-list__item-thumbnail img-box">
              <img :src="file.fileUrl">
            </div>
            <img v-else :src="file.visitUrl">
            <i class="el-icon-remove" @click="handleCheckItem(file)" />
          </div>
        </div>
      </el-footer>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <!-- <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" style="float: left;" @change="handleCheckAllChange">全选</el-checkbox> -->
      <el-button plain type="primary" @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import listMixin from '@/mixins/listMixin'
import enterpriseApi from '@/api/enterprise'
import { previewMethod } from '@/components/ImagePreview/index'
// import enterpriseApi from '@/api/enterprise'

export default {
  mixins: [listMixin],
  props: {
    /**
     * 附件类型
    */
    attachmentTypeList: {
      type: Array,
      default: () => []
    },
    // 选择回调
    onChosed: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      isShow: false,
      title: '选择附件',
      defaultProps: {
        children: 'children',
        label: 'name',
        // disabled(data, node) {
        //   return node.level !== 2
        // }
      },
      chosedItems: [],
      checkAll: false,
      isIndeterminate: false,
      currData: null,
    }
  },
  computed: {
  },
  created() {},
  mounted() {
  },
  methods: {
    // previewMethod(index, list) {
    //   previewMethod(list.map(item => item.visitUrl), index)
    // },
    clickNode(row) {
      if (this.currData !== row) {
        this.currData = row
        this.pullData()
      }
    },
    async pullData() {
      // if (this.pageSet.pageIndex > 0 && this.tableData.length >= this.pageSet.total) { // 已经到页底
      //   return
      // }
      // this.pageSet.pageIndex++
      this.isLoading = true
      enterpriseApi.getDetail().then(response => {
        this.tableData = (response.data.files || []).filter(item => item.attachmentTypeCode === this.currData.code).map(item => {
          switch ((item.postfix || '').toLowerCase()) {
            case 'pdf':
              item.fileUrl = require('./imgs/pdf.png')
              break
          }
          return item
        })
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 显示弹框
    show() {
      if (!this.attachmentTypeList || !this.attachmentTypeList.length) {
        this.$message.error('请先配置附件类型')
        return
      }
      this.isShow = true
      this.tableData = []
      this.chosedItems = []
      // this.pageSet.pageIndex = 0
      this.isIndeterminate = false
      this.checkAll = false
      this.currData = this.attachmentTypeList[0]
      this.pullData()
    },
    // rowClick(row) {
    //   if (!this.chosedItems || this.chosedItems.code !== row.code) {
    //     this.chosedItems = row
    //   } else {
    //     this.chosedItems = null
    //   }
    // },
    // rowDblClick(row) {
    //   this.chosedItems = row
    //   this.confirmHandle()
    // },
    handlePictureCardPreview({ url }) {
      console.log('handlePictureCardPreview', url)
      const picUrls = this.tableData.map(item => item.url)
      const index = picUrls.findIndex(item => item === url)
      console.log(picUrls, index)
      this.$previewImage(picUrls, index)
    },
    confirmHandle() {
      // const chosedItems = this.tableData.filter(item => item.checked)
      if (!this.chosedItems.length) {
        this.$message.info('请选择一条数据')
        return
      }
      this.isShow = false
      this.onChosed && this.onChosed(this.chosedItems)
    },
    // tableRowClassName({ row, rowIndex }) {
    //   if (this.chosedItems && row.code === this.chosedItems.code) {
    //     return 'chosed-row'
    //   }
    //   return ''
    // },
    // handleCheckAllChange(val) {
    //   this.tableData.forEach(item => (item.checked = val))
    //   this.isIndeterminate = false
    // },
    handleCheckItem(item) {
      const index = this.chosedItems.findIndex(it => it.id === item.id)
      if (index > -1) {
        this.chosedItems.splice(index, 1)
      } else {
        this.chosedItems.push(item)
      }
      // const checkedCount = this.tableData.filter(item => item.checked).length
      // this.checkAll = checkedCount === this.tableData.length
      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.tableData.length
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 10px;
  .container {
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    .main-container {
      height: 300px;
      overflow: hidden;
      border-radius: 1px solid red;
      // display: flex;
      .aside {
        margin-bottom: 0;
        background-color: #fff;
        padding: 10px 0;
        font-size: 13px;
        border-right: 1px solid #e4e7ed;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
        // text-overflow: ellipsis;
        // overflow: hidden;
        // white-space: nowrap;
        .custom-tree-node {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .main {
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
        padding: 15px 5px 5px 15px;
      }
    }
    .footer {
      border-top: 1px solid #e4e7ed;
      padding: 10px 0px 10px 10px;
      height: auto !important;
      display: flex;
      .chosed-box {
        flex: 1;
        // border: 1px solid red;
        display: flex;
        flex-wrap: wrap;
        .pic-item {
          width: 50px;
          height: 50px;
          border-radius: 3px;
          .el-icon-remove {
            position: absolute;
            right: -7px;
            top: -7px;
            font-size: 16px;
            color: #f56c6c;
            cursor: pointer;
          }
        }
      }
    }
  }
  .chosed-row td{
    background: #f0f9eb !important;
  }
  .disabled-row td{
    background: #dfe4ed !important;
  }
}
.pic-list {
  display: flex;
  flex-wrap: wrap;
  .pic-item {
    width: 100px;
    height: 100px;
    border-radius: 5px;
    cursor: pointer;
    .chosed-icon {
      display: none;
      position: absolute;
      // overflow: hidden;
      right: -2px;
      bottom: -2px;
      z-index: 100;
      color: #519e50;
      font-size: 25px;
    }
    &.chosed {
      // border: 1px solid green;
      .chosed-icon {
        display: block;
      }
      // chosed
    }
  }
}
.pic-item {
  position: relative;
  width: 50px;
  height: 50px;
  border: 1px solid #e4e7ed;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 10px;
  img {
    max-width: 100%;
    max-height: 100%;
  }
  .el-icon-remove {
    position: absolute;
    right: -7px;
    top: -7px;
    font-size: 16px;
    color: #f56c6c;
    cursor: pointer;
  }
  .el-upload-list__item-actions {
    opacity: 0;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    cursor: pointer;
    .el-icon-zoom-in {
      cursor: pointer;
      color: #fff;
      font-size: 20px;
    }
    &:hover {
      opacity: 1;
    }
  }
  .img-box {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      max-width: 60%;
      max-height: 60%;
    }
  }
}
</style>
