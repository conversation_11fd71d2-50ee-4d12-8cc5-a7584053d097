<template>
  <el-form class="files-content">
    <!-- <div>{{ fileList }}--</div>
    <div>{{ attachmentType }}--</div>
    <div>{{ filterFileList }}--</div> -->
    <!-- {{ deleteFiles }} {{ filterFileList.length }} -->
    <ul class="picture-component el-upload-list el-upload-list--picture-card">
      <li
        v-for="(file, index) in filterFileList"
        :key="index"
        class="el-upload-list__item is-success"
        :class="{
          big: file.attachmentType && (file.attachmentType.displayCompanyName || file.attachmentType.expirationDate),
        }"
      >
        <svg-icon v-if="file.deleted" class-name="deleted-icon" icon-class="deleted" />
        <div v-if="file.fileUrl" class="el-upload-list__item-thumbnail img-box">
          <img
            :src="file.fileUrl"
          >
        </div>
        <img
          v-else
          class="el-upload-list__item-thumbnail"
          :src="file.url"
        >
        <span class="el-upload-list__item-actions">
          <span
            v-if="file.fileUrl"
            class="el-upload-list__item-preview"
            @click="download(file, index)"
          >
            <i class="el-icon-download" />
          </span>
          <span
            v-else
            class="el-upload-list__item-preview"
            @click="previewMethod(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="onRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
        <div v-if="file.attachmentType" class="tip-box">
          <div class="pic-name" :title="file.attachmentType.name">{{ file.attachmentType.name }}</div>
          <template v-if="file.attachmentType.displayCompanyName || file.attachmentType.expirationDate">
            <template v-if="file.attachmentType.displayCompanyName">
              <el-input v-model="file.businessData.companyName" class="mt5" size="mini" clearable placeholder="请输入公司名称" />
            </template>
            <template v-if="file.attachmentType.displayValidityPeriod">
              <el-date-picker
                v-model="file.businessData.expirationDate"
                class="mt5"
                size="mini"
                type="date"
                value-format="timestamp"
                placeholder="选择截止日期"
              />
            </template>
          </template>
        </div>
      </li>
    </ul>
    <template v-if="!disabled">
      <el-upload
        v-for="(item, index) in fixedAttachmentTypeList"
        :key="index"
        ref="fixedUploadRef"
        :file-list="[]"
        :multiple="item.maximumQuantity !== 1"
        action="#"
        class="picture-component single mr15"
        :show-file-list="false"
        :limit="getLimit(item)"
        :on-change="onChange"
        :http-request="onHttpRequest"
        :on-success="onSuccess"
        :before-upload="onBeforeUpload"
        list-type="picture-card"
        :auto-upload="true"
        :accept="item.type"
      >
        <!-- accept=".jpeg,.jpg,.gif,.png" -->
        <!-- 只有单个附件类型，上传就不要再选择类型了 -->
        <div slot="default" @click="currAttachmentType = item">
          <i id="uploadIcon" class="el-icon-plus" />
        </div>
        <div slot="tip" class="upload-tip-box">
          {{ item.name }}
        </div>
      </el-upload>
      <el-upload
        v-if="isShowCommonUploadCtrl"
        ref="uploadRef"
        :file-list="[]"
        :multiple="currAttachmentType ? currAttachmentType.maximumQuantity !== 1 : true"
        action="#"
        class="picture-component single mr15"
        :show-file-list="false"
        :limit="getLimit(currAttachmentType)"
        :on-change="onChange"
        :http-request="onHttpRequest"
        :on-success="onSuccess"
        :before-upload="onBeforeUpload"
        list-type="picture-card"
        :auto-upload="true"
      >
        <!-- accept=".jpeg,.jpg,.gif,.png" -->
        <!-- 只有单个附件类型，上传就不要再选择类型了 -->
        <template v-if="attachmentType">
          <div slot="default" @click="currAttachmentType = attachmentType">
            <i id="uploadIcon" class="el-icon-plus" />
          </div>
          <!-- <div slot="tip" class="upload-tip-box">
            {{ attachmentType.name }}
          </div> -->
        </template>
        <div v-else-if="attachmentTypeList.length === 1" slot="default" @click="currAttachmentType = attachmentTypeList[0]">
          <i id="uploadIcon" class="el-icon-plus" />
        </div>
        <div v-else slot="default" @click.stop>
          <el-dropdown trigger="click" style="width:100%;" @command="chooseAttachmentType">
            <div class="el-dropdown-link" @click="checkCanChooseFile">
              <i id="uploadIcon" class="el-icon-plus" />
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in attachmentTypeDropdownList" :key="item.code" :command="item">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!-- <template slot="file" slot-scope="{file}">
          <img
            class="el-upload-list__item-thumbnail"
            :src="file.url"
          >
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="previewMethod(file)"
            >
              <i class="el-icon-zoom-in" />
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
              @click="onRemove(file)"
            >
              <i class="el-icon-delete" />
            </span>
          </span>
          <div v-if="file.attachmentType" class="tip-box">
            <template v-if="file.attachmentType.displayCompanyName || file.attachmentType.expirationDate">
              <template v-if="file.attachmentType.displayCompanyName">
                <el-input v-model="file.businessData.companyName" class="mt5" size="mini" placeholder="请输入公司名称" />
              </template>
              <div v-if="file.attachmentType.displayValidityPeriod">
                <el-date-picker
                  v-model="file.businessData.expirationDate"
                  class="mt5"
                  size="mini"
                  type="date"
                  placeholder="选择截止日期"
                />
              </div>
            </template>
            <div v-else class="pic-name" :title="file.attachmentType.name">{{ file.attachmentType.name }}</div>
          </div>
        </template> -->
      </el-upload>
    </template>
    <PictureChooseDialog ref="pictureChooseDialogRef" :attachment-type-list="attachmentTypeList" :on-chosed="onChosed" />
  </el-form>
</template>

<script>
import productCategoryApi from '@/api/productCategory'
import { uploadBusinessFile, updateBusinessFile } from '@/api/file'
import PictureChooseDialog from './PictureChooseDialog'
import { previewMethod } from '@/components/ImagePreview/index'
import { mapGetters } from 'vuex'
export default {
  components: { PictureChooseDialog },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 带格式化的文件数组
    files: {
      required: false,
      type: [Array],
      default: () => []
    },
    // 附件所属品类code
    productCategoryCodeList: {
      required: false,
      type: Array,
      default: () => []
    },
    // 指定附件类型，用于资质文件上传的场景（productCategoryCodeList和attachmentType选传一个）
    attachmentType: {
      required: false,
      type: Object,
      default: () => null
    },
    // 已删除文件
    deleteFiles: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currAttachmentType: null,
      productCategoryAttachmentObj: {}, // 品类对应的附件类型，避免重复请求
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
    // 占位附件类型上传按钮
    fixedAttachmentTypeList() {
      if (this.attachmentType) {
        return []
      }
      return this.attachmentTypeList.filter(item => item.showPlaceholder).filter(item => {
        // 已上传过了就不显示占位
        return !this.fileList.find(it => it.attachmentType && it.attachmentType.code === item.code)
      })
    },
    // 是否显示通用的上传空间
    isShowCommonUploadCtrl() {
      if (this.attachmentTypeList.length === this.fixedAttachmentTypeList.length) {
        return false
      }
      return true
    },
    // 如果关联了特定附件类型，过滤后的附件类型对应的附件数据
    filterFileList() {
      let list = this.fileList
      if (this.$isAdmin) {
        const deleteFiles = (this.deleteFiles || []).map(item => {
          return {
            ...item,
            deleted: true,
            attachmentType: this.attachmentTypeList.find(it => it.code === item.attachmentTypeCode),
            url: item.visitUrl,
            businessData: item.businessData || {
              companyName: '',
              expirationDate: ''
            },
          }
        })
        list = list.concat(deleteFiles)
      }
      if (this.attachmentType) {
        list = list.filter(file => file.attachmentType && file.attachmentType.code === this.attachmentType.code)
      } else {
        list = list.filter(file => file.attachmentType && this.attachmentTypeList.find(iii => iii.code === file.attachmentType.code))
      }
      // 管理端 还要去掉没权限看的数据
      if (this.$isAdmin) {
        list = list.filter(file => {
          const { authorizationRole } = file.attachmentType || {}
          const { roles } = this.$store.state.user
          if (authorizationRole && authorizationRole.length) {
            const hasPermit = roles.find(item => !!authorizationRole.find(it => it.code === item.code))
            // if (!hasPermit) {
            //   console.log('hasPermit', hasPermit, '-', file.attachmentType, authorizationRole)
            // }
            return hasPermit
          }
          // console.log('attachmentType', authorizationRole, roles)
          return true
        })
      }
      list = list.map(item => {
        switch ((item.postfix || '').toLowerCase()) {
          case 'pdf':
            item.fileUrl = require('./imgs/pdf.png')
            break
          case 'xls':
          case 'xlsx':
            item.fileUrl = require('./imgs/excel.png')
            break
          case 'doc':
          case 'docx':
            item.fileUrl = require('./imgs/word.png')
            break
          case 'ppt':
          case 'pptx':
            item.fileUrl = require('./imgs/ppt.png')
            break
          case 'mp4':
            item.fileUrl = require('./imgs/mp4.png')
            break
          // default:
          //   item.fileUrl = require('./imgs/unknown.png')
          //   break
        }
        return item
      })
      return list
    },
    // 所有整合后的附件类型列表，并去重
    attachmentTypeList() {
      let list = []
      if (this.attachmentType) {
        return [this.attachmentType]
      } else {
        (this.productCategoryCodeList || []).map(code => {
          // console.log('---', code, this.productCategoryAttachmentObj[code])
          list = [...list, ...(this.productCategoryAttachmentObj[code] || []).filter((item, index) => {
            // 去重一下
            return !list.find(it => it.code === item.code)
          })]
        })
        return list.map(item => {
          item.files = (this.files || []).filter(item => item.attachmentTypeCode === item.code)
          return item
        })
      }
    },
    // 附件类型下拉选项（排除已经满了的）
    attachmentTypeDropdownList() {
      return this.attachmentTypeList.filter(item => {
        return item.maximumQuantity <= 1 || (this.fileList.filter(file => file.attachmentType && file.attachmentType.code === item.code).length < item.maximumQuantity)
      })
    },
    combinationData() {
      return {
        attachmentTypeList: this.attachmentTypeList,
        files: this.files || []
      }
    },
  },
  watch: {
    files: {
      deep: true,
      immediate: true,
      handler(val) {
        // console.log('files change')
        this.fileList = (this.files || []).map(item => {
          return {
            ...item,
            attachmentType: this.attachmentTypeList.find(it => it.code === item.attachmentTypeCode),
            url: item.visitUrl,
            businessData: item.businessData || {
              companyName: '',
              expirationDate: ''
            }
          }
        }) || []
      }
    },
    attachmentTypeList: {
      deep: true,
      immediate: true,
      handler(val) {
        // console.log('attachmentTypeList change')
        this.fileList.forEach(item => {
          item.attachmentType = this.attachmentTypeList.find(it => it.code === item.attachmentTypeCode)
        })
      }
    },
    fileList() {
      const pictureIds = this.fileList.filter(item => item.id).map(item => item.id).join(',')
      this.$emit('input', pictureIds)
    },
    // 对应的品类列表
    productCategoryCodeList: {
      immediate: true,
      deep: false,
      async handler(newList, oldList) {
        if (newList && newList.length) {
          for (let i = 0; i < newList.length; i++) {
            await this.getProductCategoryDetail(newList[i])
          }
        }
      }
    }
  },
  mounted() {
    this.$eventBus.$on('submit-attachment', this.submitAttachment)
  },
  unmounted() {
    this.$eventBus.$off('submit-attachment', this.submitAttachment)
  },
  methods: {
    isPic(url) {
      url = (url || '').toLowerCase()
      return /\.(gif|jpg|jpeg|png|webp|svg)$/.test(url)
    },
    getLimit(attachmentType) {
      if (!attachmentType) {
        return 0
      }
      // console.log(this.fileList.map(item => JSON.stringify(item.attachmentType) + JSON.stringify(item)))
      const uploadCount = this.fileList.filter(file => file.attachmentType && file.attachmentType.code === attachmentType.code).length
      return attachmentType.maximumQuantity - uploadCount
    },
    // 表单提交的时候，更新一下附件，业务数据businessData
    submitAttachment() {
      this.filterFileList.forEach(file => {
        const attachmentType = this.attachmentType || this.attachmentTypeList.find(it => it.code === file.attachmentTypeCode)
        if (attachmentType.displayCompanyName || attachmentType.displayValidityPeriod) {
          // console.log('submit-attachment-file', file, attachmentType)
          updateBusinessFile(file)
        }
      })
    },
    checkCanChooseFile() {
      if (!this.productCategoryCodeList.length) {
        this.$message.warning('请先选择商品类目')
        return
      }
      if (!this.attachmentTypeList.length) {
        this.$message.warning('当前品类下未配置附件类型')
        return
      }
    },
    chooseAttachmentType(attachmentType) {
      this.currAttachmentType = attachmentType
      this.$refs.uploadRef.$refs['upload-inner'].handleClick()
    },
    previewMethod(file) {
      const filterFileList = this.filterFileList.filter(item => !item.fileUrl)
      const index = filterFileList.indexOf(file)
      const imgList = filterFileList.map(item => item.url)
      previewMethod(imgList, index)
    },
    download(file) {
      window.open(file.url, '_blank')
    },
    // 获取品类下的附件列表
    getProductCategoryDetail(code) {
      if (!code || this.productCategoryAttachmentObj[code]) {
        return
      }
      // alert(code === undefined || code === null || code === '')
      productCategoryApi.getDetail(code).then(response => {
        this.$set(this.productCategoryAttachmentObj, code, response.data || [])
      })
    },
    async onHttpRequest({ file }) {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('attachmentTypeCode', this.currAttachmentType.code)
      formData.append('attachmentTypeName', this.currAttachmentType.name)
      formData.append('businessData.expirationDate', '')
      formData.append('businessData.companyName', '')
      formData.append('file', file)
      const resData = await uploadBusinessFile(formData)
      if (resData.code !== 200 || !resData.data.visitUrl) {
        this.$message({
          message: resData.data.msg || '上传失败',
          type: 'error'
        })
        throw new Error('上传失败')
      }
      this.fileList.push({
        ...resData.data,
        attachmentType: this.attachmentTypeList.find(it => it.code === resData.data.attachmentTypeCode),
        url: resData.data.visitUrl
      })
      return {
        id: resData.data.id,
        aaa: 3223,
        url: resData.data.visitUrl // 'https://syfile.hzlingluo.com/tmps/20231019/5ed8a3c0f32bc1dd93a242e9/project/202310191701111574841.png'
      }
    },
    onSuccess(data) {
    //   console.log('onSuccess', data)
    },
    onBeforeUpload(file) {
    //   console.log('onBeforeUpload', file)
      return true
    },
    // handleDownload(file)
    onRemove(file,) {
      const index = this.fileList.findIndex(v => v.url === file.url)
      // console.log(index, 'remove')
      this.fileList.splice(index, 1)
      // console.log(file, this.fileList)
      // if (index + 1 > this.fileList.length) {
      // } else {
      //   item.url = ''
      // }
    },
    onChange(file, fileList) {
      Object.assign(file, {
        attachmentTypeCode: this.currAttachmentType.code,
        attachmentTypeName: this.currAttachmentType.name,
        businessData: {
          expirationDate: '',
          companyName: ''
        }
      })
      file.attachmentType = { ...this.currAttachmentType }
      // this.fileList.push(file)
      // console.log('onChange', file, fileList)
    },
    choosePics() {
      this.$refs.pictureChooseDialogRef.show()
    },
    // 选择图片回调
    onChosed(pics) {
      console.log('onChosed', pics)
      if (pics && pics.length) {
        this.fileList.push(...pics.map(item => {
          return {
            ...item,
            attachmentType: this.attachmentTypeList.find(it => it.code === item.attachmentTypeCode),
            url: item.visitUrl
          }
        }))
      }
      // console.log(this.value)
    },
    async getFiles() {
      const pictureIds = this.fileList.filter(item => item.id).map(item => item.id).join(',')
      return {
        pictureIds,
        files: this.fileList
      }
    }
    // updateVal() {
    //   this.$emit('input', [])
    // },
  }
}
</script>

<style lang="scss" scoped>
$smallWidth: 120px;
$bigWidth: 160px;
.files-content {
  display: block;
  width: 100%;

  ::v-deep {
    .el-upload-list__item.el-list-leave-active{// 去掉动画，否则很恶心的
      transition: none 0s !important;
    }
    .el-upload--picture-card, .el-upload-list--picture-card .el-upload-list__item {
      width: auto;
      height: auto;
      min-width: $smallWidth;
      // line-height: $smallWidth;
      overflow: visible;
      &.el-upload-list__item.big {
        width: $bigWidth;
        .el-upload-list__item-thumbnail, .el-input, .tip-box {
          width: $bigWidth;
        }
      }
      .deleted-icon {
        position: absolute;
        right: 0;
        top: 0;
        width: 50px;
        height: 50px;
        opacity: 0.8;
      }
      .el-upload-list__item-thumbnail {
        width: $smallWidth;
        height: $smallWidth;
      }
      &.el-upload--picture-card {
        height: $smallWidth;
        line-height: $smallWidth;
      }
    }
    .picture-component {
      display: inline-block;
      vertical-align: top;
      // height: 180px;
      .el-upload-list__item {
        font-size: 0;
        // overflow: visible;
        border: 0;
        vertical-align: top;
        .pic-name {
          margin-top: 5px;
          line-height: 20px;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 13px;
        }
        .el-upload-list__item-thumbnail {
          border: 1px solid #c0ccda;
          overflow: hidden;
          border-radius: 6px;
        }
        .el-upload-list__item-actions {
          border-radius: 6px;
          height: $smallWidth;
        }
        .tip-box {
          // position: absolute;
          width: $smallWidth;
          overflow: hidden;
          line-height: 1;
          // top: 100%;
          // left: 0;
          font-size: 13px;
          // input {

          // }
        }
      }
    }
  }
}
.upload-tip-box {
  margin-top: 5px;
  // border: 1px solid red;
  // position: absolute;
  // width: 100%;
  width: $smallWidth;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 1;
  text-align: center;
  // top: 100%;
  // left: 0;
  font-size: 13px;
  // input {

  // }
}
.img-box {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    max-width: 60%;
    max-height: 60%;
  }
}
</style>
