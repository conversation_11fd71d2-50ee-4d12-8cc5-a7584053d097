<template>
  <el-select
    ref="dyProductCategoriesRef"
    v-model="modalValue"
    value-key="code"
    multiple
    placeholder="请选择"
    clearable
    style="width: 100%;"
    v-bind="_props"
  >
    <el-option
      v-for="item in $enums['dyProductCategories'].toArr().map(item => ({
        code: item.value,
        name: item.label
      }))"
      :key="item.code"
      :label="item.name"
      :value="valueType === 'code' ? item.code : item"
    />
  </el-select>
</template>
<script>
import { Select } from 'element-ui'
import productCategoryApi from '@/api/productCategory'
export default {
  // extends: Select,
  props: {
    ...Select.props,
    limit: {
      type: Number,
      default: 0
    },
    value: {
      type: [Array],
      default: () => []
    },
    /**
     * 值类型，code或者object
    */
    valueType: {
      type: String,
      default: 'object'
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
    }
  },
  computed: {
    modalValue: {
      get() {
        return this.value
      },
      set(val) {
        if (this.limit > 0 && val.length >= this.limit) {
          while (val.length > this.limit) {
            val.shift()
          }
          this.$nextTick(() => {
            this.$refs.dyProductCategoriesRef.blur()
          })
        }
        this.$emit('input', val)
      }
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
