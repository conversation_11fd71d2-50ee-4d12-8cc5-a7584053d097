<template>
  <el-dialog v-if="isShow" title="公司信息" :visible.sync="isShow" width="400px" :close-on-click-modal="false" :before-close="close" append-to-body>
    <el-form ref="form" class="oprate-form" :model="formData" :rules="rules" label-width="130px" style="padding-right: 20px;">
      <el-row>
        <el-col :span="24">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="formData.companyName" clearable placeholder="请输入公司名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="formData.unifiedSocialCreditCode" clearable placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="dialog-footer">
      <el-button plain type="primary" @click="close">取 消</el-button>
      <el-button type="primary" @click="saveHandle">保 存</el-button>
    </div>
  </el-dialog>
</template>
<script>
// import { memberTypeList } from '@/api/dict-config'
import enterpriseApi from '@/api/enterprise'

export default {
  components: {
  },
  // mixins: [listMixin],
  props: {},
  data() {
    const defaultFormData = {
      companyName: null,
      unifiedSocialCreditCode: null
    }
    return {
      isShow: false,
      defaultFormData,
      formData: JSON.parse(JSON.stringify(defaultFormData)),
      dataList: [],
      rules: {
        companyName: [{ required: true, message: '请输入公司名称', trigger: 'change' }],
        unifiedSocialCreditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'change' }]
      },
      // memberTypeList,
      // 增删改查集合
      apis: {
        ...enterpriseApi
      }
    }
  },
  created() {},
  mounted() {
    // this.initDict()
  },
  methods: {
    // 显示弹框
    show(dataList, index) {
      console.log(index)
      this.dataList = JSON.parse(JSON.stringify(dataList)) || []
      if (index !== undefined) {
        this.formData = this.dataList[index]
      } else {
        this.formData = {}
        this.dataList.push(this.formData)
      }
      this.isShow = true
    },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 因为接口是合并的，这里重新读取企业信息来更新
          this.apis.getDetail().then(response => {
            this.apis.update({
              ...response.data,
              companies: this.dataList
            }).then(res => {
              this.$message.success('保存成功')
              this.$emit('success', res.data)
              this.close()
              // this.close()
            }).finally(() => {
              this.isSubmiting = false
            })
          })
        }
      })
    },
    close() {
      this.isShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
// ::v-deep .el-dialog__body {
//   padding: 0;
// }
.dialog-footer {
  text-align: right;
}
</style>
