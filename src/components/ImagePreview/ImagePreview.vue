<template>
  <el-dialog
    v-if="options && options.inline"
    ref="dialogRef"
    v-el-drag-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
    :show-close="true"
    :modal="true"
    :fullscreen="isFullscreen"
    append-to-body
    width="800px"
    class="image-preview-wrap"
    @close="close"
  >
    <div slot="title" class="header"><i class="el-icon-full-screen" @click="toggleFullscreen()" /></div>
    <!-- <div class="btn-options-container">
      <div class="close-btn" @click="close">
        <i style="position: absolute;left: 20px;bottom: 20px;color: #FFF;" class="el-icon-close" />
      </div>
    </div> -->
    <div class="dialog-body">
      <div class="icon-box left">
        <i class="icon el-icon-arrow-left" @click="viewer.prev()" />
      </div>
      <div class="icon-box right">
        <i class="icon el-icon-arrow-right" @click="viewer.next()" />
      </div>
      <div v-show="false" id="viewjsImages">
        <img v-for="(src, index) in images" :key="index" :src="src">
      </div>
    </div>
  </el-dialog>
  <div v-else v-show="false" id="viewjsImages">
    <img v-for="(src, index) in images" :key="index" :src="src">
  </div>
</template>
<script>
// import draggable from 'vuedraggable'
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'

export default {
  name: 'ImagePreview',
  // components: {
  //   draggable
  // },
  props: {
    images: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      viewer: null,
      isFullscreen: false,
    }
  },
  mounted() {
    // Vue.nextTick().then(() => {
    //   // this.drag(document.getElementById('image-preview-wrap'))
    // })
  },
  destroyed() {
    this.viewer.destroy()
  },
  methods: {
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      if (this.isFullscreen) {
        this.$nextTick(() => {
          const dialog = document.querySelector('.el-dialog.is-fullscreen')
          dialog.style.left = 0
          dialog.style.top = 0
        })
      }
      this.viewer.destroy()
      this.init()
    },
    // inline模式键盘会失效，要自己实现
    keyEvent(e) {
      // console.log(e.keyCode)
      if (e.keyCode === 27) { // esc
        this.close()
      } else if (e.keyCode === 37) { // 左
        this.viewer.prev()
      } else if (e.keyCode === 39) { // 右
        this.viewer.next()
      }
    },
    init() {
      this.$nextTick(() => {
        this.viewer = new Viewer(document.getElementById('viewjsImages'), this.options)
        this.viewer.show()
        if (this.options.inline) {
          document.addEventListener('keyup', this.keyEvent)
        }
      })
    },
    close() {
      this.viewer.hide()
      this.$emit('close')
      if (this.options.inline) {
        document.removeEventListener('keyup', this.keyEvent)
      }
    }
    // 拖动方法
    // drag(oDiv) {
    //   oDiv.onmousedown = function(ev) {
    //     var oEvent = ev || event

    //     var disX = oEvent.clientX - oDiv.offsetLeft
    //     var disY = oEvent.clientY - oDiv.offsetTop

    //     document.onmousemove = function(ev) {
    //       var oEvent = ev || event
    //       oDiv.style.left = oEvent.clientX - disX + 'px'
    //       oDiv.style.top = oEvent.clientY - disY + 'px'
    //     }
    //     document.onmouseup = function() {
    //       document.onmousemove = null
    //       document.onmouseup = null
    //       oDiv.releaseCapture && oDiv.releaseCapture()
    //     }
    //     oDiv.setCapture && oDiv.setCapture()
    //     return false
    //   }
    // }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .is-fullscreen {
  &.el-dialog {
    height: 100% !important;
  }
}
.image-preview-wrap {
  // position: fixed;
  // height: 100%;
  // width: 100%;
  // left: 0;
  // top: 0;
  // z-index: 9999;
  ::v-deep .el-dialog {
    height: 500px;
    display: flex;
    flex-direction: column;
    .el-dialog__header {
      padding: 10px 20px;
      .el-icon-full-screen {
        font-weight: bolder;
        font-size: 14px;
        // position: absolute;
        // float: right;
      }
      .el-dialog__headerbtn {
        top: 8px;
        right: 12px;
      }
    }
    .el-dialog__body {
      flex: 1;
      padding: 0;
      background-color: rgba(0, 0, 0, 0.1);
      .dialog-body {
        position: relative;
        height: 100%;
        .icon-box {
          position: absolute;
          top: 0;
          bottom: 50px;
          z-index: 100;
          // background-color: red;
          width: 150px;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 1s;
          cursor: pointer;
          &:hover {
            opacity: 1;
          }
          &.left {
            left: 0;
          }
          &.right {
            right: 0;
          }
          .icon {
            color: #fff;
            font-size: 30px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .viewer-container {
          // background-color: rgba(0, 0, 0, 0.1);
          background-color: transparent;
        }
      }
    }
  }
  .btn-options-container {
    .close-btn {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      cursor: pointer;
      height: 80px;
      overflow: hidden;
      position: absolute;
      z-index: 1;
      right: -40px;
      top: -40px;
      transition: background-color 0.15s;
      width: 80px;
    }
  }
}
</style>
