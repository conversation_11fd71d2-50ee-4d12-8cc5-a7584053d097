import Vue from 'vue'
import VueImagePreview from './ImagePreview'
import Viewer from 'viewerjs'

const ImagePreview = {}
let instance = null
let options = {}
// 卸载实例
function destroyInstance() {
  if (!instance) {
    return
  }
  document.body.removeChild(instance.$el)
  instance.$destroy()
  instance = null
}
export const previewMethod = (images = [], initialViewIndex) => {
  if (typeof images === 'string' && !!images) {
    images = [images]
  }
  if (!images || !images.length) {
    return
  }
  if (initialViewIndex < 0) {
    initialViewIndex = 0
  }
  destroyInstance()
  instance = new (Vue.extend(VueImagePreview))({
    // el: document.createElement('div')
  }).$mount()
  document.body.appendChild(instance.$el)
  options = Object.assign({
    className: 'viewer-custom',
    initialViewIndex: 0,
    inline: true,
    // title: true,
    button: false,
    // navbar: true,
    // tooltip: true,
    // movable: true,
    // zoomable: true,
    // rotatable: true,
    // scalable: true,
    // transition: true,
    // fullscreen: false,
    // keyboard: true,
    // url: 'data-source',
    // toolbar: {
    //   zoomIn: toolbarOption,
    //   zoomOut: toolbarOption,
    //   oneToOne: toolbarOption,
    //   reset: toolbarOption,
    //   prev: toolbarOption,
    //   // play: {
    //   //   show: 4,
    //   //   size: 'large',
    //   // },
    //   next: toolbarOption,
    //   rotateLeft: toolbarOption,
    //   rotateRight: toolbarOption
    // },
    // ready: function() {
    //   // this.viewer.zoomTo(1);
    // },
    hide() {
      destroyInstance()
    },
    viewed() {
      // this.viewer.zoomTo(1)
    }
  }, options, { initialViewIndex })

  instance.images = images || []
  instance.options = options
  instance.$on('close', visible => {
    destroyInstance()
  })
  instance.init()

  return instance
}

export default ImagePreview.install = (Vue, opts) => {
  options = opts
  Vue.$previewImage = previewMethod
  Vue.prototype.$previewImage = previewMethod
}
