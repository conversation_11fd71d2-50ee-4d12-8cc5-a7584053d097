<template>
  <div>
    <el-row :gutter="10">
      <el-col>
        <el-tree
          ref="organizeTree"
          :props="orgTrees"
          node-key="id"
          class="filter-tree"
          lazy
          :load="loadOrgNode"
          highlight-current
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { findTreeById } from '@/api/organize'
import { findListWhenSelect } from '@/api/user'
import { parseTime } from '@/utils'
import { isArray } from '@/utils/validate'

export default {
  name: 'OrganizeSelectList',
  props: {
    limit: {
      type: Number,
      require: true,
      default: 1
    },
    modalName: {
      type: String,
      default: '参会人员'
    },
    dialogData: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      filterText: '',
      orgTrees: {
        children: null,
        label: 'name'
      },
      chosedData: [],
      pageTotal: 0,
      tableData: [],
      userQuery: {
        loginName: null,
        name: null,
        currentPage: 1,
        pageSize: 20
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.organizeTree.filter(val)
    },
    dialogData: {
      immediate: true,
      handler(val, oldval) {
        this.chosedData = val
        if (oldval !== undefined) {
          this.$nextTick(() => {
            this.$refs.organizeTree.getCheckedKeys()
            this.cleanSelection(val)
          })
        }
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    loadOrgNode(node, resolve) {
      const id = node ? (node.data ? node.data.id : 0) : 0
      findTreeById(id).then(response => {
        resolve(response.data)
      })
    },
    onSubmit() {
      findListWhenSelect(this.userQuery).then(
        response => {
          this.tableData = response.data.list
          this.pageTotal = response.data.total
          this.userQuery.pageIndex = response.data.pageIndex
          this.$nextTick(() => {
            this.cleanSelection(this.chosedData)
          })
        }
      )
    },
    handleCurrentChange(val) {
      this.userQuery.currentPage = val
      this.onSubmit()
    },
    handleNodeClick(data) {
      this.userQuery.organizeId = data.id
      this.userQuery.currentPage = 1
      this.onSubmit()
      this.chosedData = []
      this.chosedData.push({ id: data.id, name: data.name })
    },
    handleSelectionChange(rows) {
      if (!isArray(rows)) {
        rows = [rows]
      }
      this.$nextTick(() => { // 分页加载也会触发，这个时候tableData还是旧的会导致判断混淆，所以需要延时
        // 删掉在本页但是没选中的
        this.chosedData = this.chosedData.filter(item => {
          if (this.tableData.find(it => it.id === item.id) && !rows.find(it => it.id === item.id)) {
            return false
          }
          return true
        })
        let canAdd = true
        const toggleRows = []
        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]
          const existsRow = this.chosedData.find(it => it.id === item.id)
          if (!existsRow) {
            if (this.chosedData.length >= this.limit && this.limit > 0) {
              if (canAdd) {
                canAdd = false
                this.$message(`${this.modalName}最多配置${this.limit}个`)
              }
              toggleRows.push(item)
            } else {
              this.chosedData.push(item)
            }
          }
        }
        this.$nextTick(() => {
          if (this.chosedData.length === 0) {
            this.$message.error('请至少选择一个人员')
            return false
          }
          toggleRows.forEach(item => {
            if (this.limit === 1) {
              this.$refs.table.setCurrentRow(item)
            } else {
              this.$refs.table.toggleRowSelection(item)
            }
          })
        })
      })
    },
    formatterData(row) {
      return parseTime(row.birthday, '{y}-{m}-{d}')
    },
    formatterSex(row) {
      return row.sex === 1 ? '男' : '女'
    },
    cleanSelection(val) {
      val.forEach(row => {
        const currRow = this.tableData.find(item => item.id === row.id)
        if (currRow) {
          if (this.limit === 1) {
            this.$refs.table.setCurrentRow(currRow)
          } else {
            this.$refs.table.toggleRowSelection(currRow, true)
          }
        }
      })
    },
    removeItem(item, index) {
      const tableItem = this.tableData.find(it => it.id === item.id)
      this.chosedData.splice(index, 1)
      if (tableItem) {
        this.$refs.table.toggleRowSelection(tableItem, false)
      }
      if (this.chosedData.length === 0) {
        this.$message.error('请至少选择一个人员')
        return false
      }
    }
  }
}
</script>

<style scoped>
.chosed-box {
  margin-bottom: 5px;
}

.chosed-item {
  margin-right: 10px;
  margin-bottom: 5px;
}
</style>
