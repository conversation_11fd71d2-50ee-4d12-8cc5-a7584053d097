<template>
  <el-input
    v-model="modalValue"
    type="number"
    clearable
    placeholder="请输入"
    v-bind="_props"
  >
    <template slot="append">万元</template>
  </el-input>
</template>
<script>
import ElInput from 'element-ui/packages/input'
import { toFixed } from '@/utils/index'
export default {
  extends: ElInput,
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    modalValue: {
      get() {
        if (this.value === undefined || this.value === null || this.value === '') {
          return ''
        }
        return toFixed((this.value || 0) / 10000, 2)
      },
      set(val) {
        this.$emit('input', val * 10000)
      }
    }
  },
  watch: {
  },
  mounted() {
    // console.log('32323', this)
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
