<template>
  <el-input
    v-model="modalValue"
    type="number"
    clearable
    placeholder="请输入"
    v-bind="_props"
  >
    <template slot="append">%</template>
  </el-input>
</template>
<script>
import ElInput from 'element-ui/packages/input'
import { toFixed } from '@/utils/index'
export default {
  extends: ElInput,
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    modalValue: {
      get() {
        if (this.value === undefined || this.value === null || this.value === '') {
          return ''
        }
        return toFixed(this.value * 100, 2)
      },
      set(val) {
        if (val === undefined || val === null || val === '') {
          this.$emit('input', '')
          return
        } else if (val.endsWith('.')) {
          return
        } else if (val > 100) {
          val = 100
        } else if (val < 0) {
          val = 0
        }
        this.$emit('input', toFixed(val / 100, 4))
      }
    }
  },
  watch: {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
