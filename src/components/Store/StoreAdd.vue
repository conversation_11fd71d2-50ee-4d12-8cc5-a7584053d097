<template>
  <el-dialog v-if="isShow" title="店铺信息" :visible.sync="isShow" width="700px" :close-on-click-modal="false" :before-close="close" append-to-body>
    <el-form ref="form" class="oprate-form" :model="formData" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="店铺ID" prop="dyStoreId">
            <el-input v-model="formData.dyStoreId" clearable placeholder="请输入店铺ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="店铺UID" prop="dyStoreUid">
            <el-input v-model="formData.dyStoreUid" clearable placeholder="请输入店铺UID" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="店铺名称" prop="storeName">
            <el-input v-model="formData.storeName" clearable placeholder="请输入店铺名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="店铺评分" prop="storeRating">
            <el-input v-model="formData.storeRating" type="number" clearable placeholder="请输入店铺评分" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="店铺链接" prop="storeLink">
            <div class="flex-content">
              <el-input v-model="formData.storeLink" clearable placeholder="请输入店铺链接" />
              <el-form><el-button style="width:55px" type="primary" plain class="ml5" @click="$copy(formData.storeLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <div class="dialog-footer">
      <el-button plain type="primary" @click="close">取 消</el-button>
      <el-button type="primary" @click="saveHandle">保 存</el-button>
    </div>
  </el-dialog>
</template>
<script>
// import { memberTypeList } from '@/api/dict-config'
import enterpriseApi from '@/api/enterprise'
import { validURL } from '@/utils/validate'

export default {
  components: {
  },
  // mixins: [listMixin],
  props: {},
  data() {
    return {
      isShow: false,
      formData: {},
      rules: {
        dyStoreId: [{ required: true, message: '请输入店铺ID', trigger: 'change' }],
        dyStoreUid: [{ required: true, message: '请输入店铺UID', trigger: 'change' }],
        storeName: [{ required: true, message: '请输入店铺名称', trigger: 'change' }],
        storeRating: [{ required: true, message: '请输入店铺评分', trigger: 'change' }],
        storeLink: [
          { required: true, message: '请输入店铺链接', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validURL(value)) {
              callback(new Error('请输入正确的链接地址（格式：https://www.example.com）'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
      },
      // memberTypeList,
      // 增删改查集合
      apis: {
        ...enterpriseApi
      }
    }
  },
  created() {},
  mounted() {
    // this.initDict()
  },
  methods: {
    // 显示弹框
    show(dataList, index) {
      console.log(index)
      this.dataList = JSON.parse(JSON.stringify(dataList)) || []
      if (index !== undefined) {
        this.formData = this.dataList[index]
      } else {
        this.formData = {}
        this.dataList.push(this.formData)
      }
      this.isShow = true
    },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 因为接口是合并的，这里重新读取企业信息来更新
          this.apis.getDetail().then(response => {
            this.apis.update({
              ...response.data,
              stores: this.dataList
            }).then(res => {
              this.$message.success('保存成功')
              this.$emit('success', res.data)
              this.close()
              // this.close()
            }).finally(() => {
              this.isSubmiting = false
            })
          })
        }
      })
    },
    async saveCallback(code, data) {
      if (code === 200) {
        this.$message.success(`导入总行数${data.rows},有效导入行数${data.validRows}`)
      }
      this.close()
      this.$emit('update')
    },
    close() {
      this.isShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
// ::v-deep .el-dialog__body {
//   padding: 0;
// }
.dialog-footer {
  text-align: right;
}
</style>
