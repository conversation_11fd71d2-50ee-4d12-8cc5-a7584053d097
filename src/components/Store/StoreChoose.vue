<template>
  <el-popover
    v-model="isShow"
    placement="left"
    width="800"
    trigger="click"
  >
    <div v-if="!$isAdmin && editable">
      <el-button
        hover-class="button-hover"
        type="primary"
        @click="addHandle()"
      >
        新增
      </el-button>
    </div>
    <el-table :data="options" :max-height="350" :row-style="rowStyle" @row-click="rowClick">
      <el-table-column type="index" label="序号" />
      <el-table-column width="110" property="dyStoreId" label="店铺ID" show-overflow-tooltip />
      <el-table-column width="110" property="dyStoreUid" label="店铺UID" show-overflow-tooltip />
      <el-table-column width="150" property="storeName" label="店铺名称" show-overflow-tooltip />
      <el-table-column property="storeLink" label="店铺链接" show-overflow-tooltip />
      <el-table-column width="80" property="storeRating" label="店铺评分" show-overflow-tooltip />
      <el-table-column v-if="!$isAdmin && editable" width="80" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addHandle(scope.$index)"
          >修改</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.no)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <slot slot="reference" />
    <StoreAdd ref="addRef" @success="onSuccess" />
  </el-popover>
  <!-- </div> -->
</template>
<script>
import enterprisetApi from '@/api/enterprise'
import StoreAdd from './StoreAdd'
import { mapGetters } from 'vuex'
export default {
  components: { StoreAdd },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 商家id
    merchantId: {
      type: [String, Number],
      default: ''
    },
    // 店铺
    store: {
      type: Object,
      default: null
    },
    editable: {
      type: Boolean,
      default: false
    },
    // 空的时候是否默认第一条数据
    setDefault: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShow: false,
      options: []
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
    modalValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    merchantId: {
      immediate: true,
      async handler(val) {
        await this.pullData(val)
        if (this.options.length && (!this.modalValue) && this.setDefault) {
          this.changeSelect(this.options[0].id)
        }
      }
    },
    isShow(val) {
      if (!val) {
        this.$emit('close')
      } else {
        this.$emit('open')
      }
    }
  },
  mounted() {
  },
  methods: {
    rowStyle({ row }) {
      return {
        cursor: 'pointer',
        // background: 'red'
      }
    },
    pullData(merchantId) {
      if (!this.$isAdmin) {
        this.options = this.userInfo.stores || []
      } else if (!merchantId) {
        return
      }
      return enterprisetApi.getDetail(merchantId).then(res => {
        this.options = res.data.stores || []
      })
    },
    changeSelect(val) {
      let item = null
      this.modalValue = val
      if (val) {
        item = this.options.find(it => it.id === val)
      }
      this.$emit('change', val, item)
    },
    rowClick(row) {
      this.changeSelect(row.id)
      this.isShow = false
    },
    addHandle(index) {
      this.$refs.addRef.show(this.options, index)
    },
    async onSuccess(data) {
      await this.$store.dispatch('user/getInfo')
      this.pullData()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
