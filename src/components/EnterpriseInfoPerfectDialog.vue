<template>
  <el-dialog title="完善企业信息" :visible.sync="isShow" width="800px" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" append-to-body>
    <el-form v-if="formData.companies && formData.stores" ref="form" :model="formData" :rules="rules" class="app-container oprate-form" label-suffix=":" label-width="130px" style="padding-right: 20px;">
      <el-row>
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companies[0].companyName">
            <el-input v-model="formData.companies[0].companyName" clearable placeholder="请输入公司名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="companies[0].unifiedSocialCreditCode">
            <el-input v-model="formData.companies[0].unifiedSocialCreditCode" clearable placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="店铺ID" prop="stores[0].dyStoreId">
            <div class="flex-content">
              <el-input v-model="formData.stores[0].dyStoreId" placeholder="请输入店铺ID" autocomplete="off" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="店铺UID" prop="stores[0].dyStoreUid">
            <div class="flex-content">
              <el-input v-model="formData.stores[0].dyStoreUid" placeholder="请输入店铺UID" autocomplete="off" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="店铺名称" prop="stores[0].storeName">
            <div class="flex-content">
              <el-input v-model="formData.stores[0].storeName" placeholder="请输入店铺名称" autocomplete="off" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品牌" prop="brand">
            <el-input v-model="formData.brand" placeholder="请输入品牌" autocomplete="off" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="店铺链接" prop="stores[0].storeLink">
            <div class="flex-content">
              <el-input v-model="formData.stores[0].storeLink" placeholder="格式：https://www.example.com" autocomplete="off" />
              <el-form><el-button type="primary" style="width: 55px;" plain class="ml5" @click="$copy(formData.stores[0].storeLink, $event)">复制</el-button></el-form>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="mt5">
          <el-form-item label="联系人姓名" prop="contactName">
            <div class="flex-content">
              <el-input v-model="formData.contactName" placeholder="请输入联系人姓名" autocomplete="off" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="mt5">
          <el-form-item label="联系人手机号" prop="contactPhoneNo">
            <div class="flex-content">
              <el-input v-model="formData.contactPhoneNo" placeholder="请输入联系人手机号" autocomplete="off" />
            </div>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12" class="mt5">
          <el-form-item label="推荐人" prop="recommendedBy">
            <el-input v-model="formData.recommendedBy" autocomplete="off" placeholder="请输入推荐人" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="$router.back()">取 消</el-button>
      <el-button type="primary" :loading="isSubmiting" @click="saveHandle">保 存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import enterpriseApis from '@/api/enterprise'
import { mapGetters } from 'vuex'
import { validPhone, validURL } from '@/utils/validate'
export default {
  components: {
  },
  data() {
    return {
      isShow: false,
      isSubmiting: false,
      formData: {},
      rules: {
        'companies[0].companyName': [
          { required: true, message: '请输入公司名称', trigger: 'change' },
        ],
        'companies[0].unifiedSocialCreditCode': [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        brand: [
          { required: true, message: '请输入品牌', trigger: 'change' },
        ],
        'stores[0].dyStoreId': [
          { required: true, message: '请输入店铺ID', trigger: 'change' },
        ],
        'stores[0].dyStoreUid': [
          { required: true, message: '请输入店铺UID', trigger: 'change' },
        ],
        'stores[0].storeName': [
          { required: true, message: '请输入店铺名称', trigger: 'change' },
        ],
        'stores[0].storeLink': [
          { required: true, message: '请输入店铺链接', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validURL(value)) {
              callback(new Error('请输入正确的链接地址（格式：https://www.example.com）'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ],
        contactName: [
          { required: true, message: '请输入联系人姓名', trigger: 'change' },
        ],
        contactPhoneNo: [
          { required: true, message: '请输入联系人手机号', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (!validPhone(value)) {
              callback(new Error('请输入正确的手机号码'))
            } else {
              callback()
            }
          }, trigger: 'change' },
        ]
      },
      callback: null
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ]),
  },
  mounted() {
  },
  methods: {
    // pullData() {
    //   this.isLoading = true
    //   this.apis.getDetail().then(response => {
    //     this.formData = response.data || {}
    //   }).finally(() => {
    //     this.isLoading = false
    //   })
    // },
    close() {
      this.isShow = false
    },
    // 显示弹框
    show(callback) {
      this.isShow = true
      this.callback = callback
      this.formData = { ...this.userInfo }
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.isSubmiting = true
      enterpriseApis.update(this.formData).then(async reponse => {
        if (reponse.code === 200) {
          await this.$store.dispatch('user/getInfo')
          // this.$emit('success', res.data)
          // if (this.userInfo.hasBeenImproved) {
          this.$message.success('保存成功')
          this.close()
          this.callback && this.callback()
          // } else {
          //   this.$message.warning('保存成功，请继续完善信息')
          // }
        } else {
          this.$message({
            showClose: true,
            type: 'error',
            message: reponse.msg
          })
        }
      }).finally(() => {
        this.isSubmiting = false
      })
    },
    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => resolve(valid))
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
