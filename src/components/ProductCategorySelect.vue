<template>
  <el-select v-if="deep === 1" v-model="modalValue" clearable :disabled="disabled" :multiple="multiple" @change="emit('change', $event)">
    <el-option
      v-for="item in options"
      :key="item.code"
      :label="item.name"
      :value="item.code"
    />
  </el-select>
  <el-cascader v-else v-model="modalValue" :options="options" :disabled="disabled" :show-all-levels="false" :props="defaultProps" @change="emit('change', $event)" />
</template>
<script>
// import ElCascader from 'element-ui/lib/cascader'
import productCategoryApi from '@/api/productCategory'
export default {
  // extends: ElCascader,
  props: {
    deep: {
      type: Number,
      default: 1
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
        // disabled(data, node) {
        //   return node.level !== 2
        // }
      },
      options: []
    }
  },
  computed: {
    modalValue: {
      get() {
        if (this.deep === 1) {
          return this.value
        } else {
          const val = this.getArrFormOption(this.value, [], this.options)
          return val
        }
      },
      set(val) {
        if (this.deep === 1) {
          this.$emit('input', val)
        } else {
          if (val && val.length) {
            this.$emit('input', val[val.length - 1])
          } else {
            this.$emit('input', '')
          }
        }
      }
    }
  },
  mounted() {
    if (this.deep === 1) {
      this.getList()
    } else {
      this.getTrees()
    }
  },
  methods: {
    emit(eventName, val) {
      let items = []
      if (!this.multiple) {
        items = this.options.find(item => item.code === val)
      } else {
        items = this.options.filters(item => (val || []).includes(item.code))
      }
      this.$emit(eventName, val, items)
    },
    // 从列表中选出已选择的
    getArrFormOption(val, arr, list) {
      if (!this.value || !list || !list.length) {
        return ''
      }
      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        if (item.children) {
          const v = this.getArrFormOption(val, [...arr, item.code], item.children)
          if (v) {
            return v
          }
        }
        if (item.code === val) {
          return [...arr, item.code]
        }
      }
      return ''
    },
    // 递归判断列表，把最后的children设为undefined
    transferTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.transferTreeData(data[i].children)
        }
      }
      return data
    },
    async getList() {
      this.isLoading = true
      const response = await productCategoryApi.getList()
      this.isLoading = false
      if (response.data) {
        if (response.data) {
          this.options = response.data || []
        }
      }
      return response
    },
    async getTrees() {
      this.isLoading = true
      const response = await productCategoryApi.getTrees()
      this.isLoading = false
      if (response.data) {
        if (response.data) {
          this.options = this.transferTreeData(response.data || [])
        }
      }
      return response
    },
    chooseHandle() {
      this.modalValue = '公司111'
    },
  }
}
</script>
<style lang="scss" scoped>
</style>
