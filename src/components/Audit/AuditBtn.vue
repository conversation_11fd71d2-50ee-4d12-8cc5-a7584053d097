<template>
  <el-button
    v-if="getIsShowAuditBtn()"
    :loading="isSubmiting"
    type="primary"
    v-bind="_props"
    @click="auditHandle()"
  >
    <slot>{{ levelTitle }}</slot>
    <AuditDialog ref="auditDialogRef" :title="levelTitle" :is-enterprise-data="isEnterpriseData" :audit-api="auditApi" :on-success="onSuccess" />
  </el-button>
</template>
<script>
import AuditDialog from '@/components/Audit/AuditDialog'
import { levelTypeSet } from '@/enums/auditLevel'
import ElButton from 'element-ui/packages/button'
export default {
  components: { AuditDialog },
  extends: ElButton,
  props: {
    // 审核级次
    level: {
      type: String,
      default: ''
    },
    // 是否商家数据，是的话要告知客户详情
    isEnterpriseData: {
      type: Boolean,
      default: false
    },
    // /**
    //  * 业务单据类型
    //  * registration商品提报
    //  * sample样品
    //  * sales_receipts销售收款
    //  * contract_payment_invoice发票
    //  * sales_contract销售合同
    //  * purchase_contract采购合同
    //  * sales_contract_sku销售合同sku
    //  * live_broadcast_plan_budget直播计划预算
    //  * purchase_quotation采购报价单
    //  * purchase_payment采购付款
    // */
    // businessType: {
    //   required: true,
    //   type: String,
    //   default: ''
    // },
    // 单据id
    orderId: {
      required: true,
      type: [String, Number],
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    onSuccess: {
      type: Function,
      default: () => {}
    },
    // eslint-disable-next-line vue/require-prop-types
    auditRecords: {
      required: true,
      // type: [Array, null]
    },
    // 接口对象
    auditApi: {
      type: Function,
      default: () => {}
    },
    // 前置执行函数
    onBefore: {
      type: Function,
      default: null
    },
  },
  data() {
    return {
      isSubmiting: false
    }
  },
  computed: {
    levelType() {
      return levelTypeSet[this.level] || {}
    },
    levelTitle() {
      if (this.title) {
        return this.title
      }
      return `${this.levelType.name || ''}审核`
    },
  },
  mounted() {
  },
  methods: {
    // 判断审核按钮权限
    getIsShowAuditBtn() {
      if (!this.$isAdmin) {
        return false
      }
      if (!this.auditRecords || !this.auditRecords.length) {
        return false
      }
      return this.auditRecords.find(item => item.result === 0 && item.auditLevel === this.levelType.level)
    },
    async auditHandle() {
      if (this.onBefore) {
        const valid = await this.onBefore()
        if (valid === false) {
          return
        }
      }
      this.$refs.auditDialogRef.show({
        orderId: this.orderId,
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
