<template>
  <el-collapse-item :name="name" class="collapse-module">
    <template slot="title">
      <div class="collapse-title-pre">审核信息</div>
    </template>
    <el-form ref="form" :model="formData" label-position="right" disabled label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="状态"
            prop="状态"
          >
            <el-select v-model="formData.status" placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in $enums['auditStatus'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.status === 2" :span="6">
          <el-form-item
            label="不通过原因"
            prop="reasonForFailure"
          >
            <el-select v-model="formData.reasonForFailure" placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in $enums['enterpriseAuditFailureReason'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.status === 2">
        <el-col :span="12">
          <el-form-item
            label="原因详情"
            prop="reasonDetails"
          >
            <el-input
              v-model="formData.reasonDetails"
              clearable
              type="textarea"
              :rows="3"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            label="审核人"
            prop="reviewBy"
          >
            <el-input
              v-model="formData.reviewBy"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="审核时间"
            prop="reviewTime"
          >
            <el-input
              :value="formData.reviewTime | parseDate"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-collapse-item>
</template>

<script>
// import enterpriseApi from '@/api/enterprise'

export default {
  props: {
    name: {
      type: String,
      default: '1'
    },
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>
