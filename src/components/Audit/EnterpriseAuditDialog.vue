<template>
  <el-dialog v-if="isShow" :title="title" :visible.sync="isShow" width="600px" :close-on-click-modal="false" append-to-body>
    <el-form ref="form" :model="formData" :rules="rules" label-position="right" label-width="100px" class="mr15">
      <el-row>
        <el-col :span="14">
          <el-form-item
            label="审核意见"
            prop="status"
          >
            <el-select v-model="formData.status" placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in $enums[statusEnumType].toArr().filter(item => item.value > 0)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.status===2">
        <el-col :span="14">
          <el-form-item
            label="不通过原因"
            prop="reasonForFailure"
          >
            <el-select v-model="formData.reasonForFailure" placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in $enums['enterpriseAuditFailureReason'].toArr()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="原因详情"
            prop="reasonDetails"
          >
            <el-input
              v-model="formData.reasonDetails"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入原因详情"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
const defaultFormData = {
  status: null,
  reasonForFailure: null,
  reasonDetails: null
}
export default {
  props: {
    title: {
      type: String,
      default: '审核'
    },
    // 如果是特殊的级别，才传，否则默认是通用的
    statusEnumType: {
      type: String,
      default: 'auditStatus'
    },
    // 接口对象
    auditApi: {
      type: Function,
      default: () => {}
    },
    // 提交成功回调
    onSuccess: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      isShow: false,
      formData: { ...defaultFormData },
      rules: {
        status: [
          { required: true, message: '请选择审核意见', trigger: 'change' }
        ],
        reasonForFailure: [
          { required: true, message: '请选择不通过原因', trigger: 'change' }
        ],
      },
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    close() {
      this.isShow = false
    },
    /**
     * 显示弹框
     * data:
     *  orderId: 业务单据id
     *  type: 业务单据类型
     * auditLevel: 审核级次：1样品；2商务；3运营；4合规；5客服；6合同；7财务
     */
    show(data) {
      this.isShow = true
      this.formData = { ...data, ...defaultFormData }
    },
    confirmHandle() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return
        }
        this.isSubmiting = true
        this.auditApi({
          ...this.formData,
        }).then(response => {
          this.$message.success('提交成功')
          this.close()
          this.onSuccess && this.onSuccess()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 10px;
}
</style>
