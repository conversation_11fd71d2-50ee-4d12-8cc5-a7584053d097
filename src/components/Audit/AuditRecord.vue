<template>
  <el-table
    class="list-table"
    :data="data"
    :stripe="true"
    border
    style="width: 100%;"
  >
    <el-table-column
      fixed
      label="序号"
      type="index"
      min-width="80"
    />
    <el-table-column
      label="审核级次"
      prop="auditLevel"
      min-width="120"
    >
      <template slot-scope="scope">
        {{ scope.row.auditLevel | toEnumName('auditLevel') }}
      </template>
    </el-table-column>
    <el-table-column
      label="审核意见"
      prop="result"
      min-width="150"
    >
      <template slot-scope="scope">
        {{ scope.row.result | toEnumName(statusEnumType) }}
      </template>
    </el-table-column>
    <el-table-column
      label="备注"
      prop="detail"
      min-width="250"
    >
      <template v-if="scope.row.detail" slot-scope="scope">
        <div v-if="scope.row.result===2">不通过原因：{{ scope.row.detail.reasonForFailure | toEnumName('enterpriseAuditFailureReason') }}</div>
        <template v-if="scope.row.result===2||scope.row.result===3">
          <div v-if="$isAdmin">告知客户详情：{{ scope.row.detail.informCustomersOfDetails }}</div>
          <div v-else>原因详情：{{ scope.row.detail.informCustomersOfDetails }}</div>
        </template>
        <div v-if="$isAdmin">原因详情：{{ scope.row.detail.reasonDetails }}</div>
      </template>
    </el-table-column>
    <!-- <el-table-column
      label="不通过原因"
      prop="detail.reasonForFailure"
      min-width="250"
    >
      <template slot-scope="scope">
        {{ scope.row.result | toEnumName('enterpriseAuditFailureReason') }}
      </template>
    </el-table-column> -->
    <!-- <el-table-column
      label="原因详情"
      prop="detail.reasonDetails"
      min-width="110"
    /> -->
    <el-table-column
      label="审核人"
      prop="auditBy"
      min-width="110"
    />
    <el-table-column
      label="审核时间"
      prop="auditTime"
      min-width="100"
    >
      <template slot-scope="scope">
        {{ scope.row.auditTime | parseDate }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
// import enterpriseApi from '@/api/enterprise'
export default {
  props: {
    // 如果是特殊的级别，才传，否则默认是通用的
    statusEnumType: {
      type: String,
      default: 'auditStatus'
    },
    // 接口对象
    apis: {
      type: Object,
      default: null
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
