<template>
  <el-dialog v-if="isShow" :title="title" :visible.sync="isShow" width="600px" :close-on-click-modal="false" append-to-body>
    <el-form ref="form" :model="formData" :rules="rules" label-position="right" label-width="100px" class="mr15">
      <el-row>
        <el-col :span="14">
          <el-form-item
            label="审核意见"
            prop="result"
          >
            <el-select v-model="formData.result" placeholder="请选择" clearable style="width: 100%;">
              <el-option
                v-for="item in $enums[statusEnumType].toArr().filter(item => item.value > 0)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row v-if="showBlindBuy">
        <el-col :span="14">
          <el-form-item
            label="是否盲拍"
            prop="detail.是否盲拍"
          >
            <el-checkbox v-model="formData.是否盲拍" />
          </el-form-item>
        </el-col>
      </el-row> -->
      <template v-if="formData.result===2">
        <el-row>
          <el-col :span="14">
            <el-form-item
              label="不通过原因"
              prop="detail.reasonForFailure"
            >
              <el-select v-model="formData.detail.reasonForFailure" placeholder="请选择" clearable style="width: 100%;">
                <el-option
                  v-for="item in $enums['enterpriseAuditFailureReason'].toArr()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isEnterpriseData">
          <el-col :span="24">
            <el-form-item
              label="告知客户详情"
              prop="detail.informCustomersOfDetails"
            >
              <el-input
                v-model="formData.detail.informCustomersOfDetails"
                type="textarea"
                :rows="3"
                clearable
                placeholder="请输入告知客户详情"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="原因详情"
            prop="detail.reasonDetails"
          >
            <el-input
              v-model="formData.detail.reasonDetails"
              type="textarea"
              :rows="3"
              clearable
              placeholder="请输入原因详情"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="confirmHandle">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
const defaultFormData = {
  result: null,
  detail: {
    reasonForFailure: '',
    informCustomersOfDetails: '',
    reasonDetails: ''
  },
}
export default {
  props: {
    title: {
      type: String,
      default: '审核'
    },
    // 是否商家数据，是的话要告知客户详情
    isEnterpriseData: {
      type: Boolean,
      default: false
    },
    // 如果是特殊的级别，才传，否则默认是通用的
    statusEnumType: {
      type: String,
      default: 'auditStatus'
    },
    // 接口对象
    auditApi: {
      type: Function,
      default: () => {}
    },
    // 提交成功回调
    onSuccess: {
      type: Function,
      default: null
    },
    // // 是否显示发起盲拍选项框
    // showBlindBuy: {
    //   type: Boolean,
    //   default: false
    // },
  },
  data() {
    return {
      isShow: false,
      formData: { ...defaultFormData },
      rules: {
        result: [
          { required: true, message: '请选择审核意见', trigger: 'change' }
        ],
        'detail.reasonForFailure': [
          { required: true, message: '请选择不通过原因', trigger: 'change' }
        ],
      },
    }
  },
  computed: {
    // // 是否商家端提交的数据，如果是的话审核框要加告知客户详情
    // isEnterpriseData() {
    //   const { type } = this.formData
    //   alert(type)
    //   return type === 'registration' ||
    //     type === 'sample' ||
    //     type === 'sales_receipts' ||
    //     type === 'contract_payment_invoice'
    // },
  },
  mounted() {
  },
  methods: {
    close() {
      this.isShow = false
    },
    /**
     * 显示弹框
     * data:
     *  orderId: 业务单据id
     * auditLevel: 审核级次：1样品；2商务；3运营；4合规；5客服；6合同；7财务
     */
    show(data) {
      this.isShow = true
      this.formData = JSON.parse(JSON.stringify({ ...defaultFormData, ...data }))
    },
    confirmHandle() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return
        }
        this.isSubmiting = true
        this.auditApi({
          ...this.formData,
        }).then(response => {
          this.$message.success('提交成功')
          this.close()
          this.onSuccess && this.onSuccess()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 10px;
}
</style>
