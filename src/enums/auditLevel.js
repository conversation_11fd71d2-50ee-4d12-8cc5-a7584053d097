/**
 * 审核级次
*/
import BaseModel from './baseModel'
export const levelTypeSet = {
  CJGLY: { level: 0, name: '超级管理员' },
  CGY: { level: 1, name: '仓管员' },
  businessAudit: { level: 2, name: '商务' },
  businessManagerAudit: { level: 3, name: '商务主管' },
  operationsAudit: { level: 4, name: '运营' },
  operationsManagerAudit: { level: 5, name: '运营主管' },
  complianceAudit: { level: 6, name: '合规' },
  customerServiceAudit: { level: 7, name: '客服' },
  legalAffairsAudit: { level: 8, name: '法务' },
  financeAudit: { level: 9, name: '财务' },
  financeManagerAudit: { level: 10, name: '财务主管' },
  cashierAudit: { level: 11, name: '出纳' },
  institutionalHeadAudit: { level: 12, name: '机构负责人' },
}
const set = {}
Object.keys(levelTypeSet).forEach(key => {
  const item = levelTypeSet[key]
  set[item.level] = levelTypeSet[key].name
})
export default new BaseModel({
  ...set
})
