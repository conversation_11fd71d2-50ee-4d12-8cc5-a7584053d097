/**
 * 基础模型类
 *
 * @export
 * @class BaseModel
 */
export default class BaseModel {
  constructor(model, sets) {
    !sets && (sets = {})
    this._model = model
    this._colors = sets.colors || {}
    this._tagTypes = sets.tagTypes || {}
    this._iconfonts = sets.iconfonts || {}
  }
    toObj = () => {
      return this._model
    }
    // 转化成[{value: 1, label: '名称'}]格式
    toArr = () => {
      return Object.entries(this._model).map(item => {
        return {
          value: parseInt(item[0]),
          label: item[1],
          tagType: this._tagTypes[item[0]] || '',
          iconfont: this._iconfonts[item[0]] || '',
          color: this._colors[item[0]] || '',
        }
      })
    }
}
