import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

import {
  showFullScreenLoading,
  tryHideFullScreenLoading
} from './requestInitHelper'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 500000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['OpenApiAccessToken'] = getToken()
    }
    showFullScreenLoading()
    return config
  },
  error => {
    tryHideFullScreenLoading()
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  async response => {
    // 如果发现文件下载的标记，直接返回response
    if (response.headers['content-type'] === 'application/octet-stream') {
      tryHideFullScreenLoading()
      return response
    }
    const res = response.data
    return new Promise((resolve, reject) => {
      // if the custom code is not 20000, it is judged as an error.
      if (res.code !== 200) {
        // 401标识未登录;
        if (res.code === 401 || res.code === 402) {
          // to re-login
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
          // MessageBox.confirm('您的账号未登录，或登录已过期！', '未登录', {
          //   confirmButtonText: '重新登录',
          //   cancelButtonText: '取消',
          //   type: 'warning'
          // }).then(() => {
          //   store.dispatch('user/resetToken').then(() => {
          //     location.reload()
          //   })
          // }).catch(() => {
          // })
        } else {
          tryHideFullScreenLoading()
          Message({
            message: res.msg || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        }
        reject(res.msg || 'Error')
      } else {
        tryHideFullScreenLoading()
        resolve(res)
      }
    })
  },
  error => {
    tryHideFullScreenLoading()
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
