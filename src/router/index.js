import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

// import None from '@/layout/None'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/mine',
    component: Layout,
    redirect: 'notice',
    children: [
      {
        path: 'notice',
        component: () => import('@/views/mine/notice/index'),
        name: 'MyNoticeLog',
        hidden: true,
        meta: { title: '我的消息', noCache: false },
      }
    ]
  },
  {
    path: '/agreement/privacy',
    hidden: true,
    component: () => import('@/views/agreement/privacy'),
  },
  {
    path: '/agreement/user-policy',
    hidden: true,
    component: () => import('@/views/agreement/userPolicy'),
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/forgot',
    component: () => import('@/views/login/forgot'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/login/register'),
    hidden: true
  },
  {
    path: '/register-success',
    component: () => import('@/views/login/register-success'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/profile',
    component: Layout,
    redirect: '/profile/index',
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/profile/index'),
        name: 'Profile',
        meta: { title: 'Profile', icon: 'user', noCache: true }
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/live-broadcast',
    component: Layout,
    redirect: 'pre-sign-up',
    hidden: false,
    name: 'LiveBroadcast',
    meta: { title: '直播报名', icon: 'el-icon-s-platform', noCache: true/*, menuCode: 'menu:live-broadcast'*/ },
    children: [
      {
        path: 'pre-sign-up',
        component: () => import('@/views/live-broadcast/pre-sign-up'),
        name: 'PreSignUp',
        meta: { title: '商品提报', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      },
      {
        path: 'pre-sign-up-detail',
        component: () => import('@/views/live-broadcast/pre-sign-up/detail'),
        name: 'PreSignUpDetail',
        hidden: true,
        meta: { title: '商品提报详情', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      },
      {
        path: 'pre-sign-up-add',
        component: () => import('@/views/live-broadcast/pre-sign-up/detail'),
        name: 'PreSignUpAdd',
        hidden: true,
        meta: { title: '新增商品提报', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      },
      {
        path: 'invitation',
        component: () => import('@/views/live-broadcast/invitation'),
        name: 'Invitation',
        meta: { title: '邀请报名', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      },
      // {
      //   path: 'invitation-add',
      //   component: () => import('@/views/live-broadcast/invitation/detail'),
      //   name: 'InvitationAdd',
      //   hidden: true,
      //   meta: { title: '新增邀请报名', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      // },
      {
        path: 'invitation-detail',
        component: () => import('@/views/live-broadcast/invitation/detail'),
        name: 'InvitationDetail',
        hidden: true,
        meta: { title: '邀请报名详情', noCache: true/*, menuCode: 'menu:live-broadcast:liveBroadcastManage'*/ }
      },
      {
        path: 'sample',
        component: () => import('@/views/sample'),
        name: 'Sample',
        meta: { title: '样品管理', noCache: true/*, menuCode: 'menu:live-broadcast:sampleManage'*/ }
      },
      {
        path: 'sample-add',
        component: () => import('@/views/sample/detail'),
        name: 'SampleAdd',
        hidden: true,
        meta: { title: '新增样品', noCache: true/*, menuCode: 'menu:live-broadcast:sampleManage'*/ }
      },
      {
        path: 'sample-detail',
        component: () => import('@/views/sample/detail'),
        name: 'SampleDetail',
        hidden: true,
        meta: { title: '样品详情', noCache: true/*, menuCode: 'menu:live-broadcast:sampleManage'*/ }
      },
      {
        path: 'sample-resend',
        component: () => import('@/views/sample/resend'),
        name: 'SampleResend',
        hidden: true,
        meta: { title: '样品补寄', noCache: true/*, menuCode: 'menu:live-broadcast:sampleManage'*/ }
      },
    ]
  },
  {
    path: '/goods',
    component: Layout,
    redirect: 'list',
    hidden: false,
    meta: { title: '商品管理', icon: 'el-icon-s-goods', noCache: true/*, menuCode: 'menu:baseinfo'*/ },
    children: [
      {
        path: 'list',
        component: () => import('@/views/goods'),
        name: 'Goods',
        meta: { title: '商品管理', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      },
      {
        path: 'goods-detail',
        component: () => import('@/views/goods/detail'),
        name: 'GoodsDetail',
        hidden: true,
        meta: { title: '商品详情', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      },
      {
        path: 'goods-add',
        component: () => import('@/views/goods/detail'),
        name: 'GoodsAdd',
        hidden: true,
        meta: { title: '新增商品', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      },
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: 'enterprise-payment',
    hidden: false,
    name: 'EnterprisePayment',
    meta: { title: '财务管理', icon: 'el-icon-money', noCache: true },
    children: [
      {
        path: 'enterprise-payment',
        component: () => import('@/views/finance/enterprise-payment'),
        name: 'EnterprisePayment',
        meta: { title: '付款管理', noCache: true }
      },
      {
        path: 'enterprise-payment-detail',
        component: () => import('@/views/finance/enterprise-payment/detail'),
        name: 'EnterprisePaymentDetail',
        hidden: true,
        meta: { title: '付款单详情', noCache: true }
      },
      {
        path: 'enterprise-payment-add',
        component: () => import('@/views/finance/enterprise-payment/add'),
        name: 'EnterprisePaymentAdd',
        hidden: true,
        meta: { title: '新增付款', noCache: true }
      },
      {
        path: 'invoice',
        component: () => import('@/views/finance/invoice'),
        name: 'FinanceInvoice',
        meta: { title: '发票管理', noCache: true }
      },
      {
        path: 'invoice-detail',
        component: () => import('@/views/finance/invoice/detail'),
        name: 'FinanceInvoiceDetail',
        hidden: true,
        meta: { title: '发票详情', noCache: true }
      },
      {
        path: 'invoice-add',
        component: () => import('@/views/finance/invoice/add'),
        name: 'FinanceInvoiceAdd',
        hidden: true,
        meta: { title: '申请发票', noCache: true }
      },
      // {
      //   path: 'earnest-money-merchant',
      //   component: () => import('@/views/finance/earnest-money/merchant-list'),
      //   name: 'EarnestMoneyMerchant',
      //   meta: { title: '保证金商家', noCache: true }
      // },
      {
        path: 'earnest-money',
        component: () => import('@/views/finance/earnest-money'),
        name: 'EarnestMoney',
        // hidden: true,
        meta: { title: '保证金', noCache: true }
      },
      {
        path: 'earnest-money-add',
        component: () => import('@/views/finance/earnest-money/add'),
        name: 'EarnestMoneyAdd',
        hidden: true,
        meta: { title: '保证金退还申请', noCache: true }
      },
      {
        path: 'earnest-money-detail',
        component: () => import('@/views/finance/earnest-money/detail'),
        name: 'EarnestMoneyDetail',
        hidden: true,
        meta: { title: '保证金退还详情', noCache: true }
      },
    ]
  },
  {
    path: '/qualification',
    component: Layout,
    redirect: 'detail',
    hidden: false,
    name: 'Qualification',
    meta: { title: '资质文件', icon: 'el-icon-s-check', noCache: true/*, menuCode: 'menu:baseinfo'*/ },
    children: [
      {
        path: 'list',
        component: () => import('@/views/qualification'),
        name: 'Qualification',
        meta: { title: '资质文件', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      },
    ]
  },
  {
    path: '/enterprise',
    component: Layout,
    redirect: 'detail',
    hidden: false,
    name: 'Enterprise',
    meta: { title: '商家信息', icon: 'el-icon-s-custom', noCache: true/*, menuCode: 'menu:baseinfo'*/ },
    children: [
      // {
      //   path: 'list',
      //   component: () => import('@/views/enterprise'),
      //   name: 'Enterprise',
      //   meta: { title: '商家信息', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      // },
      {
        path: 'detail',
        component: () => import('@/views/enterprise/detail'),
        name: 'EnterpriseDetail',
        // hidden: true,
        meta: { title: '商家详情', noCache: true/*, menuCode: 'menu:baseinfo'*/ }
      },
    ]
  },
  // {
  //   path: '/system',
  //   component: Layout,
  //   redirect: '/system/menu',
  //   meta: { title: '系统管理', icon: 'el-icon-s-tools', noCache: true, menuCode: 'menu:system' },
  //   hidden: false,
  //   children: [
  //     // {
  //     //   path: 'menu',
  //     //   component: () => import('@/views/system/menu/index'),
  //     //   name: 'Menu',
  //     //   meta: { title: '菜单管理', noCache: true, menuCode: 'menu:system:menuManage' }
  //     // },
  //     // {
  //     //   path: 'area',
  //     //   component: () => import('@/views/system/area/index'),
  //     //   name: 'Area',
  //     //   meta: { title: '地区管理', noCache: true, menuCode: 'menu:system:areaManage' }
  //     // },
  //     // {
  //     //   path: 'config',
  //     //   component: () => import('@/views/system/config/index'),
  //     //   name: 'Config',
  //     //   meta: { title: '配置管理', noCache: true, menuCode: 'menu:system:configManage' }
  //     // },
  //     // {
  //     //   path: '/dict',
  //     //   component: () => import('@/views/system/dict/index'),
  //     //   name: 'Dict',
  //     //   meta: { title: '字典管理', noCache: true, menuCode: 'menu:system:dictManage' }
  //     // },
  //     {
  //       path: 'sysUser',
  //       component: () => import('@/views/system/user/index'),
  //       name: 'SysUser',
  //       meta: { title: '用户管理', noCache: true, menuCode: 'menu:system:userManage' }
  //     },
  //     // {
  //     //   path: 'role',
  //     //   component: () => import('@/views/system/role/index'),
  //     //   name: 'Role',
  //     //   meta: { title: '角色管理', noCache: true, menuCode: 'menu:system:roleManage' }
  //     // },
  //     // {
  //     //   path: 'post',
  //     //   component: () => import('@/views/system/post/index'),
  //     //   name: 'Post',
  //     //   meta: { title: '岗位管理', noCache: true, menuCode: 'menu:system:postManage' }
  //     // },
  //     // {
  //     //   path: '/code',
  //     //   component: () => import('@/views/code/list'),
  //     //   name: 'Code',
  //     //   meta: { title: '编码管理', noCache: true, menuCode: 'menu:system:codeManage' }
  //     // },
  //     // {
  //     //   path: '/detail/:ruleId',
  //     //   name: 'CodeDetail',
  //     //   hidden: true,
  //     //   component: () => import('@/views/code/detail'),
  //     //   meta: { title: '编码详情' }
  //     // },
  //     // {
  //     //   path: 'organize',
  //     //   component: () => import('@/views/system/organize/index'),
  //     //   name: 'Organize',
  //     //   meta: { title: '组织管理', noCache: true, menuCode: 'menu:system:organizeManage' }
  //     // },
  //     // {
  //     //   path: 'log',
  //     //   component: () => import('@/views/system/log/index'),
  //     //   name: 'Log',
  //     //   meta: { title: '日志管理', noCache: true, menuCode: 'menu:system:logManage' }
  //     // }
  //   ]
  // },
  // {
  //   path: '/notice',
  //   component: Layout,
  //   redirect: '/notice/log',
  //   hidden: false,
  //   name: 'Notice',
  //   meta: { title: '消息管理', icon: 'el-icon-s-promotion', noCache: true, menuCode: 'MENU202108190038' },
  //   children: [
  //     {
  //       path: 'log',
  //       component: () => import('@/views/notice/log/index'),
  //       name: 'Log',
  //       meta: { title: '发送消息列表', noCache: true, menuCode: 'MENU202108190039' }
  //     },
  //     {
  //       path: 'template',
  //       component: () => import('@/views/notice/template/index'),
  //       name: 'Template',
  //       meta: { title: '消息模版管理', noCache: true, menuCode: 'MENU202108190042' }
  //     }
  //   ]
  // },
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
