import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/area/list',
    method: 'post',
    data
  })
}

export function findChinaAllArea() {
  return request({
    url: '/area/findChinaAllArea',
    method: 'post'
  })
}

export function detail(data) {
  return request({
    url: '/area/detail',
    method: 'get',
    params: {
      code: data
    }
  })
}

export function save(data) {
  return request({
    url: '/area/save',
    method: 'post',
    data
  })
}

export function businessDel(data) {
  return request({
    url: '/area/delete/business',
    method: 'get',
    params: {
      code: data
    }
  })
}

export function update(data) {
  return request({
    url: '/area/update',
    method: 'post',
    data
  })
}

export function province() {
  return request({
    url: '/area/province',
    method: 'get'
  })
}

