/**
 * 保证金
*/
import request from '@/utils/request'
import { getCurrentUserInfo } from './user'

export const apis = {
  // 返回保证金相关商家分页
  getMerchantPager(data) {
    return request({
      url: `/merchant/page`,
      method: 'post',
      data
    })
  },
  // 获取商家保证金数据
  getEnterprisEarnestMoneyeData: getCurrentUserInfo,
  getPager(data) {
    return request({
      url: `/merchantMarginReturn/page`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/merchantMarginReturn/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/merchantMarginReturn/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/merchantMarginReturn/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/merchantMarginReturn/delete/${no}`,
      method: 'post'
    })
  },
  // 提交
  submit(id) {
    return request({
      url: `/merchantMarginReturn/submit/${id}`,
      method: 'post',
    })
  },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/merchantMarginReturn/businessAudit`,
      method: 'post',
      data
    })
  },
  // 财务主管审核
  financeManagerAudit(data) {
    return request({
      url: `/merchantMarginReturn/financeManagerAudit`,
      method: 'post',
      data
    })
  },
  // 出纳审核
  cashierAudit(data) {
    return request({
      url: `/merchantMarginReturn/cashierAudit`,
      method: 'post',
      data
    })
  },
  // 实际支付
  savePayInfo(data) {
    return request({
      url: `/merchantMarginReturn/actualPayment`,
      method: 'post',
      data
    })
  },
  // 保证金记录
  merchantMarginRecord(data) {
    return request({
      url: `/merchantMarginRecord/page`,
      method: 'post',
      data
    })
  },
  // 保证金冻结记录
  merchantFreezeMarginRecord(data) {
    return request({
      url: `/merchantFreezeMarginRecord/page`,
      method: 'post',
      data
    })
  },
}
export default {
  ...apis
}
