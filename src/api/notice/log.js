/**
 * 消息发送记录
*/
import request from '@/utils/request'

export const apis = {
  // 读取未读消息总数
  countUnRead() {
    return request({
      url: `/noticeLog/countUnRead`,
      method: 'get',
    })
  },
  getPager(data) {
    return request({
      url: `/noticeLog/page`,
      method: 'post',
      data
    })
  },
  getDetail(code) {
    return request({
      url: `/noticeLog/detail/${code}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/noticeLog/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/noticeLog/update`,
      method: 'post',
      data
    })
  },
  delete(id) {
    return request({
      url: `/noticeLog/delete/${id}`,
      method: 'post',
    })
  },
  // 我的消息列表
  getMyPager(data) {
    return request({
      url: `/noticeLog/mine/list`,
      method: 'post',
      data
    })
  },
  // 批量设置已读
  batchRead(data) {
    return request({
      url: `/noticeLog/batchRead`,
      method: 'post',
      data
    })
  },
}
export default {
  ...apis
}
