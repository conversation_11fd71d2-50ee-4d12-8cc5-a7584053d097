import request from '@/utils/request'

export function getRule(id) {
  return request({
    url: '/rule/get',
    method: 'get',
    params: { id: id }
  })
}

export function insertRule(data) {
  return request({
    url: '/rule/insert',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/rule/update',
    method: 'post',
    data
  })
}

export function delRule(data) {
  return request({
    url: '/rule/del',
    method: 'post',
    data
  })
}

export function findRuleList(data) {
  return request({
    url: '/rule/list',
    method: 'post',
    data
  })
}
