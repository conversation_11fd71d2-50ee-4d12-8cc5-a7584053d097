/**
 * 商品分类管理
*/
import request from '@/utils/request'

export const apis = {
  getTrees(params) {
    return request({
      url: `/productCategory/tree`,
      method: 'get',
      params
    })
  },
  getList(parentCode = 0) {
    return request({
      url: `/productCategory/list/${parentCode}`,
      method: 'get',
    })
  },
  // 通过商品类目查询附件
  getDetail(productCategoryCode = 0) {
    return request({
      url: `/attachmentType/list/${productCategoryCode}`,
      method: 'get',
    })
  },
  // 配置品类所属附件类型
  update({ productCategoryCode, strings }) {
    return request({
      url: `/attachmentType/updateBelong/${productCategoryCode}`,
      method: 'post',
      data: strings
    })
  },
}
export default {
  ...apis
}
