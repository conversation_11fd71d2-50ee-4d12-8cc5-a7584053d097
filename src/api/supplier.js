/**
 * 供应厂商
*/
import request from '@/utils/request'

export const supplierApis = {
  getPager(data) {
    return request({
      url: `/baseinfo/supplier/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/baseinfo/supplier/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/baseinfo/supplier/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    data.establishmentTime && (data.establishmentTime = new Date(data.establishmentTime).getTime())
    return request({
      url: `/baseinfo/supplier/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    data.establishmentTime && (data.establishmentTime = new Date(data.establishmentTime).getTime())
    return request({
      url: `/baseinfo/supplier/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/baseinfo/supplier/delete/${no}`,
      method: 'delete'
    })
  }
}

// 厂商商务信息分页
export const supplierBusinessApis = {
  getPager(data) {
    return request({
      url: `/datainfo/supplierBusinessInformation/page`,
      method: 'post',
      data
    })
  },
  // 添加厂商商务信息
  add(data) {
    return request({
      url: `/datainfo/supplierBusinessInformation/save`,
      method: 'post',
      data
    })
  }
}

// 厂商联系人
export const supplierLinkManApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/supplierEmployee/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/supplierEmployee/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/supplierEmployee/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/supplierEmployee/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/supplierEmployee/delete`,
      method: 'delete',
      data
    })
  }
}
