/**
 * 发票
*/
import request from '@/utils/request'

export const collectionApis = {
  getPager(data) {
    return request({
      url: `/salesInvoice/page`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/salesInvoice/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/salesInvoice/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/salesInvoice/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/salesInvoice/delete/${no}`,
      method: 'post'
    })
  },
  // 提交
  submit(id) {
    return request({
      url: `/salesInvoice/submit/${id}`,
      method: 'post',
    })
  },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/salesInvoice/businessAudit`,
      method: 'post',
      data
    })
  },
}
export default {
  ...collectionApis
}
