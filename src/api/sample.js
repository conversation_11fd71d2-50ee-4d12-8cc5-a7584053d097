/**
 * 样品管理
*/
import request from '@/utils/request'

export const sampleApis = {
  getPager(data) {
    return request({
      url: `/sample/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/sample/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/sample/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/sample/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/sample/update`,
      method: 'post',
      data
    })
  },
  // 提交
  submit(id) {
    return request({
      url: `/sample/submit/${id}`,
      method: 'post',
    })
  },
  // 样品审核
  sampleAudit(data) {
    return request({
      url: `/sample/sampleAudit`,
      method: 'post',
      data
    })
  },
  // 运营审核
  operationsAudit(data) {
    return request({
      url: `/sample/operationsAudit`,
      method: 'post',
      data
    })
  },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/sample/businessAudit`,
      method: 'post',
      data
    })
  },
  // 通知补寄
  resend(no) {
    return request({
      url: `/sample/resend/${no}`,
      method: 'post'
    })
  },
  // 签收
  receipt(no) {
    return request({
      url: `/sample/receipt/${no}`,
      method: 'post'
    })
  },
  // 新增补寄
  saveResend(data) {
    return request({
      url: `/sample/saveResend`,
      method: 'post',
      data
    })
  },
}
export default {
  ...sampleApis
}
