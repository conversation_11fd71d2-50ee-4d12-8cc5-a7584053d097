/**
 * 直播管理
*/
import request from '@/utils/request'

export const contractApis = {
  getPager(data) {
    return request({
      url: `/baseinfo/assm/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/baseinfo/assm/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/baseinfo/assm/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/baseinfo/assm/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/baseinfo/assm/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/baseinfo/assm/delete/${no}`,
      method: 'delete'
    })
  }// ,
  // manageState(data) {
  //   return request({
  //     url: `/baseinfo/assm/updateEnableFlag`,
  //     method: 'post',
  //     data
  //   })
  // }
}
export default {
  ...contractApis
}
