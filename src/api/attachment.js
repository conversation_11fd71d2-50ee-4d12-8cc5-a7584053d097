/**
 * 附件管理
*/
import request from '@/utils/request'

export const apis = {
  // 安组读取附件
  getTrees(data) {
    return request({
      url: `/attachmentType/groupList`,
      method: 'get',
      data
    })
  },
  // getList(data) {
  //   return request({
  //     url: `/attachmentType/list`,
  //     method: 'post',
  //     data
  //   })
  // },
  getDetail(no) {
    return request({
      url: `/attachmentType/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/attachmentType/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/attachmentType/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/attachmentType/delete/${no}`,
      method: 'delete'
    })
  },
}
export default {
  ...apis
}
