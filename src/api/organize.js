import request from '@/utils/request'

export function findAllTreeSub() {
  return request({
    url: '/organize/findAllTree',
    method: 'get'
  })
}

export function addOrgSub(data) {
  return request({
    url: '/organize/add',
    method: 'post',
    data
  })
}

export function updateOrgSub(data) {
  return request({
    url: '/organize/update',
    method: 'post',
    data
  })
}

export function deleteOrgSub(id) {
  return request({
    url: '/organize/delete/' + id,
    method: 'delete'
  })
}

export function detailOrgSub(id) {
  return request({
    url: '/organize/detail/' + id,
    method: 'get'
  })
}

export function findTreeById(id) {
  return request({
    url: '/organize/findTreeById',
    method: 'get',
    params: { id: id }
  })
}

export function getCurrentUserTopOrganize() {
  return request({
    url: '/organize/getCurrentUserTopOrganize',
    method: 'get'
  })
}
export function findAllList() {
  return request({
    url: '/organize/findAllList',
    method: 'get'
  })
}
export function testReload() {
  return request({
    url: '/account/testReload',
    method: 'post'
  })
}
export function testReloadUser() {
  return request({
    url: '/account/testUserReload',
    method: 'post'
  })
}
