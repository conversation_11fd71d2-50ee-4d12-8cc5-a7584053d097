import request from '@/utils/request'

export function queryByKeywordSub(params) {
  return request({
    url: '/post/queryByKeyword',
    method: 'get',
    params: params
  })
}

export function addSub(data) {
  return request({
    url: '/post/add',
    method: 'post',
    data
  })
}

export function updateSub(data) {
  return request({
    url: '/post/update',
    method: 'post',
    data
  })
}

export function detailSub(id) {
  return request({
    url: '/post/detail/' + id,
    method: 'get'
  })
}

export function deleteSub(id) {
  return request({
    url: '/post/delete/' + id,
    method: 'delete'
  })
}

export function findByOrgId(id) {
  return request({
    url: '/post/findByOrgId/' + id,
    method: 'get'
  })
}

export function findAllEnable() {
  return request({
    url: '/post/findAllEnable/',
    method: 'get'
  })
}
