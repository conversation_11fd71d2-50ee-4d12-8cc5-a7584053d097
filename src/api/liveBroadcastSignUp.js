/**
 * 商品提报
*/
import request from '@/utils/request'

export const apis = {
  getPager(data) {
    return request({
      url: `/registration/prePage`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/registration/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/registration/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/registration/update`,
      method: 'post',
      data
    })
  },
  submit(id) {
    return request({
      url: `/registration/submit/${id}`,
      method: 'post',
    })
  },
  delete(no) {
    return request({
      url: `/registration/delete/${no}`,
      method: 'delete'
    })
  },
  // 商品预提，商务审核
  businessAudit(data) {
    return request({
      url: `/registration/preBusinessAudit`,
      method: 'post',
      data
    })
  },
}
export default {
  ...apis
}
