/**
 * 直播计划
*/
import request from '@/utils/request'

export const apis = {
  getPager(data) {
    return request({
      url: `/liveBroadcastPlan/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/baseinfo/assm/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/liveBroadcastPlan/getDetail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/liveBroadcastPlan/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/liveBroadcastPlan/update`,
      method: 'post',
      data
    })
  },
  submit(no) {
    return request({
      url: `/liveBroadcastPlan/submit/${no}`,
      method: 'post',
    })
  },
  invalid(no) {
    return request({
      url: `/liveBroadcastPlan/invalid/${no}`,
      method: 'post',
    })
  },
  audit(data) {
    return request({
      url: `/liveBroadcastPlan/audit`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return new Promise(resolve => {
      resolve({
        data: 1111
      })
    })
  },
  // 直播计划货盘列表
  getPlanSkuList(no) {
    return request({
      url: `/cargoCombination/list/${no}`,
      method: 'get'
    })
  },
  // 直播计划的邀请列表
  getPlanInviteRegistrationPager(liveBroadcastPlanId) {
    return request({
      url: `/inviteRegistration/list/${liveBroadcastPlanId}`,
      method: 'get',
    })
  },
  // 直播计划的邀请报名详情
  getPlanInviteRegistrationDetail(id) {
    return request({
      url: `/inviteRegistration/getPlanDetail/${id}`,
      method: 'get',
    })
  },
}
export default {
  ...apis
}
