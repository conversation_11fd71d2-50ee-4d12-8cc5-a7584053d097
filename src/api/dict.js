import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/dict/list',
    method: 'post',
    data
  })
}

export function save(data) {
  return request({
    url: '/dict/save',
    method: 'post',
    data
  })
}

export function businessDel(data) {
  return request({
    url: '/dict/delete/business',
    method: 'get',
    params: {
      dictId: data
    }
  })
}

export function businessDelCancel(data) {
  return request({
    url: '/dict/delete/business/cancel',
    method: 'get',
    params: {
      dictId: data
    }
  })
}

export function physicsDel(data) {
  return request({
    url: '/dict/delete/physics',
    method: 'get',
    params: {
      dictId: data
    }
  })
}

export function update(data) {
  return request({
    url: '/dict/update',
    method: 'post',
    data
  })
}

