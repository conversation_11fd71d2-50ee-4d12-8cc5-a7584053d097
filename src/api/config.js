import request from '@/utils/request'

export function searchConf(params) {
  return request({
    url: '/config/search',
    method: 'get',
    params
  })
}

export function saveConf(data) {
  return request({
    url: '/config/save',
    method: 'post',
    data
  })
}

export function updateConf(data) {
  return request({
    url: '/config/update',
    method: 'post',
    data
  })
}

export function deleteConf(code) {
  return request({
    url: '/config/delete/' + code,
    method: 'delete'
  })
}
