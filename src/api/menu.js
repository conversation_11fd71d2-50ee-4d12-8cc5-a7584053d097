import request from '@/utils/request'

export function get(id) {
  return request({
    url: '/permission/get',
    method: 'get',
    params: { id: id }
  })
}

export function insert(data) {
  return request({
    url: '/permission/insert',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/permission/update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: '/permission/del',
    method: 'post',
    data
  })
}

export function findTree(parentId, isMenu) {
  return request({
    url: '/permission/tree',
    method: 'get',
    params: { parentId: parentId, isMenu: isMenu }
  })
}
