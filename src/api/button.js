import request from '@/utils/request'

export function getButton(id) {
  return request({
    url: '/button/get',
    method: 'get',
    params: { id: id }
  })
}

export function insertButton(data) {
  return request({
    url: '/button/insert',
    method: 'post',
    data
  })
}

export function updateButton(data) {
  return request({
    url: '/button/update',
    method: 'post',
    data
  })
}

export function delButton(data) {
  return request({
    url: '/button/del',
    method: 'post',
    data
  })
}

export function findButtonList(data) {
  return request({
    url: '/button/list',
    method: 'post',
    data
  })
}
