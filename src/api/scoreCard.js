import request from '@/utils/request'

export function findPage(data) {
  return request({
    url: 'trident/score/card/page',
    method: 'post',
    data
  })
}

export function findList(data) {
  return request({
    url: 'trident/score/card/list',
    method: 'post',
    data
  })
}

export function getDetail(data) {
  return request({
    url: 'trident/score/card/get',
    method: 'post',
    data
  })
}

export function createScoreCard(data) {
  return request({
    url: 'trident/score/card/create',
    method: 'post',
    data
  })
}

export function updateScoreCard(data) {
  return request({
    url: 'trident/score/card/update',
    method: 'post',
    data
  })
}

export function deleteScoreCard(data) {
  return request({
    url: 'trident/score/card/delete',
    method: 'post',
    data
  })
}

export function copyScoreCard(data) {
  return request({
    url: 'trident/score/card/copy',
    method: 'post',
    data
  })
}

export function revertScoreCard(data) {
  return request({
    url: 'trident/score/card/revert',
    method: 'post',
    data
  })
}

export function scoreCardVersions(data) {
  return request({
    url: 'trident/score/card/versions',
    method: 'post',
    data
  })
}
