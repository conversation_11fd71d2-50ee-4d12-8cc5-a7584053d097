import request from '@/utils/request'

export function uploadFilesSub(data) {
  return request({
    url: '/file/uploadFiles',
    method: 'post',
    data
  })
}

export function uploadSingleFileSub(data) {
  return request({
    url: '/file/upload/single',
    method: 'post',
    data
  })
}

export function detailSub(id) {
  return request({
    url: '/file/detail/' + id,
    method: 'get'
  })
}

// 上传业务附件
export function uploadBusinessFile(data) {
  return request({
    url: '/file/upload',
    method: 'post',
    data
  })
}

// 更新业务附件
export function updateBusinessFile(data) {
  return request({
    url: '/file/update',
    method: 'post',
    data
  })
}

/**
 * 查询历史附件
 * @param type
 *    registration 报名
 *    sku sku
 *    product 产品
 * @param data
 */
export function findHistoryFile(type, id) {
  return request({
    url: `/${type}/findHistoryFile/${id}`,
    method: 'get',
  })
}
