import request from '@/utils/request'

export function get(id) {
  return request({
    url: '/role/get',
    method: 'get',
    params: { id: id }
  })
}

export function insert(data) {
  return request({
    url: '/role/insert',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data
  })
}

export function builder(data) {
  return request({
    url: '/role/build',
    method: 'post',
    data
  })
}

export function builderUser(data) {
  return request({
    url: '/role/build/user',
    method: 'post',
    data
  })
}

export function removerUser(data) {
  return request({
    url: '/role/remove/user',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: '/role/del',
    method: 'post',
    data
  })
}

export function findPage(data) {
  return request({
    url: '/role/find',
    method: 'post',
    data
  })
}

export function findList() {
  return request({
    url: '/role/findAll',
    method: 'get'
  })
}
