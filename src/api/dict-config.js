
/**
 * 公共枚举属性配置
*/

// IoT信号输出方式
export const signalOutputList = [
  { no: '模拟量输出', name: '模拟量输出' },
  { no: '数字量输出', name: '数字量输出' },
  { no: '开关量输出', name: '开关量输出' }
]

// IoT通信方式
export const commTypeList = [
  { no: '232', name: '232' },
  { no: '485', name: '485' },
  { no: 'Modbus', name: 'Modbus' },
  { no: 'DP', name: 'DP' }
]

// 属性值表达
export const expressionList = [
  { no: '文本', name: '文本' },
  { no: '数值', name: '数值' },
  { no: '区间值', name: '区间值' }
]

// 告警动作
export const warningActionList = [
  { no: '记录告警', name: '记录告警' },
  { no: '触发标记', name: '触发标记' },
  { no: '触发通知单', name: '触发通知单' },
  { no: '触发故障', name: '触发故障' }
]

// ABC分类
export const abcTypeList = [
  { no: '重要', name: '重要' },
  { no: '一般', name: '一般' },
  { no: '次要', name: '次要' }
]

// 值的分类
export const valueClassificationList = [
  { no: '绝对值', name: '绝对值' },
  { no: '比例值', name: '比例值' }
]

// 故障码-问题等级
export const questionLevelList = [
  { no: '一般', name: '一般' },
  { no: '严重', name: '严重' },
  { no: '紧急', name: '紧急' }
]

// 故障码-故障等级
export const faultLevelList = [
  { no: '一般', name: '一般' },
  { no: '严重', name: '严重' }
]

// 故障码-行动重要性
export const faultActionImportanceList = [
  { no: '一般', name: '一般' },
  { no: '重要', name: '重要' }
]

// 故障码-解决问题程度
export const faultSolveProblemLevelList = [
  { no: '可保持', name: '可保持' },
  { no: '不可保持', name: '不可保持' }
]

// 标准工单模板-危险等级
export const workOrderDangerLevelList = [
  { no: '一般', name: '一般' },
  { no: '危险', name: '危险' }
]

// 标准工单模板-重要等级
export const workOrderImportanceLevelList = [
  { no: '一般', name: '一般' },
  { no: '重要', name: '重要' }
]

// 标准工单模板-物料优先级
export const workOrderMaterialPriorityList = [
  { no: '低', name: '低' },
  { no: '中', name: '中' },
  { no: '高', name: '高' }
]

// 通知单-失控严重性
export const notificationOutOfControlSeriousnessList = [
  { no: '低', name: '低' },
  { no: '中', name: '中' },
  { no: '高', name: '高' }
]
