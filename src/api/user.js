import request from '@/utils/request'
// 获取图形验证码
export function getVerifyCode(account) {
  return `${process.env.VUE_APP_BASE_API}/merchantAccount/getCode/${account}?n=${new Date().getTime()}`
  // return request({
  //   url: `/merchantAccount/getCode/${account}`,
  //   method: 'post',
  // })
}

// 校验手机号
export function verifyAccount(data) {
  return new Promise((resolve) => {
    resolve({
      code: 200
    })
  })
  // return request({
  //   url: `/merchantAccount/verifyAccount`,
  //   method: 'post',
  //   data
  // })
}

// 注册
export function register(data) {
  return request({
    url: `/merchantAccount/register`,
    method: 'post',
    data
  })
}
// 找回密码
export function findPwd(data) {
  return request({
    url: `/merchantAccount/forgotPwd`,
    method: 'post',
    data
  })
}
// 发送注册短信
export function sendRegisterCode(account) {
  return request({
    url: `/merchantAccount/sendRegisterCode/${account}`,
    method: 'get',
  })
}
// 发送登录短信
export function sendLoginCode(account) {
  return request({
    url: `/merchantAccount/sendLoginCode/${account}`,
    method: 'get',
  })
}
// 发送找回密码短信
export function sendForgotPwdCode(account) {
  return request({
    url: `/merchantAccount/sendForgotPwdCode/${account}`,
    method: 'get',
  })
}
export function login(data) {
  return request({
    url: '/merchantAccount/login',
    method: 'post',
    data
  })
}
export function logout() {
  return request({
    url: '/merchantAccount/logout',
    method: 'post'
  })
}

export function updatePwd(data) {
  return request({
    url: '/merchantAccount/updatePwd',
    method: 'post',
    data
  })
}

export function getCurrentUserInfo() {
  return request({
    url: '/merchant/detail',
    method: 'get'
  })
}

export function selectList(params) {
  return request({
    url: '/user/selectList',
    method: 'get',
    params: params
  })
}

export function getEnableUserListByName(data) {
  return request({
    url: '/user/getEnableUserListByName',
    method: 'get',
    params: {
      name: data
    }
  })
}

export function userList(userQuery) {
  return request({
    url: '/user/list',
    method: 'get',
    params: userQuery
  })
}

export function userListByRole(userQuery) {
  return request({
    url: '/user/role/list',
    method: 'get',
    params: userQuery
  })
}

export function getUserDetail(userId) {
  return request({
    url: 'user/getUserInfo/' + userId,
    method: 'get'
  })
}

export function deleteUser(userId) {
  return request({
    url: '/user/delete/' + userId,
    method: 'post'
  })
}

export function enableUser(userId) {
  return request({
    url: '/user/enable/' + userId,
    method: 'post'
  })
}

export function disableUser(userId) {
  return request({
    url: '/user/disable/' + userId,
    method: 'post'
  })
}

export function updateUserInfo(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  })
}

export function saveUserInfo(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}

export function findListWhenSelect(userQuery) {
  return request({
    url: '/user/v2/selectList',
    method: 'get',
    params: userQuery
  })
}

export function getUserListByOrganizeId(data) {
  return request({
    url: '/user/getUserListByOrganizeId',
    method: 'post',
    data
  })
}
