import request from '@/utils/request'

export function pageByKeywordSub(params) {
  return request({
    url: '/codeRule/pageByKeyword',
    method: 'get',
    params: params
  })
}

export function addCodeSub(data) {
  return request({
    url: '/codeRule/add',
    method: 'post',
    data
  })
}

export function updateCodeSub(data) {
  return request({
    url: '/codeRule/update',
    method: 'post',
    data
  })
}

export function codeDetailSub(id) {
  return request({
    url: '/codeRule/detail/' + id,
    method: 'get'
  })
}

export function deleteSub(id) {
  return request({
    url: '/codeRule/delete/' + id,
    method: 'delete'
  })
}

export function changeEnableSub(data) {
  return request({
    url: '/codeRule/changeEnable',
    method: 'post',
    data
  })
}

export function getNewestNoByCodeSub(param) {
  return request({
    url: '/codeRule/getNewestNoByCode',
    method: 'get',
    params: param
  })
}
