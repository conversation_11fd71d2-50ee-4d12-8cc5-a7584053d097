/**
 * 销售合同管理
*/
import request from '@/utils/request'

export const apis = {
  getPager(data) {
    return request({
      url: `/salesContract/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/salesContract/list`,
      method: 'post',
      data
    })
  },
  getDetail(code) {
    return request({
      url: `/salesContract/detail/${code}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/salesContract/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/salesContract/update`,
      method: 'post',
      data
    })
  },
  // 提交
  submit(code) {
    return request({
      url: `/salesContract/submit/${code}`,
      method: 'post',
    })
  },
  // 作废
  invalid(code) {
    return request({
      url: `/salesContract/invalid/${code}`,
      method: 'post'
    })
  },
  // 合同审核
  contractAudit(data) {
    return request({
      url: `/salesContract/contractAudit`,
      method: 'post',
      data
    })
  },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/salesContract/businessAudit`,
      method: 'post',
      data
    })
  },
  // 读取业务公司列表（乙方）
  getBussinessCompanyList(data) {
    return request({
      url: `/businessCompany/list`,
      method: 'post',
      data
    })
  },
  // 法务审核
  legalAffairsAudit(data) {
    return request({
      url: `/salesContract/legalAffairsAudit`,
      method: 'post',
      data
    })
  },
  // 冻结保证金
  frozenMargin(id) {
    return request({
      url: `/salesContract/freeze/margin/${id}`,
      method: 'post',
    })
  },
  // 解冻保证金
  unfreezeMargin(id) {
    return request({
      url: `/salesContract/unfreeze/margin/${id}`,
      method: 'post',
    })
  },
}
export default {
  ...apis
}
