/**
 * 我的货盘表
*/
import request from '@/utils/request'

export const apis = {
  // 我的货盘表
  getPager(data) {
    return request({
      url: `/cargoCombination/minePage`,
      method: 'post',
      data
    })
  },
  // 我的货盘表SKU分页数据
  getPalletSkuPager(data) {
    return request({
      url: `/cargoCombination/mineSkuPage`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/baseinfo/assm/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/cargoCombination/mineDetail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/cargoCombination/saveMine`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/cargoCombination/updateMine`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/cargoCombination/delete/${no}`,
      method: 'delete'
    })
  },
  // sku分页列表
  skuPager(data) {
    return request({
      url: `/sku/page`,
      method: 'post',
      data
    })
  },
  // 货盘添加sku
  addPalletSku(data) {
    return request({
      url: `/cargoCombination/addSku`,
      method: 'post',
      data
    })
  },
  // 货盘批量添加sku
  batchAddSkuToPallet(data) {
    return request({
      url: `/cargoCombination/addSkuBatch`,
      method: 'post',
      data
    })
  },
  // 货盘删除sku
  deletePalletSku(data) {
    return request({
      url: `/cargoCombination/deleteSku`,
      method: 'post',
      data
    })
  },
}
export default {
  ...apis
}
