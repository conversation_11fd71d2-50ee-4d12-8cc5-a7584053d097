import request from '@/utils/request'

export function findPage(data) {
  return request({
    url: 'trident/decision/flow/page',
    method: 'post',
    data
  })
}

export function findList(data) {
  return request({
    url: 'trident/decision/flow/list',
    method: 'post',
    data
  })
}

export function getDetail(data) {
  return request({
    url: 'trident/decision/flow/get',
    method: 'post',
    data
  })
}

export function createDecisionFlow(data) {
  return request({
    url: 'trident/decision/flow/create',
    method: 'post',
    data
  })
}

export function updateDecisionFlow(data) {
  return request({
    url: 'trident/decision/flow/update',
    method: 'post',
    data
  })
}

export function deleteDecisionFlow(data) {
  return request({
    url: 'trident/decision/flow/delete',
    method: 'post',
    data
  })
}

export function copyDecisionFlow(data) {
  return request({
    url: 'trident/decision/flow/copy',
    method: 'post',
    data
  })
}

export function revertDecisionFlow(data) {
  return request({
    url: 'trident/decision/flow/revert',
    method: 'post',
    data
  })
}

export function decisionFlowVersions(data) {
  return request({
    url: 'trident/decision/flow/versions',
    method: 'post',
    data
  })
}
