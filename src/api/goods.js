/**
 * 商品管理
*/
import request from '@/utils/request'

export const productApis = {
  getPager(data) {
    return request({
      url: `/product/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/product/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/product/getDetail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/product/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/product/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/product/delete/${no}`,
      method: 'delete'
    })
  },
}
export default {
  ...productApis
}
