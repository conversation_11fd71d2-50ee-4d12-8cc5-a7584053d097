import request from '@/utils/request'

export function dictValueList(data) {
  return request({
    url: '/dict/value/list',
    method: 'post',
    data
  })
}

export function saveDictValue(data) {
  return request({
    url: '/dict/value/save',
    method: 'post',
    data
  })
}

export function businessDelDictValue(data) {
  return request({
    url: '/dict/value/delete/business',
    method: 'get',
    params: {
      dictValueId: data
    }
  })
}

export function businessDelCancelDictValue(data) {
  return request({
    url: '/dict/value/delete/business/cancel',
    method: 'get',
    params: {
      dictValueId: data
    }
  })
}

export function physicsDelDictValue(data) {
  return request({
    url: '/dict/value/delete/physics',
    method: 'get',
    params: {
      dictValueId: data
    }
  })
}

export function updateDictValue(data) {
  return request({
    url: '/dict/value/update',
    method: 'post',
    data
  })
}

export function findByCodeValue(params) {
  return request({
    url: '/dict/value/findByCode',
    method: 'get',
    params: params
  })
}

