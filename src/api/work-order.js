/**
 * 工单
*/
import request from '@/utils/request'

// 工单相关字典
export const workOrderDictApis = {
  // 处理优先级字典
  getDealPriorityDict(data) {
    return request({
      url: `/wo/priority/level/list`,
      method: 'post',
      data
    }).then(res => {
      res.data.forEach(item => {
        item.no = item.priorityLevelNo
      })
      return res
    })
  },
  // 工单状态字典
  getStatusDict(data) {
    return request({
      url: `/wo/status/list`,
      method: 'post',
      data
    }).then(res => {
      res.data.forEach(item => {
        item.no = item.noticeStatusNo
      })
      return res
    })
  },
  // 工单类型字典
  getTypeDict(data) {
    return request({
      url: `/wo/type/list`,
      method: 'post',
      data
    }).then(res => {
      res.data.forEach(item => {
        item.no = item.noticeTypeNo
      })
      return res
    })
  }
}

function parseWorkerOrderData(data) {
  if (data.workOrder) {
    const workOrder = data.workOrder
    workOrder.contentPlanStime && (workOrder.contentPlanStime = new Date(workOrder.contentPlanStime).getTime())
    workOrder.contentPlanEtime && (workOrder.contentPlanEtime = new Date(workOrder.contentPlanEtime).getTime())
    workOrder.contentActuallyStime && (workOrder.contentActuallyStime = new Date(workOrder.contentActuallyStime).getTime())
    workOrder.contentActuallyEtime && (workOrder.contentActuallyEtime = new Date(workOrder.contentActuallyEtime).getTime())
    workOrder.originatorTime && (workOrder.originatorTime = new Date(workOrder.originatorTime).getTime())
  }
  if (data.wocheckClearing) {
    const wocheckClearing = data.wocheckClearing
    wocheckClearing.checkTime && (wocheckClearing.checkTime = new Date(wocheckClearing.checkTime).getTime())
  }
  // 工作准备交底
  data.wodisclosures && data.wodisclosures.map(item => {
    item.disclosureTime && (item.disclosureTime = new Date(item.disclosureTime).getTime())
  })
  // 工序
  data.woworkProcedures && data.woworkProcedures.map(item => {
    item.planStime && (item.planStime = new Date(item.planStime).getTime())
    item.planEtime && (item.planEtime = new Date(item.planEtime).getTime())
    item.factStime && (item.factStime = new Date(item.factStime).getTime())
    item.factEtime && (item.factEtime = new Date(item.factEtime).getTime())
    item.wooperatorList && item.wooperatorList.map(it => {
      it.planEtime && (it.planEtime = new Date(it.planEtime).getTime())
      it.planStime && (it.planStime = new Date(it.planStime).getTime())
      it.factStime && (it.factStime = new Date(it.factStime).getTime())
      it.factEtime && (it.factEtime = new Date(it.factEtime).getTime())
    })
  })
}

// 工单
export const workOrderApis = {
  getPager(data) {
    return request({
      url: `/wo/page`,
      method: 'post',
      data
    })
  },
  // 工单监控
  getMonitorPager(data) {
    return request({
      url: `/wo/page/all`,
      method: 'post',
      data
    })
  },
  // 工序自评
  getSelfAssessmentPager(data) {
    return request({
      url: `/wo/procedure/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/wo/list`,
      method: 'post',
      data
    })
  },
  getDetail(params) {
    return request({
      url: `/wo/detail`,
      method: 'get',
      params
    })
  },
  add(data) {
    parseWorkerOrderData(data)
    return request({
      url: `/wo/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    parseWorkerOrderData(data)
    return request({
      url: `/wo/update`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `/wo/deleteByKey`,
      method: 'delete',
      data
    })
  },
  /**
   * 工单审批操作
   * opName：关闭close/延期delay/删除delete/处理handle/发放provide/挂起suspend/验收woCheck
  */
  approval(opName, data) {
    parseWorkerOrderData(data)
    return request({
      url: `/wo/${opName}`,
      method: 'post',
      data
    })
  },
  // 提交工序自评
  selfAssessment(data) {
    parseWorkerOrderData(data)
    return request({
      url: `/wo/selfAssessment`,
      method: 'post',
      data
    })
  },
  // 保存并发放
  saveAndProvide(data) {
    return request({
      url: `/wo/saveAndProvide`,
      method: 'post',
      data
    })
  }
}

// 工单注释
export const workOrderNotesApis = {
  getPager(data) {
    return request({
      url: `/wo/woannotation/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/wo/woannotation/list`,
      method: 'post',
      data
    })
  },
  getDetail(params) {
    return request({
      url: `/wo/woannotation/detail`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `/wo/woannotation/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/wo/woannotation/update`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `/wo/woannotation/delete`,
      method: 'delete',
      data
    })
  }
}

// 工单审批记录
export const workOrderApprovalRecordApis = {
  getPager(data) {
    return request({
      url: `/wo/wsapproval/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/wo/wsapproval/list`,
      method: 'post',
      data
    })
  },
  getDetail(params) {
    return request({
      url: `/wo/wsapproval/detail`,
      method: 'get',
      params
    })
  },
  delete(data) {
    return request({
      url: `/wo/wsapproval/delete`,
      method: 'delete',
      data
    })
  }
}

// 工单状态记录
export const workOrderStatusRecordApis = {
  getPager(data) {
    return request({
      url: `/wo/record/status/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/wo/record/status/list`,
      method: 'post',
      data
    })
  },
  getDetail(params) {
    return request({
      url: `/wo/record/status/detail`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `/wo/record/status/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/wo/record/status/update`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `/wo/record/status/delete`,
      method: 'delete',
      data
    })
  }
}
