/**
 * 销售收款（商家端的付款）
*/
import request from '@/utils/request'

export const collectionApis = {
  getPager(data) {
    return request({
      url: `/salesReceipts/page`,
      method: 'post',
      data
    })
  },
  getList(data) {
    return request({
      url: `/baseinfo/assm/list`,
      method: 'post',
      data
    })
  },
  getDetail(no) {
    return request({
      url: `/salesReceipts/detail/${no}`,
      method: 'get'
    })
  },
  add(data) {
    return request({
      url: `/salesReceipts/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/salesReceipts/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/salesReceipts/delete/${no}`,
      method: 'post'
    })
  },
  // 提交
  submit(id) {
    return request({
      url: `/salesReceipts/submit/${id}`,
      method: 'post',
    })
  },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/salesReceipts/businessAudit`,
      method: 'post',
      data
    })
  },
}
export default {
  ...collectionApis
}
