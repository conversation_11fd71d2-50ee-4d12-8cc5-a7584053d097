/**
 * 商家管理
*/
import request from '@/utils/request'
import { getCurrentUserInfo } from './user'

export const enterpriseApis = {
  getList(data) {
    return request({
      url: `/merchant/list`,
      method: 'post',
      data
    })
  },
  getDetail: getCurrentUserInfo,
  add(data) {
    return request({
      url: `/merchant/save`,
      method: 'post',
      data
    })
  },
  update(data) {
    return request({
      url: `/merchant/update`,
      method: 'post',
      data
    })
  },
  delete(no) {
    return request({
      url: `/merchant/delete/${no}`,
      method: 'delete'
    })
  },
  audit(data) {
    return request({
      url: `/api/merchant/audit`,
      method: 'post',
      data
    })
  },
}
export default {
  ...enterpriseApis
}
