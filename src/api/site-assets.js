/**
 * 站点资产
*/
import request from '@/utils/request'

// 站点
export const siteApis = {
  // 所有站点
  getPager(data) {
    return request({
      url: `/baseinfo/monitorPostion/page`,
      method: 'post',
      data
    })
  },
  // 当前所属组织所分配的站点
  getMyList(data) {
    return request({
      url: `/wos/relatedPartiesStationDistribute/selectPage`,
      method: 'post',
      data
    })
  }
}

// 站点资产
export const siteAssetsApis = {
  // 资产树
  getTrees(no) {
    return request({
      url: `/datainfo/monitorPostionAssmComposition/tree/${no}`,
      method: 'post'
    })
  },
  add(data) {
    return request({
      url: `/datainfo/monitorPostionAssmComposition/save`,
      method: 'post',
      data
    })
  }
}

// 站点资产基础数据
export const siteAssetsDataApis = {
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostionAssm/detail`,
      method: 'get',
      params
    })
  },
  update(data) {
    data.purchaseDate && (data.purchaseDate = new Date(data.purchaseDate).getTime())
    data.productionDate && (data.productionDate = new Date(data.productionDate).getTime())
    data.warrantyStime && (data.warrantyStime = new Date(data.warrantyStime).getTime())
    data.warrantyEtime && (data.warrantyEtime = new Date(data.warrantyEtime).getTime())
    data.installationTime && (data.installationTime = new Date(data.installationTime).getTime())
    data.acceptanceTime && (data.acceptanceTime = new Date(data.acceptanceTime).getTime())
    data.planRuningEtime && (data.planRuningEtime = new Date(data.planRuningEtime).getTime())
    return request({
      url: `/datainfo/monitorPostionAssm/update`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `/datainfo/monitorPostionAssm/delete`,
      method: 'delete',
      data
    })
  },
  // 资产启用/停用
  manageState(data) {
    data.runingStime && (data.runingStime = new Date(data.runingStime).getTime())
    data.factRuningEtime && (data.factRuningEtime = new Date(data.factRuningEtime).getTime())
    return request({
      url: `/datainfo/monitorPostionAssm/updateRuningFlag`,
      method: 'post',
      data
    })
  }
}

// 站点资产属性信息
export const siteAssetsAttributeApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostionAssmAttribute/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostionAssmAttribute/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/monitorPostionAssmAttribute/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/monitorPostionAssmAttribute/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostionAssmAttribute/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-iot
export const siteAssetsIotApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostion/ioTattributeAndRange/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostion/ioTattributeAndRange/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/monitorPostion/ioTattributeAndRange/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/monitorPostion/ioTattributeAndRange/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostion/ioTattributeAndRange/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-管控目标
export const siteAssetsControlTargetApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityControl/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityControl/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityControl/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityControl/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityControl/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-告警设置
export const siteAssetsAlarmApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityAlarm/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityAlarm/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityAlarm/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityAlarm/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostion/runingQualityAlarm/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-环保强制要求
export const siteAssetsEnvMandatoryApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostion/enviMandatory/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostion/enviMandatory/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    // data.minResponseTime && (data.minResponseTime = new Date(data.minResponseTime).getTime())
    // data.maxResponseTime && (data.maxResponseTime = new Date(data.maxResponseTime).getTime())
    return request({
      url: `/datainfo/monitorPostion/enviMandatory/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    // data.minResponseTime && (data.minResponseTime = new Date(data.minResponseTime).getTime())
    // data.maxResponseTime && (data.maxResponseTime = new Date(data.maxResponseTime).getTime())
    return request({
      url: `/datainfo/monitorPostion/enviMandatory/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostion/enviMandatory/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-资质认证
export const siteAssetsQualificationsApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/qualifications/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/qualifications/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    data.certificationDay && (data.certificationDay = new Date(data.certificationDay).getTime())
    data.stime && (data.stime = new Date(data.stime).getTime())
    data.etime && (data.etime = new Date(data.etime).getTime())
    return request({
      url: `/datainfo/qualifications/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    data.certificationDay && (data.certificationDay = new Date(data.certificationDay).getTime())
    data.stime && (data.stime = new Date(data.stime).getTime())
    data.etime && (data.etime = new Date(data.etime).getTime())
    return request({
      url: `/datainfo/qualifications/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/qualifications/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-环保测项
export const siteAssetsEnvTestApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/assmAttributeRelation/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/assmAttributeRelation/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    data.stime && (data.stime = new Date(data.stime).getTime())
    return request({
      url: `/datainfo/assmAttributeRelation/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    data.stime && (data.stime = new Date(data.stime).getTime())
    return request({
      url: `/datainfo/assmAttributeRelation/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/assmAttributeRelation/delete`,
      method: 'delete',
      data
    })
  }
}

// 站点资产-资产履历
export const siteAssetsAssmResumeApis = {
  // 分页
  getPager(data) {
    return request({
      url: `/datainfo/monitorPostion/assmResume/page`,
      method: 'post',
      data
    })
  },
  // 获取详情
  getDetail(params) {
    return request({
      url: `/datainfo/monitorPostion/assmResume/detail`,
      method: 'get',
      params
    })
  },
  // 添加
  add(data) {
    return request({
      url: `/datainfo/monitorPostion/assmResume/save`,
      method: 'post',
      data
    })
  },
  // 修改
  update(data) {
    return request({
      url: `/datainfo/monitorPostion/assmResume/update`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/datainfo/monitorPostion/assmResume/delete`,
      method: 'delete',
      data
    })
  }
}
