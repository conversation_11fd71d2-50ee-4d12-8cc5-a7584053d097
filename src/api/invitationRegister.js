/**
 * 邀请报名
*/
import request from '@/utils/request'

export const apis = {
  getPager(data) {
    return request({
      url: `/registration/perfectPage`,
      method: 'post',
      data
    })
  },
  // getList(data) {
  //   return request({
  //     url: `/registration/list`,
  //     method: 'post',
  //     data
  //   })
  // },
  getDetail(no) {
    return request({
      url: `/registration/detail/${no}`,
      method: 'get'
    })
  },
  // add(data) {
  //   return request({
  //     url: `/registration/save`,
  //     method: 'post',
  //     data
  //   })
  // },
  update(data) {
    return request({
      url: `/registration/perfectUpdate`,
      method: 'post',
      data
    })
  },
  submit(id) {
    return request({
      url: `/registration/perfectSubmit/${id}`,
      method: 'post',
    })
  },
  // delete(no) {
  //   return request({
  //     url: `/registration/delete/${no}`,
  //     method: 'delete'
  //   })
  // },
  // 商务审核
  businessAudit(data) {
    return request({
      url: `/registration/businessAudit`,
      method: 'post',
      data
    })
  },
  // 运营审核
  operationsAudit(data) {
    return request({
      url: `/registration/operationsAudit`,
      method: 'post',
      data
    })
  },
  // 合规审核
  complianceAudit(data) {
    return request({
      url: `/registration/complianceAudit`,
      method: 'post',
      data
    })
  },
  // 客服审核
  customerServiceAudit(data) {
    return request({
      url: `/registration/customerServiceAudit`,
      method: 'post',
      data
    })
  },
}
export default {
  ...apis
}
