/**
 * 通用选择列表混淆
*/
export default {
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    /**
     * 是否可选择（用于弹框选择）
     */
    canChoose: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 限选数量，0表示不限
    limit: {
      type: Number,
      default: 0
    },
    // 行样式
    tableRowClassNameFn: {
      type: Function,
      default: () => ''
    },
    // 初始化的搜索参数
    initSearchParams: {
      type: [Object, null],
      default: () => (null)
    },
    initSelectItems: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      rowKey: 'id', // 行的唯一标识
      name: (this.$route.meta.title || '').replace('管理', ''),
      selectItems: []
    }
  },
  computed: {
  },
  created() {
    if (this.initSearchParams) {
      this.searchForm = { ...this.defaultSearchForm, ...this.initSearchParams }
    }
    this.defaultSearchForm = { ...this.searchForm }
  },
  mounted() {
    if (this.initSelectItems) {
      this.selectItems = [...this.initSelectItems]
    }
  },
  methods: {
    getSelectedRowIndex(row) {
      return this.selectItems.findIndex(item => item[this.rowKey] === row[this.rowKey])
    },
    rowClick(row) {
      if (!this.canChoose) {
        return
      }
      // console.log('rowClick')
      const index = this.getSelectedRowIndex(row)
      if (!this.multiple) { // 单选
        this.selectItems.splice(0, this.selectItems.length, row)
      } else {
        if (this.limit > 0 && this.selectItems.length >= this.limit) {
          this.$message.error('最多只能选择' + this.limit + '项')
        } else {
          this.$refs.tableRef.toggleRowSelection(row, index === -1)
          // this.selectItems.push(row)
        }
      }
    },
    rowDblClick(row) {
      if (!this.canChoose) {
        return
      }
      if (!this.multiple) {
        // this.selectItems = [row]
        this.$emit('confirm')
      }
    },
    tableRowClassName({ row, rowIndex }) {
      const index = this.getSelectedRowIndex(row)
      if (index > -1) {
        return 'chosed-row'
      }
      return ''
    },
    handleSelectionChange(val) {
      this.selectItems = val
      // console.log(this.selectItems)
    }
  }
}
