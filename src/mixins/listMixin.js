/**
 * 列表混淆
*/
import dialogListMixin from './dialogListMixin'
export default {
  mixins: [dialogListMixin],
  data() {
    return {
      name: (this.$route.meta.title || '').replace('管理', ''),
      isLoading: false,
      defaultSearchForm: {},
      pageSet: {
        pageIndex: 1, // 当前页
        pageSize: 10, // 每页多少条
        total: 0 // 共多少条数据
      },
      oprateDialog: {
        title: '',
        type: 'new',
        isShow: false,
        data: null
      },
      tableData: [],
      registerRefresh: true, // 是否注册刷新监听
    }
  },
  computed: {
  },
  created() {
    this.defaultSearchForm = { ...this.searchForm }
  },
  mounted() {
    if (this.registerRefresh && this.$isAdmin) {
      const refreshName = (this.$route.meta && this.$route.meta.refreshName) ? this.$route.meta.refreshName : this.$route.name
      // console.log(`refresh-page-${this.$route.name}`)
      this.$eventBus.$on(`refresh-page-${refreshName}`, this.pullData)
    }
  },
  unmounted() {
    if (this.registerRefresh && this.$isAdmin) {
      const refreshName = (this.$route.meta && this.$route.meta.refreshName) ? this.$route.meta.refreshName : this.$route.name
      this.$eventBus.$off(`refresh-page-${refreshName}`, this.pullData)
    }
  },
  methods: {
    beforePullData() {
    },
    async pullData() {
      this.beforePullData && this.beforePullData()
      this.isLoading = true
      const response = await this.apis.getPager({
        ...this.searchForm,
        ...this.pageSet
      })
      this.isLoading = false
      if (response.data) {
        this.tableData = response.data.list
        this.pageSet.total = response.data.total
        this.pageSet.pageIndex = response.data.pageIndex
        this.pageSet.pageSize = response.data.pageSize
      }
      return response
    },
    saveHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.oprateDialog.type === 'edit') {
            this.apis.update(this.formData).then(response => {
              this.$message.success('保存成功')
              this.saveCallback()
            })
          } else {
            this.apis.add(this.formData).then(response => {
              this.$message.success('保存成功')
              this.saveCallback()
            })
          }
        }
      })
    },
    // // 补全数字-前面补零
    // handelBlurNo(digit, code = 'no') {
    //   var no = this.formData[code]
    //   if (no) {
    //     no = no.toString()
    //     this.formData[code] =
    //       no.length < digit
    //         ? no.padStart(digit, '0')
    //         : no
    //   }
    // },
    saveCallback() {
      this.oprateDialog.isShow = !this.oprateDialog.isShow
      this.pullData()
    },
    addHandle() {
      this.formData = Object.assign({}, this.defaultFormData)
      this.oprateDialog.title = `新增${this.name}`
      this.oprateDialog.type = 'new'
      this.oprateDialog.isShow = true
      this.oprateDialog.data = null
    },
    editHandle(row) {
      this.oprateDialog.title = `编辑${this.name}`
      this.oprateDialog.type = 'edit'
      this.oprateDialog.isShow = true
      this.oprateDialog.data = { ...row }
      this.formData = { ...row }
    },
    // 删除弹框
    deleteHandle(no) {
      this.$confirm('你确定删除该条数据，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.apis.delete(no).then(reponse => {
          if (reponse.code === 200) {
            this.$message({
              showClose: true,
              type: 'success',
              message: '删除成功!'
            })
            this.pullData()
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: reponse.msg
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 搜索
    submitSearch() {
      this.pageSet.pageIndex = 1
      this.pullData()
    },
    // 重置搜索
    resetSearch() {
      this.searchForm = { ...this.defaultSearchForm }
      this.$refs.searchForm.resetFields()
      this.pullData()
      // console.log('resetSearch')
    },
  }
}
