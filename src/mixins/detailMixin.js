/**
 * 详情页（或者详情弹框）混淆
*/
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 弹框类型：'new' | 'edit'
    dialogType: {
      type: String,
      default: 'new'
    },
    // 初始化数据
    dialogData: {
      type: Object,
      default: () => ({
      })
    }
  },
  data() {
    return {
      // lastQuery: null,
      isLoading: false,
      isSubmiting: false,
      originFormData: {},
      formData: {},
      apis: {
        add: null,
        update: null,
        getDetail: null
      }
    }
  },
  computed: {
    isAdd() {
      return !(this.dialogType === 'edit' || this.$route.query.id)
    },
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     // console.log('---', vm.lastQuery, JSON.stringify(vm.$route.query))
  //     if (!vm.lastQuery) {
  //       // console.log('第一次进入')
  //     } else if (vm.lastQuery !== JSON.stringify(vm.$route.query)) {
  //       // console.log('route改变')
  //       vm.pullData()
  //     } else {
  //       // console.log('query未改变')
  //     }
  //     setTimeout(() => {
  //       vm.lastQuery = JSON.stringify(vm.$route.query)
  //     }, 200)
  //   })
  // },
  // watch: {
  //   '$route': {
  //     // deep: true,
  //     handler(newVal) {
  //       console.log('route change', newVal)
  //     }
  //   }
  // },
  mounted() {
    // const pageName = this.$route.name.replace('Detail', '').replace('Add', '')
    // console.log('---', `refresh-page-${pageName}`)
  },
  methods: {
    pullData() {
      const id = this.formData.id || this.$route.query.id
      if (!id) {
        return
      }
      this.isLoading = true
      return this.apis.getDetail(id).then(response => {
        this.originFormData = { ...this.originFormData, ...response.data }
        this.formData = { ...response.data }
      }).finally(() => {
        this.isLoading = false
      })
    },
    freshListPage(pageName) {
      if (!pageName) {
        pageName = this.$route.name.replace('Detail', '').replace('Add', '')
      }
      // console.log('freshListPage', `refresh-page-${pageName}`)
      this.$eventBus.$emit(`refresh-page-${pageName}`)
    },
    // 提交前校验
    async validate() {
      // return new Promise(resolve => {
      //   this.$refs.form.validate(valid => resolve(valid))
      // })
      return new Promise((resolve, reject) => {
        resolve(true)
      })
    },
    async saveHandle() {
      const valid = await this.validate()
      if (!valid) {
        return false
      }
      return new Promise(resolve => {
        const api = this.isAdd ? this.apis.add : this.apis.update
        this.isSubmiting = true
        api(this.formData).then(res => {
          this.$message.success('保存成功')
          this.$emit('success', res.data)
          this.$eventBus.$emit('submit-attachment')
          this.freshListPage()
          if (res.data && res.data.id) {
            this.formData.id = res.data.id
          }
          this.close()
          resolve(true)
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    invalidHandle() {
      this.$confirm('确定作废?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        this.apis.invalid(this.formData.id).then(res => {
          this.$message.success('作废成功')
          this.pullData()
          this.freshListPage()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    deleteHandle() {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        this.apis.delete(this.formData.id).then(res => {
          this.$message.success('删除成功')
          this.close(true)
          this.freshListPage()
        }).finally(() => {
          this.isSubmiting = false
        })
      })
    },
    async submitHandle() {
      const valid = await this.validate()
      if (!valid) {
        return
      }
      this.$confirm('确定提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.isSubmiting = true
        const saveApi = this.isAdd ? this.apis.add : this.apis.update
        saveApi(this.formData).then((res) => {
          if (!this.formData.id && res.data && res.data.id) {
            this.formData.id = res.data.id
          }
          this.apis.submit(this.formData.id).then(res => {
            this.$message.success('提交成功')
            // this.$emit('success', res.data)
            this.$eventBus.$emit('submit-attachment')
            this.freshListPage()
            if (this.isAdd) {
              const isGoToDetailPage = this.goToDetailPage()
              if (!isGoToDetailPage) {
                this.$router.back()
              }
            } else {
              this.pullData()
            }
          }).finally(() => {
            this.isSubmiting = false
          })
        })
      })
    },
    close(goBack) {
      if (this.value) {
        this.$emit('input', false)
      } else {
        if (goBack) {
          this.$router.back()
        } else if (this.isAdd) {
          if (this.formData.id) {
            const isGoToDetailPage = this.goToDetailPage()
            if (!isGoToDetailPage) {
              this.$router.back()
            }
          } else {
            this.$router.back()
          }
        }
      }
    },
    goToDetailPage() {
      const obj = {
        path: this.$route.path.replace('-add', '-detail').replace('/add', '/detail'),
        query: { id: this.formData.id }
      }
      const detailRoute = this.$router.resolve(obj).route
      if (detailRoute.path === '/404') {
        return false
      } else {
        this.$store.dispatch('tagsView/delView', this.$route)
        this.$router.replace(obj)
        return true
      }
    },
    // 数据更新后刷新当前页和通知列表页
    dataUpdateCallback() {
      this.freshListPage()
      this.pullData()
    },
  }
}
